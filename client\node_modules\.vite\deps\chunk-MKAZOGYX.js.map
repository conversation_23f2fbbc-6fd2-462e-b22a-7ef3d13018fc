{"version": 3, "sources": ["../../@mui/material/Table/Tablelvl2Context.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst Tablelvl2Context = /*#__PURE__*/React.createContext();\nif (process.env.NODE_ENV !== 'production') {\n  Tablelvl2Context.displayName = 'Tablelvl2Context';\n}\nexport default Tablelvl2Context;"], "mappings": ";;;;;;;;AAAA,YAAuB;AAKvB,IAAM,mBAAsC,oBAAc;AAC1D,IAAI,MAAuC;AACzC,mBAAiB,cAAc;AACjC;AACA,IAAO,2BAAQ;", "names": []}