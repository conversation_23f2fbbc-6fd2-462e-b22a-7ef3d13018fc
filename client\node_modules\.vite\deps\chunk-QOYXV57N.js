import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/utils/esm/getValidReactChildren/getValidReactChildren.js
var React = __toESM(require_react());
function getValidReactChildren(children) {
  return React.Children.toArray(children).filter((child) => React.isValidElement(child));
}

export {
  getValidReactChildren
};
//# sourceMappingURL=chunk-QOYXV57N.js.map
