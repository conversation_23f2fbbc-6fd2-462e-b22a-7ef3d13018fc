import {
  inputBaseClasses_default
} from "./chunk-RYEZ56SV.js";
import {
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-AGTTBKOW.js";
import {
  _extends,
  init_extends
} from "./chunk-Q7CPF5VB.js";

// node_modules/@mui/material/OutlinedInput/outlinedInputClasses.js
init_extends();
function getOutlinedInputUtilityClass(slot) {
  return generateUtilityClass("MuiOutlinedInput", slot);
}
var outlinedInputClasses = _extends({}, inputBaseClasses_default, generateUtilityClasses("MuiOutlinedInput", ["root", "notchedOutline", "input"]));
var outlinedInputClasses_default = outlinedInputClasses;

export {
  getOutlinedInputUtilityClass,
  outlinedInputClasses_default
};
//# sourceMappingURL=chunk-5GNHS7NM.js.map
