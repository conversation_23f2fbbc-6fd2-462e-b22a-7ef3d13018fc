import {
  inputBaseClasses_default
} from "./chunk-RYEZ56SV.js";
import {
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-AGTTBKOW.js";
import {
  _extends,
  init_extends
} from "./chunk-Q7CPF5VB.js";

// node_modules/@mui/material/FilledInput/filledInputClasses.js
init_extends();
function getFilledInputUtilityClass(slot) {
  return generateUtilityClass("MuiFilledInput", slot);
}
var filledInputClasses = _extends({}, inputBaseClasses_default, generateUtilityClasses("MuiFilledInput", ["root", "underline", "input"]));
var filledInputClasses_default = filledInputClasses;

export {
  getFilledInputUtilityClass,
  filledInputClasses_default
};
//# sourceMappingURL=chunk-7VVJ376Q.js.map
