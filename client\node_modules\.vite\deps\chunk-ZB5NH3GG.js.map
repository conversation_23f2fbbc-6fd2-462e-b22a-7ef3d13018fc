{"version": 3, "sources": ["../../@mui/material/Link/Link.js", "../../@mui/material/Link/linkClasses.js", "../../@mui/material/Link/getTextDecoration.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"component\", \"onBlur\", \"onFocus\", \"TypographyClasses\", \"underline\", \"variant\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useForkRef from '../utils/useForkRef';\nimport Typography from '../Typography';\nimport linkClasses, { getLinkUtilityClass } from './linkClasses';\nimport getTextDecoration, { colorTransformations } from './getTextDecoration';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    component,\n    focusVisible,\n    underline\n  } = ownerState;\n  const slots = {\n    root: ['root', `underline${capitalize(underline)}`, component === 'button' && 'button', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getLinkUtilityClass, classes);\n};\nconst LinkRoot = styled(Typography, {\n  name: 'MuiLink',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`underline${capitalize(ownerState.underline)}`], ownerState.component === 'button' && styles.button];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  return _extends({}, ownerState.underline === 'none' && {\n    textDecoration: 'none'\n  }, ownerState.underline === 'hover' && {\n    textDecoration: 'none',\n    '&:hover': {\n      textDecoration: 'underline'\n    }\n  }, ownerState.underline === 'always' && _extends({\n    textDecoration: 'underline'\n  }, ownerState.color !== 'inherit' && {\n    textDecorationColor: getTextDecoration({\n      theme,\n      ownerState\n    })\n  }, {\n    '&:hover': {\n      textDecorationColor: 'inherit'\n    }\n  }), ownerState.component === 'button' && {\n    position: 'relative',\n    WebkitTapHighlightColor: 'transparent',\n    backgroundColor: 'transparent',\n    // Reset default value\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    border: 0,\n    margin: 0,\n    // Remove the margin in Safari\n    borderRadius: 0,\n    padding: 0,\n    // Remove the padding in Firefox\n    cursor: 'pointer',\n    userSelect: 'none',\n    verticalAlign: 'middle',\n    MozAppearance: 'none',\n    // Reset\n    WebkitAppearance: 'none',\n    // Reset\n    '&::-moz-focus-inner': {\n      borderStyle: 'none' // Remove Firefox dotted outline.\n    },\n    [`&.${linkClasses.focusVisible}`]: {\n      outline: 'auto'\n    }\n  });\n});\nconst Link = /*#__PURE__*/React.forwardRef(function Link(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiLink'\n  });\n  const {\n      className,\n      color = 'primary',\n      component = 'a',\n      onBlur,\n      onFocus,\n      TypographyClasses,\n      underline = 'always',\n      variant = 'inherit',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const handlerRef = useForkRef(ref, focusVisibleRef);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    if (onBlur) {\n      onBlur(event);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    focusVisible,\n    underline,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(LinkRoot, _extends({\n    color: color,\n    className: clsx(classes.root, className),\n    classes: TypographyClasses,\n    component: component,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    ref: handlerRef,\n    ownerState: ownerState,\n    variant: variant,\n    sx: [...(!Object.keys(colorTransformations).includes(color) ? [{\n      color\n    }] : []), ...(Array.isArray(sx) ? sx : [sx])]\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Link.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the link.\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.any,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: elementTypeAcceptingRef,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Typography`](/material-ui/api/typography/) element.\n   */\n  TypographyClasses: PropTypes.object,\n  /**\n   * Controls when the link should have an underline.\n   * @default 'always'\n   */\n  underline: PropTypes.oneOf(['always', 'hover', 'none']),\n  /**\n   * Applies the theme typography styles.\n   * @default 'inherit'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string])\n} : void 0;\nexport default Link;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'underlineNone', 'underlineHover', 'underlineAlways', 'button', 'focusVisible']);\nexport default linkClasses;", "import { getPath } from '@mui/system';\nimport { alpha } from '@mui/system/colorManipulator';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AAEA,YAAuB;AACvB,wBAAsB;;;ACJf,SAAS,oBAAoB,MAAM;AACxC,SAAO,qBAAqB,WAAW,IAAI;AAC7C;AACA,IAAM,cAAc,uBAAuB,WAAW,CAAC,QAAQ,iBAAiB,kBAAkB,mBAAmB,UAAU,cAAc,CAAC;AAC9I,IAAO,sBAAQ;;;ACLf,8BAAsB;AACf,IAAM,uBAAuB;AAAA,EAClC,SAAS;AAAA,EACT,aAAa;AAAA,EACb,WAAW;AAAA,EACX,eAAe;AAAA,EACf,OAAO;AACT;AACA,IAAM,4BAA4B,WAAS;AACzC,SAAO,qBAAqB,KAAK,KAAK;AACxC;AACA,IAAM,oBAAoB,CAAC;AAAA,EACzB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,mBAAmB,0BAA0B,WAAW,KAAK;AACnE,QAAM,QAAQ,QAAQ,OAAO,WAAW,gBAAgB,IAAI,KAAK,KAAK,WAAW;AACjF,QAAM,eAAe,QAAQ,OAAO,WAAW,gBAAgB,SAAS;AACxE,MAAI,UAAU,SAAS,cAAc;AACnC,WAAO,QAAQ,YAAY;AAAA,EAC7B;AACA,aAAO,+BAAM,OAAO,GAAG;AACzB;AACA,IAAO,4BAAQ;;;AFNf,yBAA4B;AAd5B,IAAM,YAAY,CAAC,aAAa,SAAS,aAAa,UAAU,WAAW,qBAAqB,aAAa,WAAW,IAAI;AAe5H,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,YAAY,mBAAW,SAAS,CAAC,IAAI,cAAc,YAAY,UAAU,gBAAgB,cAAc;AAAA,EACxH;AACA,SAAO,eAAe,OAAO,qBAAqB,OAAO;AAC3D;AACA,IAAM,WAAW,eAAO,oBAAY;AAAA,EAClC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,YAAY,mBAAW,WAAW,SAAS,CAAC,EAAE,GAAG,WAAW,cAAc,YAAY,OAAO,MAAM;AAAA,EACjI;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM;AACJ,SAAO,SAAS,CAAC,GAAG,WAAW,cAAc,UAAU;AAAA,IACrD,gBAAgB;AAAA,EAClB,GAAG,WAAW,cAAc,WAAW;AAAA,IACrC,gBAAgB;AAAA,IAChB,WAAW;AAAA,MACT,gBAAgB;AAAA,IAClB;AAAA,EACF,GAAG,WAAW,cAAc,YAAY,SAAS;AAAA,IAC/C,gBAAgB;AAAA,EAClB,GAAG,WAAW,UAAU,aAAa;AAAA,IACnC,qBAAqB,0BAAkB;AAAA,MACrC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,GAAG;AAAA,IACD,WAAW;AAAA,MACT,qBAAqB;AAAA,IACvB;AAAA,EACF,CAAC,GAAG,WAAW,cAAc,YAAY;AAAA,IACvC,UAAU;AAAA,IACV,yBAAyB;AAAA,IACzB,iBAAiB;AAAA;AAAA;AAAA,IAGjB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA;AAAA,IAER,cAAc;AAAA,IACd,SAAS;AAAA;AAAA,IAET,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,eAAe;AAAA;AAAA,IAEf,kBAAkB;AAAA;AAAA,IAElB,uBAAuB;AAAA,MACrB,aAAa;AAAA;AAAA,IACf;AAAA,IACA,CAAC,KAAK,oBAAY,YAAY,EAAE,GAAG;AAAA,MACjC,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,OAA0B,iBAAW,SAASA,MAAK,SAAS,KAAK;AACrE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,UAAU;AAAA,IACV;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,KAAK;AAAA,EACP,IAAI,0BAAkB;AACtB,QAAM,CAAC,cAAc,eAAe,IAAU,eAAS,KAAK;AAC5D,QAAM,aAAa,mBAAW,KAAK,eAAe;AAClD,QAAM,aAAa,WAAS;AAC1B,sBAAkB,KAAK;AACvB,QAAI,kBAAkB,YAAY,OAAO;AACvC,sBAAgB,KAAK;AAAA,IACvB;AACA,QAAI,QAAQ;AACV,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,QAAM,cAAc,WAAS;AAC3B,uBAAmB,KAAK;AACxB,QAAI,kBAAkB,YAAY,MAAM;AACtC,sBAAgB,IAAI;AAAA,IACtB;AACA,QAAI,SAAS;AACX,cAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACA,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,UAAU,SAAS;AAAA,IAC1C;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC,SAAS;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA,IAAI,CAAC,GAAI,CAAC,OAAO,KAAK,oBAAoB,EAAE,SAAS,KAAK,IAAI,CAAC;AAAA,MAC7D;AAAA,IACF,CAAC,IAAI,CAAC,GAAI,GAAI,MAAM,QAAQ,EAAE,IAAI,KAAK,CAAC,EAAE,CAAE;AAAA,EAC9C,GAAG,KAAK,CAAC;AACX,CAAC;AACD,OAAwC,KAAK,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9E,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,kBAAAA,QAAgD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvD,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAItJ,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,WAAW,kBAAAA,QAAU,MAAM,CAAC,UAAU,SAAS,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtD,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,SAAS,SAAS,UAAU,WAAW,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,WAAW,YAAY,aAAa,WAAW,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AACtO,IAAI;AACJ,IAAO,eAAQ;", "names": ["Link", "_jsx", "PropTypes"]}