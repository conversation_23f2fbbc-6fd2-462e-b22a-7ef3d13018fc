import React, { useState } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Typography,
  SelectChangeEvent,
  Card,
  CardHeader,
  IconButton,
  Collapse,
  CardContent,
} from '@mui/material';
import { useSnackbar } from '@/components/snackbar';
import FormikDatePickerField from '@/components/formik-form/formik-date-picker';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { updateModelFetchMechanism, refreshKpiValues } from '@/shared/services/kpi.service';
import Iconify from '@/components/iconify';
import { fetchMechanismOptions } from '@/shared/enum/common.enum';
import { FullScreenLoader } from '@/components/loading-screen';

interface OverrideFetchMechanismProps {
  modelId: number;
  modelData?: any; // Add modelData prop
  onUpdate?: () => void;
}

const validationSchema = Yup.object({
  override_fetch_mechanism_type: Yup.string().required('Date range selection is required'),
  pull_from_date: Yup.date().when('override_fetch_mechanism_type', {
    is: 'custom',
    then: (schema) => schema.required('From date is required'),
    otherwise: (schema) => schema.nullable(),
  }),
  pull_to_date: Yup.date().when('override_fetch_mechanism_type', {
    is: 'custom',
    then: (schema) => schema.required('To date is required'),
    otherwise: (schema) => schema.nullable(),
  }),
});

export default function OverrideFetchMechanism({ modelId, modelData, onUpdate }: OverrideFetchMechanismProps) {
  const { enqueueSnackbar } = useSnackbar();
  const [isUpdating, setIsUpdating] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);

  const initialValues = {
    override_fetch_mechanism_type: modelData?.override_fetch_mechanism_type || 'default',
    pull_from_date: modelData?.pull_from_date ? new Date(modelData.pull_from_date) : null,
    pull_to_date: modelData?.pull_to_date ? new Date(modelData.pull_to_date) : null,
  };

  // const toggleExpanded = () =>
  //   initialValues?.override_fetch_mechanism_type && initialValues.override_fetch_mechanism_type !== 'default'
  //     ? setExpanded(!expanded)
  //     : setExpanded(false);

  const toggleExpanded = () => setExpanded(!expanded);

  // if (modelData?.override_fetch_mechanism_type && modelData.override_fetch_mechanism_type !== 'default') {
  //   setExpanded(true);
  // }

  const handleSubmit = async (values: any) => {
    setIsUpdating(true);
    setLoading(true);
    try {
      const payload: any = {
        model_id: modelId,
        override_fetch_mechanism_type: values.override_fetch_mechanism_type,
      };

      if (values.override_fetch_mechanism_type === 'custom') {
        payload.pull_from_date = values.pull_from_date;
        payload.pull_to_date = values.pull_to_date;
      }

      // Update the fetch mechanism
      await updateModelFetchMechanism(payload);

      // Refresh KPI values with the new fetch mechanism
      await refreshKpiValues(modelId);

      enqueueSnackbar('Model values updated successfully', { variant: 'success' });

      if (onUpdate) {
        onUpdate();
      }
    } catch (error) {
      enqueueSnackbar('Failed to update model values', { variant: 'error' });
    } finally {
      setIsUpdating(false);
      setLoading(false);
    }
  };

  return (
    <Box>
      {loading && <FullScreenLoader />}
      <Card sx={{ mb: 2 }}>
        <CardHeader
          title="Data Pull Configuration"
          action={
            <IconButton onClick={toggleExpanded}>
              <Iconify icon={expanded ? 'eva:arrow-ios-upward-fill' : 'eva:arrow-ios-downward-fill'} />
            </IconButton>
          }
          sx={{ cursor: 'pointer', px: 2, py: 1 }}
          onClick={toggleExpanded}
        />
        <Collapse in={expanded}>
          <CardContent sx={{ px: 2, py: 2 }}>
            <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
              {({ values, setFieldValue, errors, touched }) => (
                <Form>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Featch Mechanism</InputLabel>
                        <Select
                          value={values.override_fetch_mechanism_type}
                          label="Featch Mechanism Type"
                          onChange={(e: SelectChangeEvent) => {
                            setFieldValue('override_fetch_mechanism_type', e.target.value);
                            if (e.target.value !== 'custom') {
                              setFieldValue('pull_from_date', null);
                              setFieldValue('pull_to_date', null);
                            }
                          }}
                          error={touched.override_fetch_mechanism_type && Boolean(errors.override_fetch_mechanism_type)}
                        >
                          {fetchMechanismOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    {values.override_fetch_mechanism_type === 'custom' && (
                      <>
                        <Grid item xs={12} md={3}>
                          <FormikDatePickerField
                            name="pull_from_date"
                            label="From Date"
                            required
                            sx={{ width: '100%' }}
                          />
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <FormikDatePickerField name="pull_to_date" label="To Date" required sx={{ width: '100%' }} />
                        </Grid>
                      </>
                    )}

                    <Grid item xs={12}>
                      <Button
                        type="submit"
                        variant="contained"
                        disabled={isUpdating}
                        startIcon={isUpdating ? <Iconify icon="mdi:loading" /> : <Iconify icon="mdi:update" />}
                        sx={{ px: 3, py: 1 }}
                      >
                        Update
                      </Button>
                    </Grid>
                  </Grid>
                </Form>
              )}
            </Formik>
          </CardContent>
        </Collapse>
      </Card>
    </Box>
  );
}
