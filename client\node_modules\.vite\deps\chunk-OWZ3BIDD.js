import {
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-AGTTBKOW.js";

// node_modules/@mui/material/DialogTitle/dialogTitleClasses.js
function getDialogTitleUtilityClass(slot) {
  return generateUtilityClass("MuiDialogTitle", slot);
}
var dialogTitleClasses = generateUtilityClasses("MuiDialogTitle", ["root"]);
var dialogTitleClasses_default = dialogTitleClasses;

export {
  getDialogTitleUtilityClass,
  dialogTitleClasses_default
};
//# sourceMappingURL=chunk-OWZ3BIDD.js.map
