export enum KPI_TYPE_COLORS {
  formula = '#ececec',
  input = '#fff1e0',
  pull_data = '#d9e3f7',
}

export enum KPI_TYPE_LABELS {
  formula = 'Formula',
  input = 'User Input',
  pull_data = 'Pull Data'
} 


export const fetchMechanismOptions = [
  { value: 'default', label: 'Default' },
  { value: 'last_12_month', label: 'Last 12 Rolling Months' },
  { value: 'last_year', label: 'Previous Full Year' },
  { value: 'year_to_date', label: 'Year To Date' },
  { value: 'custom', label: 'Custom Selection' },
]