{"version": 3, "sources": ["../../@azure/msal-react/src/MsalContext.ts", "../../@azure/msal-react/src/utils/utilities.ts", "../../@azure/msal-react/src/packageMetadata.ts", "../../@azure/msal-react/src/MsalProvider.tsx", "../../@azure/msal-react/src/hooks/useMsal.ts", "../../@azure/msal-react/src/hooks/useIsAuthenticated.ts", "../../@azure/msal-react/src/components/AuthenticatedTemplate.tsx", "../../@azure/msal-react/src/components/UnauthenticatedTemplate.tsx", "../../@azure/msal-react/src/hooks/useAccount.ts", "../../@azure/msal-react/src/error/ReactAuthError.ts", "../../@azure/msal-react/src/hooks/useMsalAuthentication.ts", "../../@azure/msal-react/src/components/MsalAuthenticationTemplate.tsx", "../../@azure/msal-react/src/components/withMsal.tsx"], "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport * as React from \"react\";\nimport {\n    IPublicClientApplication,\n    stubbedPublicClientApplication,\n    Logger,\n    InteractionStatus,\n    AccountInfo,\n} from \"@azure/msal-browser\";\n\nexport interface IMsalContext {\n    instance: IPublicClientApplication;\n    inProgress: InteractionStatus;\n    accounts: AccountInfo[];\n    logger: Logger;\n}\n\n/*\n * Stubbed context implementation\n * Only used when there is no provider, which is an unsupported scenario\n */\nconst defaultMsalContext: IMsalContext = {\n    instance: stubbedPublicClientApplication,\n    inProgress: InteractionStatus.None,\n    accounts: [],\n    logger: new Logger({}),\n};\n\nexport const MsalContext =\n    React.createContext<IMsalContext>(defaultMsalContext);\nexport const MsalConsumer = MsalContext.Consumer;\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { AccountIdentifiers } from \"../types/AccountIdentifiers\";\nimport { AccountInfo } from \"@azure/msal-browser\";\n\ntype FaaCFunction = <T>(args: T) => React.ReactNode;\n\nexport function getChildrenOrFunction<T>(\n    children: React.ReactNode | FaaCFunction,\n    args: T\n): React.ReactNode {\n    if (typeof children === \"function\") {\n        return children(args);\n    }\n    return children;\n}\n\n/*\n * Utility types\n * Reference: https://github.com/piotrwitek/utility-types\n */\nexport type SetDifference<A, B> = A extends B ? never : A;\nexport type SetComplement<A, A1 extends A> = SetDifference<A, A1>;\nexport type Subtract<T extends T1, T1 extends object> = Pick<\n    T,\n    SetComplement<keyof T, keyof T1>\n>;\n\n/**\n * Helper function to determine whether 2 arrays are equal\n * Used to avoid unnecessary state updates\n * @param arrayA\n * @param arrayB\n */\nexport function accountArraysAreEqual(\n    arrayA: Array<AccountIdentifiers>,\n    arrayB: Array<AccountIdentifiers>\n): boolean {\n    if (arrayA.length !== arrayB.length) {\n        return false;\n    }\n\n    const comparisonArray = [...arrayB];\n\n    return arrayA.every((elementA) => {\n        const elementB = comparisonArray.shift();\n        if (!elementA || !elementB) {\n            return false;\n        }\n\n        return (\n            elementA.homeAccountId === elementB.homeAccountId &&\n            elementA.localAccountId === elementB.localAccountId &&\n            elementA.username === elementB.username\n        );\n    });\n}\n\nexport function getAccountByIdentifiers(\n    allAccounts: AccountInfo[],\n    accountIdentifiers: AccountIdentifiers\n): AccountInfo | null {\n    if (\n        allAccounts.length > 0 &&\n        (accountIdentifiers.homeAccountId ||\n            accountIdentifiers.localAccountId ||\n            accountIdentifiers.username)\n    ) {\n        const matchedAccounts = allAccounts.filter((accountObj) => {\n            if (\n                accountIdentifiers.username &&\n                accountIdentifiers.username.toLowerCase() !==\n                    accountObj.username.toLowerCase()\n            ) {\n                return false;\n            }\n            if (\n                accountIdentifiers.homeAccountId &&\n                accountIdentifiers.homeAccountId.toLowerCase() !==\n                    accountObj.homeAccountId.toLowerCase()\n            ) {\n                return false;\n            }\n            if (\n                accountIdentifiers.localAccountId &&\n                accountIdentifiers.localAccountId.toLowerCase() !==\n                    accountObj.localAccountId.toLowerCase()\n            ) {\n                return false;\n            }\n\n            return true;\n        });\n\n        return matchedAccounts[0] || null;\n    } else {\n        return null;\n    }\n}\n", "/* eslint-disable header/header */\nexport const name = \"@azure/msal-react\";\nexport const version = \"2.0.22\";\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport React, {\n    useEffect,\n    useReducer,\n    PropsWithChildren,\n    useMemo,\n} from \"react\";\nimport {\n    IPublicClientApplication,\n    EventMessage,\n    EventMessageUtils,\n    InteractionStatus,\n    Logger,\n    WrapperSKU,\n    AccountInfo,\n} from \"@azure/msal-browser\";\nimport { MsalContext, IMsalContext } from \"./MsalContext\";\nimport { accountArraysAreEqual } from \"./utils/utilities\";\nimport { name as SKU, version } from \"./packageMetadata\";\n\nexport type MsalProviderProps = PropsWithChildren<{\n    instance: IPublicClientApplication;\n}>;\n\ntype MsalState = {\n    inProgress: InteractionStatus;\n    accounts: AccountInfo[];\n};\n\nconst MsalProviderActionType = {\n    UNBLOCK_INPROGRESS: \"UNBLOCK_INPROGRESS\",\n    EVENT: \"EVENT\",\n} as const;\ntype MsalProviderActionType =\n    (typeof MsalProviderActionType)[keyof typeof MsalProviderActionType];\n\ntype MsalProviderReducerAction = {\n    type: MsalProviderActionType;\n    payload: {\n        logger: Logger;\n        instance: IPublicClientApplication;\n        message?: EventMessage;\n    };\n};\n\n/**\n * Returns the next inProgress and accounts state based on event message\n * @param previousState\n * @param action\n */\nconst reducer = (\n    previousState: MsalState,\n    action: MsalProviderReducerAction\n): MsalState => {\n    const { type, payload } = action;\n    let newInProgress = previousState.inProgress;\n\n    switch (type) {\n        case MsalProviderActionType.UNBLOCK_INPROGRESS:\n            if (previousState.inProgress === InteractionStatus.Startup) {\n                newInProgress = InteractionStatus.None;\n                payload.logger.info(\n                    \"MsalProvider - handleRedirectPromise resolved, setting inProgress to 'none'\"\n                );\n            }\n            break;\n        case MsalProviderActionType.EVENT:\n            const message = payload.message as EventMessage;\n            const status = EventMessageUtils.getInteractionStatusFromEvent(\n                message,\n                previousState.inProgress\n            );\n            if (status) {\n                payload.logger.info(\n                    `MsalProvider - ${message.eventType} results in setting inProgress from ${previousState.inProgress} to ${status}`\n                );\n                newInProgress = status;\n            }\n            break;\n        default:\n            throw new Error(`Unknown action type: ${type}`);\n    }\n\n    const currentAccounts = payload.instance.getAllAccounts();\n    if (\n        newInProgress !== previousState.inProgress &&\n        !accountArraysAreEqual(currentAccounts, previousState.accounts)\n    ) {\n        // Both inProgress and accounts changed\n        return {\n            ...previousState,\n            inProgress: newInProgress,\n            accounts: currentAccounts,\n        };\n    } else if (newInProgress !== previousState.inProgress) {\n        // Only only inProgress changed\n        return {\n            ...previousState,\n            inProgress: newInProgress,\n        };\n    } else if (\n        !accountArraysAreEqual(currentAccounts, previousState.accounts)\n    ) {\n        // Only accounts changed\n        return {\n            ...previousState,\n            accounts: currentAccounts,\n        };\n    } else {\n        // Nothing changed\n        return previousState;\n    }\n};\n\n/**\n * MSAL context provider component. This must be rendered above any other components that use MSAL.\n */\nexport function MsalProvider({\n    instance,\n    children,\n}: MsalProviderProps): React.ReactElement {\n    useEffect(() => {\n        instance.initializeWrapperLibrary(WrapperSKU.React, version);\n    }, [instance]);\n    // Create a logger instance for msal-react with the same options as PublicClientApplication\n    const logger = useMemo(() => {\n        return instance.getLogger().clone(SKU, version);\n    }, [instance]);\n\n    const [state, updateState] = useReducer(reducer, undefined, () => {\n        // Lazy initialization of the initial state\n        return {\n            inProgress: InteractionStatus.Startup,\n            accounts: instance.getAllAccounts(),\n        };\n    });\n\n    useEffect(() => {\n        const callbackId = instance.addEventCallback(\n            (message: EventMessage) => {\n                updateState({\n                    payload: {\n                        instance,\n                        logger,\n                        message,\n                    },\n                    type: MsalProviderActionType.EVENT,\n                });\n            }\n        );\n        logger.verbose(\n            `MsalProvider - Registered event callback with id: ${callbackId}`\n        );\n\n        instance\n            .initialize()\n            .then(() => {\n                instance\n                    .handleRedirectPromise()\n                    .catch(() => {\n                        // Errors should be handled by listening to the LOGIN_FAILURE event\n                        return;\n                    })\n                    .finally(() => {\n                        /*\n                         * If handleRedirectPromise returns a cached promise the necessary events may not be fired\n                         * This is a fallback to prevent inProgress from getting stuck in 'startup'\n                         */\n                        updateState({\n                            payload: {\n                                instance,\n                                logger,\n                            },\n                            type: MsalProviderActionType.UNBLOCK_INPROGRESS,\n                        });\n                    });\n            })\n            .catch(() => {\n                // Errors should be handled by listening to the LOGIN_FAILURE event\n                return;\n            });\n\n        return () => {\n            // Remove callback when component unmounts or accounts change\n            if (callbackId) {\n                logger.verbose(\n                    `MsalProvider - Removing event callback ${callbackId}`\n                );\n                instance.removeEventCallback(callbackId);\n            }\n        };\n    }, [instance, logger]);\n\n    const contextValue: IMsalContext = {\n        instance,\n        inProgress: state.inProgress,\n        accounts: state.accounts,\n        logger,\n    };\n\n    return (\n        <MsalContext.Provider value={contextValue}>\n            {children}\n        </MsalContext.Provider>\n    );\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { useContext } from \"react\";\nimport { IMsalContext, MsalContext } from \"../MsalContext\";\n\n/**\n * Returns Msal Context values\n */\nexport const useMsal = (): IMsalContext => useContext(MsalContext);\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { useMemo } from \"react\";\nimport { useMsal } from \"./useMsal\";\nimport { AccountIdentifiers } from \"../types/AccountIdentifiers\";\nimport { AccountInfo, InteractionStatus } from \"@azure/msal-browser\";\nimport { getAccountByIdentifiers } from \"../utils/utilities\";\n\nfunction isAuthenticated(\n    allAccounts: AccountInfo[],\n    matchAccount?: AccountIdentifiers\n): boolean {\n    if (\n        matchAccount &&\n        (matchAccount.username ||\n            matchAccount.homeAccountId ||\n            matchAccount.localAccountId)\n    ) {\n        return !!getAccountByIdentifiers(allAccounts, matchAccount);\n    }\n\n    return allAccounts.length > 0;\n}\n\n/**\n * Returns whether or not a user is currently signed-in. Optionally provide 1 or more accountIdentifiers to determine if a specific user is signed-in\n * @param matchAccount\n */\nexport function useIsAuthenticated(matchAccount?: AccountIdentifiers): boolean {\n    const { accounts: allAccounts, inProgress } = useMsal();\n\n    const isUserAuthenticated = useMemo(() => {\n        if (inProgress === InteractionStatus.Startup) {\n            return false;\n        }\n        return isAuthenticated(allAccounts, matchAccount);\n    }, [allAccounts, inProgress, matchAccount]);\n\n    return isUserAuthenticated;\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport React, { PropsWithChildren, useMemo } from \"react\";\nimport { AccountIdentifiers } from \"../types/AccountIdentifiers\";\nimport { getChildrenOrFunction } from \"../utils/utilities\";\nimport { useMsal } from \"../hooks/useMsal\";\nimport { useIsAuthenticated } from \"../hooks/useIsAuthenticated\";\nimport { InteractionStatus } from \"@azure/msal-browser\";\n\nexport type AuthenticatedTemplateProps = PropsWithChildren<AccountIdentifiers>;\n\n/**\n * Renders child components if user is authenticated\n * @param props\n */\nexport function AuthenticatedTemplate({\n    username,\n    homeAccountId,\n    localAccountId,\n    children,\n}: AuthenticatedTemplateProps): React.ReactElement | null {\n    const context = useMsal();\n    const accountIdentifier: AccountIdentifiers = useMemo(() => {\n        return {\n            username,\n            homeAccountId,\n            localAccountId,\n        };\n    }, [username, homeAccountId, localAccountId]);\n    const isAuthenticated = useIsAuthenticated(accountIdentifier);\n\n    if (isAuthenticated && context.inProgress !== InteractionStatus.Startup) {\n        return (\n            <React.Fragment>\n                {getChildrenOrFunction(children, context)}\n            </React.Fragment>\n        );\n    }\n    return null;\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport React, { PropsWithChildren, useMemo } from \"react\";\nimport { useMsal } from \"../hooks/useMsal\";\nimport { useIsAuthenticated } from \"../hooks/useIsAuthenticated\";\nimport { getChildrenOrFunction } from \"../utils/utilities\";\nimport { AccountIdentifiers } from \"../types/AccountIdentifiers\";\nimport { InteractionStatus } from \"@azure/msal-browser\";\n\nexport type UnauthenticatedTemplateProps =\n    PropsWithChildren<AccountIdentifiers>;\n\n/**\n * Renders child components if user is unauthenticated\n * @param props\n */\nexport function UnauthenticatedTemplate({\n    username,\n    homeAccountId,\n    localAccountId,\n    children,\n}: UnauthenticatedTemplateProps): React.ReactElement | null {\n    const context = useMsal();\n    const accountIdentifier: AccountIdentifiers = useMemo(() => {\n        return {\n            username,\n            homeAccountId,\n            localAccountId,\n        };\n    }, [username, homeAccountId, localAccountId]);\n    const isAuthenticated = useIsAuthenticated(accountIdentifier);\n\n    if (\n        !isAuthenticated &&\n        context.inProgress !== InteractionStatus.Startup &&\n        context.inProgress !== InteractionStatus.HandleRedirect\n    ) {\n        return (\n            <React.Fragment>\n                {getChildrenOrFunction(children, context)}\n            </React.Fragment>\n        );\n    }\n    return null;\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { useState, useEffect } from \"react\";\nimport {\n    AccountInfo,\n    IPublicClientApplication,\n    AccountEntity,\n} from \"@azure/msal-browser\";\nimport { useMsal } from \"./useMsal\";\nimport { AccountIdentifiers } from \"../types/AccountIdentifiers\";\nimport { getAccountByIdentifiers } from \"../utils/utilities\";\n\nfunction getAccount(\n    instance: IPublicClientApplication,\n    accountIdentifiers?: AccountIdentifiers\n): AccountInfo | null {\n    if (\n        !accountIdentifiers ||\n        (!accountIdentifiers.homeAccountId &&\n            !accountIdentifiers.localAccountId &&\n            !accountIdentifiers.username)\n    ) {\n        // If no account identifiers are provided, return active account\n        return instance.getActiveAccount();\n    }\n\n    return getAccountByIdentifiers(\n        instance.getAllAccounts(),\n        accountIdentifiers\n    );\n}\n\n/**\n * Given 1 or more accountIdentifiers, returns the Account object if the user is signed-in\n * @param accountIdentifiers\n */\nexport function useAccount(\n    accountIdentifiers?: AccountIdentifiers\n): AccountInfo | null {\n    const { instance, inProgress, logger } = useMsal();\n\n    const [account, setAccount] = useState<AccountInfo | null>(() =>\n        getAccount(instance, accountIdentifiers)\n    );\n\n    useEffect(() => {\n        setAccount((currentAccount: AccountInfo | null) => {\n            const nextAccount = getAccount(instance, accountIdentifiers);\n            if (\n                !AccountEntity.accountInfoIsEqual(\n                    currentAccount,\n                    nextAccount,\n                    true\n                )\n            ) {\n                logger.info(\"useAccount - Updating account\");\n                return nextAccount;\n            }\n\n            return currentAccount;\n        });\n    }, [inProgress, accountIdentifiers, instance, logger]);\n\n    return account;\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { AuthError } from \"@azure/msal-browser\";\n\nexport const ReactAuthErrorMessage = {\n    invalidInteractionType: {\n        code: \"invalid_interaction_type\",\n        desc: \"The provided interaction type is invalid.\",\n    },\n    unableToFallbackToInteraction: {\n        code: \"unable_to_fallback_to_interaction\",\n        desc: \"Interaction is required but another interaction is already in progress. Please try again when the current interaction is complete.\",\n    },\n};\n\nexport class ReactAuthError extends AuthError {\n    constructor(errorCode: string, errorMessage?: string) {\n        super(errorCode, errorMessage);\n\n        Object.setPrototypeOf(this, ReactAuthError.prototype);\n        this.name = \"ReactAuthError\";\n    }\n\n    static createInvalidInteractionTypeError(): ReactAuthError {\n        return new ReactAuthError(\n            ReactAuthErrorMessage.invalidInteractionType.code,\n            ReactAuthErrorMessage.invalidInteractionType.desc\n        );\n    }\n\n    static createUnableToFallbackToInteractionError(): ReactAuthError {\n        return new ReactAuthError(\n            ReactAuthErrorMessage.unableToFallbackToInteraction.code,\n            ReactAuthErrorMessage.unableToFallbackToInteraction.desc\n        );\n    }\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { useCallback, useEffect, useState, useRef } from \"react\";\nimport {\n    PopupRequest,\n    RedirectRequest,\n    SsoSilentRequest,\n    InteractionType,\n    AuthenticationResult,\n    AuthError,\n    EventMessage,\n    EventType,\n    InteractionStatus,\n    SilentRequest,\n    InteractionRequiredAuthError,\n    OIDC_DEFAULT_SCOPES,\n} from \"@azure/msal-browser\";\nimport { useIsAuthenticated } from \"./useIsAuthenticated\";\nimport { AccountIdentifiers } from \"../types/AccountIdentifiers\";\nimport { useMsal } from \"./useMsal\";\nimport { useAccount } from \"./useAccount\";\nimport { ReactAuthError } from \"../error/ReactAuthError\";\n\nexport type MsalAuthenticationResult = {\n    login: (\n        callbackInteractionType?: InteractionType | undefined,\n        callbackRequest?: PopupRequest | RedirectRequest | SilentRequest\n    ) => Promise<AuthenticationResult | null>;\n    acquireToken: (\n        callbackInteractionType?: InteractionType | undefined,\n        callbackRequest?: SilentRequest | undefined\n    ) => Promise<AuthenticationResult | null>;\n    result: AuthenticationResult | null;\n    error: AuthError | null;\n};\n\n/**\n * If a user is not currently signed in this hook invokes a login. Failed logins can be retried using the login callback returned.\n * If a user is currently signed in this hook attempts to acquire a token. Subsequent token requests can use the acquireToken callback returned.\n * Optionally provide a request object to be used in the login/acquireToken call.\n * Optionally provide a specific user that should be logged in.\n * @param interactionType\n * @param authenticationRequest\n * @param accountIdentifiers\n */\nexport function useMsalAuthentication(\n    interactionType: InteractionType,\n    authenticationRequest?: PopupRequest | RedirectRequest | SsoSilentRequest,\n    accountIdentifiers?: AccountIdentifiers\n): MsalAuthenticationResult {\n    const { instance, inProgress, logger } = useMsal();\n    const isAuthenticated = useIsAuthenticated(accountIdentifiers);\n    const account = useAccount(accountIdentifiers);\n    const [[result, error], setResponse] = useState<\n        [AuthenticationResult | null, AuthError | null]\n    >([null, null]);\n\n    // Used to prevent state updates after unmount\n    const mounted = useRef(true);\n    useEffect(() => {\n        return () => {\n            mounted.current = false;\n        };\n    }, []);\n\n    // Boolean used to check if interaction is in progress in acquireTokenSilent fallback. Use Ref instead of state to prevent acquireToken function from being regenerated on each change to interactionInProgress value\n    const interactionInProgress = useRef(inProgress !== InteractionStatus.None);\n    useEffect(() => {\n        interactionInProgress.current = inProgress !== InteractionStatus.None;\n    }, [inProgress]);\n\n    // Flag used to control when the hook calls login/acquireToken\n    const shouldAcquireToken = useRef(true);\n    useEffect(() => {\n        if (!!error) {\n            // Errors should be handled by consuming component\n            shouldAcquireToken.current = false;\n            return;\n        }\n\n        if (!!result) {\n            // Token has already been acquired, consuming component/application is responsible for renewing\n            shouldAcquireToken.current = false;\n            return;\n        }\n    }, [error, result]);\n\n    const login = useCallback(\n        async (\n            callbackInteractionType?: InteractionType,\n            callbackRequest?: PopupRequest | RedirectRequest | SsoSilentRequest\n        ): Promise<AuthenticationResult | null> => {\n            const loginType = callbackInteractionType || interactionType;\n            const loginRequest = callbackRequest || authenticationRequest;\n            switch (loginType) {\n                case InteractionType.Popup:\n                    logger.verbose(\n                        \"useMsalAuthentication - Calling loginPopup\"\n                    );\n                    return instance.loginPopup(loginRequest as PopupRequest);\n                case InteractionType.Redirect:\n                    // This promise is not expected to resolve due to full frame redirect\n                    logger.verbose(\n                        \"useMsalAuthentication - Calling loginRedirect\"\n                    );\n                    return instance\n                        .loginRedirect(loginRequest as RedirectRequest)\n                        .then(null);\n                case InteractionType.Silent:\n                    logger.verbose(\"useMsalAuthentication - Calling ssoSilent\");\n                    return instance.ssoSilent(loginRequest as SsoSilentRequest);\n                default:\n                    throw ReactAuthError.createInvalidInteractionTypeError();\n            }\n        },\n        [instance, interactionType, authenticationRequest, logger]\n    );\n\n    const acquireToken = useCallback(\n        async (\n            callbackInteractionType?: InteractionType,\n            callbackRequest?: SilentRequest\n        ): Promise<AuthenticationResult | null> => {\n            const fallbackInteractionType =\n                callbackInteractionType || interactionType;\n\n            let tokenRequest: SilentRequest;\n\n            if (callbackRequest) {\n                logger.trace(\n                    \"useMsalAuthentication - acquireToken - Using request provided in the callback\"\n                );\n                tokenRequest = {\n                    ...callbackRequest,\n                };\n            } else if (authenticationRequest) {\n                logger.trace(\n                    \"useMsalAuthentication - acquireToken - Using request provided in the hook\"\n                );\n                tokenRequest = {\n                    ...authenticationRequest,\n                    scopes: authenticationRequest.scopes || OIDC_DEFAULT_SCOPES,\n                };\n            } else {\n                logger.trace(\n                    \"useMsalAuthentication - acquireToken - No request object provided, using default request.\"\n                );\n                tokenRequest = {\n                    scopes: OIDC_DEFAULT_SCOPES,\n                };\n            }\n\n            if (!tokenRequest.account && account) {\n                logger.trace(\n                    \"useMsalAuthentication - acquireToken - Attaching account to request\"\n                );\n                tokenRequest.account = account;\n            }\n\n            const getToken = async (): Promise<AuthenticationResult | null> => {\n                logger.verbose(\n                    \"useMsalAuthentication - Calling acquireTokenSilent\"\n                );\n                return instance\n                    .acquireTokenSilent(tokenRequest)\n                    .catch(async (e: AuthError) => {\n                        if (e instanceof InteractionRequiredAuthError) {\n                            if (!interactionInProgress.current) {\n                                logger.error(\n                                    \"useMsalAuthentication - Interaction required, falling back to interaction\"\n                                );\n                                return login(\n                                    fallbackInteractionType,\n                                    tokenRequest\n                                );\n                            } else {\n                                logger.error(\n                                    \"useMsalAuthentication - Interaction required but is already in progress. Please try again, if needed, after interaction completes.\"\n                                );\n                                throw ReactAuthError.createUnableToFallbackToInteractionError();\n                            }\n                        }\n\n                        throw e;\n                    });\n            };\n\n            return getToken()\n                .then((response: AuthenticationResult | null) => {\n                    if (mounted.current) {\n                        setResponse([response, null]);\n                    }\n                    return response;\n                })\n                .catch((e: AuthError) => {\n                    if (mounted.current) {\n                        setResponse([null, e]);\n                    }\n                    throw e;\n                });\n        },\n        [\n            instance,\n            interactionType,\n            authenticationRequest,\n            logger,\n            account,\n            login,\n        ]\n    );\n\n    useEffect(() => {\n        const callbackId = instance.addEventCallback(\n            (message: EventMessage) => {\n                switch (message.eventType) {\n                    case EventType.LOGIN_SUCCESS:\n                    case EventType.SSO_SILENT_SUCCESS:\n                        if (message.payload) {\n                            setResponse([\n                                message.payload as AuthenticationResult,\n                                null,\n                            ]);\n                        }\n                        break;\n                    case EventType.LOGIN_FAILURE:\n                    case EventType.SSO_SILENT_FAILURE:\n                        if (message.error) {\n                            setResponse([null, message.error as AuthError]);\n                        }\n                        break;\n                }\n            }\n        );\n        logger.verbose(\n            `useMsalAuthentication - Registered event callback with id: ${callbackId}`\n        );\n\n        return () => {\n            if (callbackId) {\n                logger.verbose(\n                    `useMsalAuthentication - Removing event callback ${callbackId}`\n                );\n                instance.removeEventCallback(callbackId);\n            }\n        };\n    }, [instance, logger]);\n\n    useEffect(() => {\n        if (\n            shouldAcquireToken.current &&\n            inProgress === InteractionStatus.None\n        ) {\n            shouldAcquireToken.current = false;\n            if (!isAuthenticated) {\n                logger.info(\n                    \"useMsalAuthentication - No user is authenticated, attempting to login\"\n                );\n                login().catch(() => {\n                    // Errors are saved in state above\n                    return;\n                });\n            } else if (account) {\n                logger.info(\n                    \"useMsalAuthentication - User is authenticated, attempting to acquire token\"\n                );\n                acquireToken().catch(() => {\n                    // Errors are saved in state above\n                    return;\n                });\n            }\n        }\n    }, [isAuthenticated, account, inProgress, login, acquireToken, logger]);\n\n    return {\n        login,\n        acquireToken,\n        result,\n        error,\n    };\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport React, { PropsWithChildren, useMemo } from \"react\";\nimport { AccountIdentifiers } from \"../types/AccountIdentifiers\";\nimport { getChildrenOrFunction } from \"../utils/utilities\";\nimport { useMsal } from \"../hooks/useMsal\";\nimport {\n    MsalAuthenticationResult,\n    useMsalAuthentication,\n} from \"../hooks/useMsalAuthentication\";\nimport { useIsAuthenticated } from \"../hooks/useIsAuthenticated\";\nimport {\n    InteractionType,\n    PopupRequest,\n    RedirectRequest,\n    SsoSilentRequest,\n    InteractionStatus,\n} from \"@azure/msal-browser\";\nimport { IMsalContext } from \"../MsalContext\";\n\nexport type MsalAuthenticationProps = PropsWithChildren<\n    AccountIdentifiers & {\n        interactionType: InteractionType;\n        authenticationRequest?:\n            | PopupRequest\n            | RedirectRequest\n            | SsoSilentRequest;\n        loadingComponent?: React.ElementType<IMsalContext>;\n        errorComponent?: React.ElementType<MsalAuthenticationResult>;\n    }\n>;\n\n/**\n * Attempts to authenticate user if not already authenticated, then renders child components\n * @param props\n */\nexport function MsalAuthenticationTemplate({\n    interactionType,\n    username,\n    homeAccountId,\n    localAccountId,\n    authenticationRequest,\n    loadingComponent: LoadingComponent,\n    errorComponent: ErrorComponent,\n    children,\n}: MsalAuthenticationProps): React.ReactElement | null {\n    const accountIdentifier: AccountIdentifiers = useMemo(() => {\n        return {\n            username,\n            homeAccountId,\n            localAccountId,\n        };\n    }, [username, homeAccountId, localAccountId]);\n    const context = useMsal();\n    const msalAuthResult = useMsalAuthentication(\n        interactionType,\n        authenticationRequest,\n        accountIdentifier\n    );\n    const isAuthenticated = useIsAuthenticated(accountIdentifier);\n\n    if (msalAuthResult.error && context.inProgress === InteractionStatus.None) {\n        if (!!ErrorComponent) {\n            return <ErrorComponent {...msalAuthResult} />;\n        }\n\n        throw msalAuthResult.error;\n    }\n\n    if (isAuthenticated) {\n        return (\n            <React.Fragment>\n                {getChildrenOrFunction(children, msalAuthResult)}\n            </React.Fragment>\n        );\n    }\n\n    if (!!LoadingComponent && context.inProgress !== InteractionStatus.None) {\n        return <LoadingComponent {...context} />;\n    }\n\n    return null;\n}\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport React from \"react\";\nimport { IMsalContext } from \"../MsalContext\";\nimport { useMsal } from \"../hooks/useMsal\";\nimport { Subtract } from \"../utils/utilities\";\n\nexport type WithMsalProps = {\n    msalContext: IMsalContext;\n};\n\n/**\n * Higher order component wraps provided component with msal by injecting msal context values into the component's props\n * @param Component\n */\nexport const withMsal = <P extends WithMsalProps>(\n    Component: React.ComponentType<P>\n): React.FunctionComponent<Subtract<P, WithMsalProps>> => {\n    const ComponentWithMsal: React.FunctionComponent<\n        Subtract<P, WithMsalProps>\n    > = (props) => {\n        const msal = useMsal();\n        return <Component {...(props as P)} msalContext={msal} />;\n    };\n\n    const componentName =\n        Component.displayName || Component.name || \"Component\";\n    ComponentWithMsal.displayName = `withMsal(${componentName})`;\n\n    return ComponentWithMsal;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAM,qBAAmC;EACrC,UAAU;EACV,YAAY,kBAAkB;EAC9B,UAAU,CAAA;EACV,QAAQ,IAAI,OAAO,CAAA,CAAE;;AAGZ,IAAA,cACH,oBAA4B,kBAAkB;AAC3C,IAAA,eAAe,YAAY;;;;;;ACxBxB,SAAA,sBACZ,UACA,MAAO;AAEP,MAAI,OAAO,aAAa,YAAY;AAChC,WAAO,SAAS,IAAI;EACvB;AACD,SAAO;AACX;AAmBgB,SAAA,sBACZ,QACA,QAAiC;AAEjC,MAAI,OAAO,WAAW,OAAO,QAAQ;AACjC,WAAO;EACV;AAED,QAAM,kBAAkB,CAAC,GAAG,MAAM;AAElC,SAAO,OAAO,MAAM,CAAC,aAAY;AAC7B,UAAM,WAAW,gBAAgB,MAAK;AACtC,QAAI,CAAC,YAAY,CAAC,UAAU;AACxB,aAAO;IACV;AAED,WACI,SAAS,kBAAkB,SAAS,iBACpC,SAAS,mBAAmB,SAAS,kBACrC,SAAS,aAAa,SAAS;EAEvC,CAAC;AACL;AAEgB,SAAA,wBACZ,aACA,oBAAsC;AAEtC,MACI,YAAY,SAAS,MACpB,mBAAmB,iBAChB,mBAAmB,kBACnB,mBAAmB,WACzB;AACE,UAAM,kBAAkB,YAAY,OAAO,CAAC,eAAc;AACtD,UACI,mBAAmB,YACnB,mBAAmB,SAAS,YAAW,MACnC,WAAW,SAAS,YAAW,GACrC;AACE,eAAO;MACV;AACD,UACI,mBAAmB,iBACnB,mBAAmB,cAAc,YAAW,MACxC,WAAW,cAAc,YAAW,GAC1C;AACE,eAAO;MACV;AACD,UACI,mBAAmB,kBACnB,mBAAmB,eAAe,YAAW,MACzC,WAAW,eAAe,YAAW,GAC3C;AACE,eAAO;MACV;AAED,aAAO;IACX,CAAC;AAED,WAAO,gBAAgB,CAAC,KAAK;EAChC,OAAM;AACH,WAAO;EACV;AACL;;;ACpGO,IAAM,OAAO;AACb,IAAM,UAAU;;;AC+BvB,IAAM,yBAAyB;EAC3B,oBAAoB;EACpB,OAAO;;AAmBX,IAAM,UAAU,CACZ,eACA,WACW;AACX,QAAM,EAAE,MAAM,QAAO,IAAK;AAC1B,MAAI,gBAAgB,cAAc;AAElC,UAAQ,MAAI;IACR,KAAK,uBAAuB;AACxB,UAAI,cAAc,eAAe,kBAAkB,SAAS;AACxD,wBAAgB,kBAAkB;AAClC,gBAAQ,OAAO,KACX,6EAA6E;MAEpF;AACD;IACJ,KAAK,uBAAuB;AACxB,YAAM,UAAU,QAAQ;AACxB,YAAM,SAAS,kBAAkB,8BAC7B,SACA,cAAc,UAAU;AAE5B,UAAI,QAAQ;AACR,gBAAQ,OAAO,KACX,kBAAkB,QAAQ,SAAS,uCAAuC,cAAc,UAAU,OAAO,MAAM,EAAE;AAErH,wBAAgB;MACnB;AACD;IACJ;AACI,YAAM,IAAI,MAAM,wBAAwB,IAAI,EAAE;EACrD;AAED,QAAM,kBAAkB,QAAQ,SAAS,eAAc;AACvD,MACI,kBAAkB,cAAc,cAChC,CAAC,sBAAsB,iBAAiB,cAAc,QAAQ,GAChE;AAEE,WAAO;MACH,GAAG;MACH,YAAY;MACZ,UAAU;;EAEjB,WAAU,kBAAkB,cAAc,YAAY;AAEnD,WAAO;MACH,GAAG;MACH,YAAY;;EAEnB,WACG,CAAC,sBAAsB,iBAAiB,cAAc,QAAQ,GAChE;AAEE,WAAO;MACH,GAAG;MACH,UAAU;;EAEjB,OAAM;AAEH,WAAO;EACV;AACL;SAKgB,aAAa,EACzB,UACA,SAAQ,GACQ;AAChB,8BAAU,MAAK;AACX,aAAS,yBAAyB,WAAW,OAAO,OAAO;EAC/D,GAAG,CAAC,QAAQ,CAAC;AAEb,QAAM,aAAS,sBAAQ,MAAK;AACxB,WAAO,SAAS,UAAS,EAAG,MAAMA,MAAK,OAAO;EAClD,GAAG,CAAC,QAAQ,CAAC;AAEb,QAAM,CAAC,OAAO,WAAW,QAAI,yBAAW,SAAS,QAAW,MAAK;AAE7D,WAAO;MACH,YAAY,kBAAkB;MAC9B,UAAU,SAAS,eAAc;;EAEzC,CAAC;AAED,8BAAU,MAAK;AACX,UAAM,aAAa,SAAS,iBACxB,CAAC,YAAyB;AACtB,kBAAY;QACR,SAAS;UACL;UACA;UACA;QACH;QACD,MAAM,uBAAuB;MAChC,CAAA;IACL,CAAC;AAEL,WAAO,QACH,qDAAqD,UAAU,EAAE;AAGrE,aACK,WAAU,EACV,KAAK,MAAK;AACP,eACK,sBAAqB,EACrB,MAAM,MAAK;AAER;MACJ,CAAC,EACA,QAAQ,MAAK;AAKV,oBAAY;UACR,SAAS;YACL;YACA;UACH;UACD,MAAM,uBAAuB;QAChC,CAAA;MACL,CAAC;IACT,CAAC,EACA,MAAM,MAAK;AAER;IACJ,CAAC;AAEL,WAAO,MAAK;AAER,UAAI,YAAY;AACZ,eAAO,QACH,0CAA0C,UAAU,EAAE;AAE1D,iBAAS,oBAAoB,UAAU;MAC1C;IACL;EACJ,GAAG,CAAC,UAAU,MAAM,CAAC;AAErB,QAAM,eAA6B;IAC/B;IACA,YAAY,MAAM;IAClB,UAAU,MAAM;IAChB;;AAGJ,SACIC,aAAAA,QAAA,cAAC,YAAY,UAAQ,EAAC,OAAO,aAAY,GACpC,QAAQ;AAGrB;;;;;;;ACtMa,IAAA,UAAU,UAAoB,0BAAW,WAAW;;;;ACAjE,SAAS,gBACL,aACA,cAAiC;AAEjC,MACI,iBACC,aAAa,YACV,aAAa,iBACb,aAAa,iBACnB;AACE,WAAO,CAAC,CAAC,wBAAwB,aAAa,YAAY;EAC7D;AAED,SAAO,YAAY,SAAS;AAChC;AAMM,SAAU,mBAAmB,cAAiC;AAChE,QAAM,EAAE,UAAU,aAAa,WAAU,IAAK,QAAO;AAErD,QAAM,0BAAsB,uBAAQ,MAAK;AACrC,QAAI,eAAe,kBAAkB,SAAS;AAC1C,aAAO;IACV;AACD,WAAO,gBAAgB,aAAa,YAAY;KACjD,CAAC,aAAa,YAAY,YAAY,CAAC;AAE1C,SAAO;AACX;;;ACxBM,SAAU,sBAAsB,EAClC,UACA,eACA,gBACA,SAAQ,GACiB;AACzB,QAAM,UAAU,QAAO;AACvB,QAAM,wBAAwC,uBAAQ,MAAK;AACvD,WAAO;MACH;MACA;MACA;;KAEL,CAAC,UAAU,eAAe,cAAc,CAAC;AAC5C,QAAMC,mBAAkB,mBAAmB,iBAAiB;AAE5D,MAAIA,oBAAmB,QAAQ,eAAe,kBAAkB,SAAS;AACrE,WACIC,cAAAA,QAAA,cAACA,cAAAA,QAAM,UACF,MAAA,sBAAsB,UAAU,OAAO,CAAC;EAGpD;AACD,SAAO;AACX;;;;ACvBM,SAAU,wBAAwB,EACpC,UACA,eACA,gBACA,SAAQ,GACmB;AAC3B,QAAM,UAAU,QAAO;AACvB,QAAM,wBAAwC,uBAAQ,MAAK;AACvD,WAAO;MACH;MACA;MACA;;KAEL,CAAC,UAAU,eAAe,cAAc,CAAC;AAC5C,QAAMC,mBAAkB,mBAAmB,iBAAiB;AAE5D,MACI,CAACA,oBACD,QAAQ,eAAe,kBAAkB,WACzC,QAAQ,eAAe,kBAAkB,gBAC3C;AACE,WACIC,cAAAA,QAAA,cAACA,cAAAA,QAAM,UACF,MAAA,sBAAsB,UAAU,OAAO,CAAC;EAGpD;AACD,SAAO;AACX;;;;;;;;;;AChCA,SAAS,WACL,UACA,oBAAuC;AAEvC,MACI,CAAC,sBACA,CAAC,mBAAmB,iBACjB,CAAC,mBAAmB,kBACpB,CAAC,mBAAmB,UAC1B;AAEE,WAAO,SAAS,iBAAgB;EACnC;AAED,SAAO,wBACH,SAAS,eAAc,GACvB,kBAAkB;AAE1B;AAMM,SAAU,WACZ,oBAAuC;AAEvC,QAAM,EAAE,UAAU,YAAY,OAAM,IAAK,QAAO;AAEhD,QAAM,CAAC,SAAS,UAAU,QAAI,wBAA6B,MACvD,WAAW,UAAU,kBAAkB,CAAC;AAG5C,+BAAU,MAAK;AACX,eAAW,CAAC,mBAAsC;AAC9C,YAAM,cAAc,WAAW,UAAU,kBAAkB;AAC3D,UACI,CAAC,cAAc,mBACX,gBACA,aACA,IAAI,GAEV;AACE,eAAO,KAAK,+BAA+B;AAC3C,eAAO;MACV;AAED,aAAO;IACX,CAAC;KACF,CAAC,YAAY,oBAAoB,UAAU,MAAM,CAAC;AAErD,SAAO;AACX;;;AC5Da,IAAA,wBAAwB;EACjC,wBAAwB;IACpB,MAAM;IACN,MAAM;EACT;EACD,+BAA+B;IAC3B,MAAM;IACN,MAAM;EACT;;AAGC,IAAO,iBAAP,MAAO,wBAAuB,UAAS;EACzC,YAAY,WAAmB,cAAqB;AAChD,UAAM,WAAW,YAAY;AAE7B,WAAO,eAAe,MAAM,gBAAe,SAAS;AACpD,SAAK,OAAO;;EAGhB,OAAO,oCAAiC;AACpC,WAAO,IAAI,gBACP,sBAAsB,uBAAuB,MAC7C,sBAAsB,uBAAuB,IAAI;;EAIzD,OAAO,2CAAwC;AAC3C,WAAO,IAAI,gBACP,sBAAsB,8BAA8B,MACpD,sBAAsB,8BAA8B,IAAI;;AAGnE;;;SCSe,sBACZ,iBACA,uBACA,oBAAuC;AAEvC,QAAM,EAAE,UAAU,YAAY,OAAM,IAAK,QAAO;AAChD,QAAMC,mBAAkB,mBAAmB,kBAAkB;AAC7D,QAAM,UAAU,WAAW,kBAAkB;AAC7C,QAAM,CAAC,CAAC,QAAQ,KAAK,GAAG,WAAW,QAAI,wBAErC,CAAC,MAAM,IAAI,CAAC;AAGd,QAAM,cAAU,sBAAO,IAAI;AAC3B,+BAAU,MAAK;AACX,WAAO,MAAK;AACR,cAAQ,UAAU;IACtB;KACD,CAAA,CAAE;AAGL,QAAM,4BAAwB,sBAAO,eAAe,kBAAkB,IAAI;AAC1E,+BAAU,MAAK;AACX,0BAAsB,UAAU,eAAe,kBAAkB;EACrE,GAAG,CAAC,UAAU,CAAC;AAGf,QAAM,yBAAqB,sBAAO,IAAI;AACtC,+BAAU,MAAK;AACX,QAAI,CAAC,CAAC,OAAO;AAET,yBAAmB,UAAU;AAC7B;IACH;AAED,QAAI,CAAC,CAAC,QAAQ;AAEV,yBAAmB,UAAU;AAC7B;IACH;EACL,GAAG,CAAC,OAAO,MAAM,CAAC;AAElB,QAAM,YAAQ,2BACV,OACI,yBACA,oBACsC;AACtC,UAAM,YAAY,2BAA2B;AAC7C,UAAM,eAAe,mBAAmB;AACxC,YAAQ,WAAS;MACb,KAAK,gBAAgB;AACjB,eAAO,QACH,4CAA4C;AAEhD,eAAO,SAAS,WAAW,YAA4B;MAC3D,KAAK,gBAAgB;AAEjB,eAAO,QACH,+CAA+C;AAEnD,eAAO,SACF,cAAc,YAA+B,EAC7C,KAAK,IAAI;MAClB,KAAK,gBAAgB;AACjB,eAAO,QAAQ,2CAA2C;AAC1D,eAAO,SAAS,UAAU,YAAgC;MAC9D;AACI,cAAM,eAAe,kCAAiC;IAC7D;KAEL,CAAC,UAAU,iBAAiB,uBAAuB,MAAM,CAAC;AAG9D,QAAM,mBAAe,2BACjB,OACI,yBACA,oBACsC;AACtC,UAAM,0BACF,2BAA2B;AAE/B,QAAI;AAEJ,QAAI,iBAAiB;AACjB,aAAO,MACH,+EAA+E;AAEnF,qBAAe;QACX,GAAG;;IAEV,WAAU,uBAAuB;AAC9B,aAAO,MACH,2EAA2E;AAE/E,qBAAe;QACX,GAAG;QACH,QAAQ,sBAAsB,UAAU;;IAE/C,OAAM;AACH,aAAO,MACH,2FAA2F;AAE/F,qBAAe;QACX,QAAQ;;IAEf;AAED,QAAI,CAAC,aAAa,WAAW,SAAS;AAClC,aAAO,MACH,qEAAqE;AAEzE,mBAAa,UAAU;IAC1B;AAED,UAAM,WAAW,YAAiD;AAC9D,aAAO,QACH,oDAAoD;AAExD,aAAO,SACF,mBAAmB,YAAY,EAC/B,MAAM,OAAO,MAAgB;AAC1B,YAAI,aAAa,8BAA8B;AAC3C,cAAI,CAAC,sBAAsB,SAAS;AAChC,mBAAO,MACH,2EAA2E;AAE/E,mBAAO,MACH,yBACA,YAAY;UAEnB,OAAM;AACH,mBAAO,MACH,oIAAoI;AAExI,kBAAM,eAAe,yCAAwC;UAChE;QACJ;AAED,cAAM;MACV,CAAC;IACT;AAEA,WAAO,SAAQ,EACV,KAAK,CAAC,aAAyC;AAC5C,UAAI,QAAQ,SAAS;AACjB,oBAAY,CAAC,UAAU,IAAI,CAAC;MAC/B;AACD,aAAO;IACX,CAAC,EACA,MAAM,CAAC,MAAgB;AACpB,UAAI,QAAQ,SAAS;AACjB,oBAAY,CAAC,MAAM,CAAC,CAAC;MACxB;AACD,YAAM;IACV,CAAC;EACT,GACA;IACI;IACA;IACA;IACA;IACA;IACA;EACH,CAAA;AAGL,+BAAU,MAAK;AACX,UAAM,aAAa,SAAS,iBACxB,CAAC,YAAyB;AACtB,cAAQ,QAAQ,WAAS;QACrB,KAAK,UAAU;QACf,KAAK,UAAU;AACX,cAAI,QAAQ,SAAS;AACjB,wBAAY;cACR,QAAQ;cACR;YACH,CAAA;UACJ;AACD;QACJ,KAAK,UAAU;QACf,KAAK,UAAU;AACX,cAAI,QAAQ,OAAO;AACf,wBAAY,CAAC,MAAM,QAAQ,KAAkB,CAAC;UACjD;AACD;MACP;IACL,CAAC;AAEL,WAAO,QACH,8DAA8D,UAAU,EAAE;AAG9E,WAAO,MAAK;AACR,UAAI,YAAY;AACZ,eAAO,QACH,mDAAmD,UAAU,EAAE;AAEnE,iBAAS,oBAAoB,UAAU;MAC1C;IACL;EACJ,GAAG,CAAC,UAAU,MAAM,CAAC;AAErB,+BAAU,MAAK;AACX,QACI,mBAAmB,WACnB,eAAe,kBAAkB,MACnC;AACE,yBAAmB,UAAU;AAC7B,UAAI,CAACA,kBAAiB;AAClB,eAAO,KACH,uEAAuE;AAE3E,cAAK,EAAG,MAAM,MAAK;AAEf;QACJ,CAAC;MACJ,WAAU,SAAS;AAChB,eAAO,KACH,4EAA4E;AAEhF,qBAAY,EAAG,MAAM,MAAK;AAEtB;QACJ,CAAC;MACJ;IACJ;EACL,GAAG,CAACA,kBAAiB,SAAS,YAAY,OAAO,cAAc,MAAM,CAAC;AAEtE,SAAO;IACH;IACA;IACA;IACA;;AAER;;;ACnPM,SAAU,2BAA2B,EACvC,iBACA,UACA,eACA,gBACA,uBACA,kBAAkB,kBAClB,gBAAgB,gBAChB,SAAQ,GACc;AACtB,QAAM,wBAAwC,uBAAQ,MAAK;AACvD,WAAO;MACH;MACA;MACA;;KAEL,CAAC,UAAU,eAAe,cAAc,CAAC;AAC5C,QAAM,UAAU,QAAO;AACvB,QAAM,iBAAiB,sBACnB,iBACA,uBACA,iBAAiB;AAErB,QAAMC,mBAAkB,mBAAmB,iBAAiB;AAE5D,MAAI,eAAe,SAAS,QAAQ,eAAe,kBAAkB,MAAM;AACvE,QAAI,CAAC,CAAC,gBAAgB;AAClB,aAAOC,cAAAA,QAAC,cAAA,gBAAmB,EAAA,GAAA,eAAc,CAAA;IAC5C;AAED,UAAM,eAAe;EACxB;AAED,MAAID,kBAAiB;AACjB,WACIC,cAAAA,QAAA,cAACA,cAAAA,QAAM,UACF,MAAA,sBAAsB,UAAU,cAAc,CAAC;EAG3D;AAED,MAAI,CAAC,CAAC,oBAAoB,QAAQ,eAAe,kBAAkB,MAAM;AACrE,WAAOA,cAAAA,QAAC,cAAA,kBAAqB,EAAA,GAAA,QAAO,CAAA;EACvC;AAED,SAAO;AACX;;;;ACnEa,IAAA,WAAW,CACpB,cACqD;AACrD,QAAM,oBAEF,CAAC,UAAS;AACV,UAAM,OAAO,QAAO;AACpB,WAAOC,cAAAA,QAAA,cAAC,WAAe,EAAA,GAAA,OAAa,aAAa,KAAI,CAAA;EACzD;AAEA,QAAM,gBACF,UAAU,eAAe,UAAU,QAAQ;AAC/C,oBAAkB,cAAc,YAAY,aAAa;AAEzD,SAAO;AACX;", "names": ["SKU", "React", "isAuthenticated", "React", "isAuthenticated", "React", "isAuthenticated", "isAuthenticated", "React", "React"]}