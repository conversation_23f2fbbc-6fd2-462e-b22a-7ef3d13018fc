import {
  useThemeProps
} from "./chunk-YPW5DK5D.js";
import {
  defaultTheme_default,
  identifier_default
} from "./chunk-EQ7NEI4H.js";

// node_modules/@mui/material/styles/useThemeProps.js
function useThemeProps2({
  props,
  name
}) {
  return useThemeProps({
    props,
    name,
    defaultTheme: defaultTheme_default,
    themeId: identifier_default
  });
}

export {
  useThemeProps2 as useThemeProps
};
//# sourceMappingURL=chunk-7YGW6RIT.js.map
