import {
  AccountEntity,
  ApiId,
  AuthError,
  AuthErrorCodes_exports,
  AuthErrorMessage,
  AuthenticationHeaderParser,
  AuthenticationScheme,
  AzureCloudInstance,
  BrowserAuthError,
  BrowserAuthErrorCodes_exports,
  BrowserAuthErrorMessage,
  BrowserCacheLocation,
  BrowserConfigurationAuthError,
  BrowserConfigurationAuthErrorCodes_exports,
  BrowserConfigurationAuthErrorMessage,
  BrowserPerformanceClient,
  BrowserStorage,
  BrowserUtils_exports,
  CacheLookupPolicy,
  ClientAuthError,
  ClientAuthErrorCodes_exports,
  ClientAuthErrorMessage,
  ClientConfigurationError,
  ClientConfigurationErrorCodes_exports,
  ClientConfigurationErrorMessage,
  DEFAULT_IFRAME_TIMEOUT_MS,
  EventMessageUtils,
  EventType,
  InteractionRequiredAuthError,
  InteractionRequiredAuthErrorCodes_exports,
  InteractionRequiredAuthErrorMessage,
  InteractionStatus,
  InteractionType,
  JsonWebTokenTypes,
  LogLevel,
  Logger,
  MemoryStorage,
  NavigationClient,
  OIDC_DEFAULT_SCOPES,
  PerformanceEvents,
  PromptValue,
  ProtocolMode,
  PublicClientApplication,
  PublicClientNext,
  ServerError,
  ServerResponseType,
  SignedHttpRequest,
  StringUtils,
  StubPerformanceClient,
  UrlString,
  WrapperSKU,
  createNestablePublicClientApplication,
  createStandardPublicClientApplication,
  stubbedPublicClientApplication,
  version
} from "./chunk-O7S3OSKW.js";
import {
  BrowserPerformanceMeasurement
} from "./chunk-7WWTMPRL.js";
import "./chunk-EWTE5DHJ.js";
export {
  AccountEntity,
  ApiId,
  AuthError,
  AuthErrorCodes_exports as AuthErrorCodes,
  AuthErrorMessage,
  AuthenticationHeaderParser,
  AuthenticationScheme,
  AzureCloudInstance,
  BrowserAuthError,
  BrowserAuthErrorCodes_exports as BrowserAuthErrorCodes,
  BrowserAuthErrorMessage,
  BrowserCacheLocation,
  BrowserConfigurationAuthError,
  BrowserConfigurationAuthErrorCodes_exports as BrowserConfigurationAuthErrorCodes,
  BrowserConfigurationAuthErrorMessage,
  BrowserPerformanceClient,
  BrowserPerformanceMeasurement,
  BrowserStorage,
  BrowserUtils_exports as BrowserUtils,
  CacheLookupPolicy,
  ClientAuthError,
  ClientAuthErrorCodes_exports as ClientAuthErrorCodes,
  ClientAuthErrorMessage,
  ClientConfigurationError,
  ClientConfigurationErrorCodes_exports as ClientConfigurationErrorCodes,
  ClientConfigurationErrorMessage,
  DEFAULT_IFRAME_TIMEOUT_MS,
  EventMessageUtils,
  EventType,
  InteractionRequiredAuthError,
  InteractionRequiredAuthErrorCodes_exports as InteractionRequiredAuthErrorCodes,
  InteractionRequiredAuthErrorMessage,
  InteractionStatus,
  InteractionType,
  JsonWebTokenTypes,
  LogLevel,
  Logger,
  MemoryStorage,
  NavigationClient,
  OIDC_DEFAULT_SCOPES,
  PerformanceEvents,
  PromptValue,
  ProtocolMode,
  PublicClientApplication,
  PublicClientNext,
  ServerError,
  ServerResponseType,
  SignedHttpRequest,
  StringUtils,
  StubPerformanceClient,
  UrlString,
  WrapperSKU,
  createNestablePublicClientApplication,
  createStandardPublicClientApplication,
  stubbedPublicClientApplication,
  version
};
//# sourceMappingURL=@azure_msal-browser.js.map
