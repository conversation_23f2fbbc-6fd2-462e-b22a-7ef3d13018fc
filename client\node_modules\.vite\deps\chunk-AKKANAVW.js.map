{"version": 3, "sources": ["../../@mui/material/InputBase/utils.js"], "sourcesContent": ["// Supports determination of isControlled().\n// Controlled input accepts its current value as a prop.\n//\n// @see https://facebook.github.io/react/docs/forms.html#controlled-components\n// @param value\n// @returns {boolean} true if string (including '') or number (including zero)\nexport function hasValue(value) {\n  return value != null && !(Array.isArray(value) && value.length === 0);\n}\n\n// Determine if field is empty or filled.\n// Response determines if label is presented above field or as placeholder.\n//\n// @param obj\n// @param SSR\n// @returns {boolean} False when not present or empty string.\n//                    True when any number or string with length.\nexport function isFilled(obj, SSR = false) {\n  return obj && (hasValue(obj.value) && obj.value !== '' || SSR && hasValue(obj.defaultValue) && obj.defaultValue !== '');\n}\n\n// Determine if an Input is adorned on start.\n// It's corresponding to the left with LTR.\n//\n// @param obj\n// @returns {boolean} False when no adornments.\n//                    True when adorned at the start.\nexport function isAdornedStart(obj) {\n  return obj.startAdornment;\n}"], "mappings": ";AAMO,SAAS,SAAS,OAAO;AAC9B,SAAO,SAAS,QAAQ,EAAE,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW;AACrE;AASO,SAAS,SAAS,KAAK,MAAM,OAAO;AACzC,SAAO,QAAQ,SAAS,IAAI,KAAK,KAAK,IAAI,UAAU,MAAM,OAAO,SAAS,IAAI,YAAY,KAAK,IAAI,iBAAiB;AACtH;AAQO,SAAS,eAAe,KAAK;AAClC,SAAO,IAAI;AACb;", "names": []}