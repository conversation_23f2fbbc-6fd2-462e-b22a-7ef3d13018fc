import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js
var React = __toESM(require_react());
var useEnhancedEffect = typeof window !== "undefined" ? React.useLayoutEffect : React.useEffect;
var useEnhancedEffect_default = useEnhancedEffect;

export {
  useEnhancedEffect_default
};
//# sourceMappingURL=chunk-RESQ4RBR.js.map
