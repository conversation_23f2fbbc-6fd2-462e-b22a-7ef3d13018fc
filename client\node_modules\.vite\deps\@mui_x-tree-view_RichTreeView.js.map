{"version": 3, "sources": ["../../@mui/x-tree-view/RichTreeView/RichTreeView.js", "../../@mui/x-tree-view/RichTreeView/richTreeViewClasses.js", "../../@mui/x-tree-view/internals/zero-styled/index.js", "../../@mui/x-tree-view/internals/useTreeView/useTreeView.js", "../../@mui/x-tree-view/internals/useTreeView/useTreeViewModels.js", "../../@mui/x-tree-view/internals/corePlugins/useTreeViewInstanceEvents/useTreeViewInstanceEvents.js", "../../@mui/x-internals/EventManager/EventManager.js", "../../@mui/x-tree-view/internals/corePlugins/useTreeViewOptionalPlugins/useTreeViewOptionalPlugins.js", "../../@mui/x-tree-view/internals/corePlugins/useTreeViewId/useTreeViewId.js", "../../@mui/x-tree-view/internals/corePlugins/corePlugins.js", "../../@mui/x-tree-view/internals/useTreeView/extractPluginParamsFromProps.js", "../../@mui/x-tree-view/internals/useTreeView/useTreeViewBuildContext.js", "../../@mui/x-tree-view/internals/plugins/useTreeViewItems/useTreeViewItems.js", "../../@mui/x-tree-view/internals/utils/publishTreeViewEvent.js", "../../@mui/x-tree-view/internals/plugins/useTreeViewItems/useTreeViewItems.utils.js", "../../@mui/x-tree-view/internals/TreeViewItemDepthContext/TreeViewItemDepthContext.js", "../../@mui/x-tree-view/internals/plugins/useTreeViewExpansion/useTreeViewExpansion.js", "../../@mui/x-tree-view/internals/plugins/useTreeViewSelection/useTreeViewSelection.js", "../../@mui/x-tree-view/internals/utils/tree.js", "../../@mui/x-tree-view/internals/plugins/useTreeViewSelection/useTreeViewSelection.utils.js", "../../@mui/x-tree-view/internals/plugins/useTreeViewFocus/useTreeViewFocus.js", "../../@mui/x-tree-view/internals/hooks/useInstanceEventHandler.js", "../../@mui/x-tree-view/internals/utils/cleanupTracking/TimerBasedCleanupTracking.js", "../../@mui/x-tree-view/internals/utils/cleanupTracking/FinalizationRegistryBasedCleanupTracking.js", "../../@mui/x-tree-view/internals/utils/utils.js", "../../@mui/x-tree-view/internals/plugins/useTreeViewKeyboardNavigation/useTreeViewKeyboardNavigation.js", "../../@mui/x-tree-view/internals/plugins/useTreeViewIcons/useTreeViewIcons.js", "../../@mui/x-tree-view/RichTreeView/RichTreeView.plugins.js", "../../@mui/x-tree-view/TreeItem/TreeItem.js", "../../@mui/x-tree-view/TreeItem/TreeItemContent.js", "../../@mui/x-tree-view/TreeItem/useTreeItemState.js", "../../@mui/x-tree-view/TreeItem2DragAndDropOverlay/TreeItem2DragAndDropOverlay.js", "../../@mui/x-tree-view/TreeItem2LabelInput/TreeItem2LabelInput.js", "../../@mui/x-tree-view/TreeItem/treeItemClasses.js", "../../@mui/x-tree-view/icons/icons.js", "../../@mui/x-tree-view/TreeItem2Provider/TreeItem2Provider.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { getRichTreeViewUtilityClass } from \"./richTreeViewClasses.js\";\nimport { styled, createUseThemeProps } from \"../internals/zero-styled/index.js\";\nimport { useTreeView } from \"../internals/useTreeView/index.js\";\nimport { TreeViewProvider } from \"../internals/TreeViewProvider/index.js\";\nimport { RICH_TREE_VIEW_PLUGINS } from \"./RichTreeView.plugins.js\";\nimport { TreeItem } from \"../TreeItem/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useThemeProps = createUseThemeProps('MuiRichTreeView');\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getRichTreeViewUtilityClass, classes);\n};\nexport const RichTreeViewRoot = styled('ul', {\n  name: 'MuiRichTreeView',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  padding: 0,\n  margin: 0,\n  listStyle: 'none',\n  outline: 0,\n  position: 'relative'\n});\nfunction WrappedTreeItem({\n  slots,\n  slotProps,\n  label,\n  id,\n  itemId,\n  children\n}) {\n  const Item = slots?.item ?? TreeItem;\n  const itemProps = useSlotProps({\n    elementType: Item,\n    externalSlotProps: slotProps?.item,\n    additionalProps: {\n      itemId,\n      id,\n      label\n    },\n    ownerState: {\n      itemId,\n      label\n    }\n  });\n  return /*#__PURE__*/_jsx(Item, _extends({}, itemProps, {\n    children: children\n  }));\n}\n\n/**\n *\n * Demos:\n *\n * - [Tree View](https://mui.com/x/react-tree-view/)\n *\n * API:\n *\n * - [RichTreeView API](https://mui.com/x/api/tree-view/rich-tree-view/)\n */\nconst RichTreeView = /*#__PURE__*/React.forwardRef(function RichTreeView(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiRichTreeView'\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.children != null) {\n      warnOnce(['MUI X: The `RichTreeView` component does not support JSX children.', 'If you want to add items, you need to use the `items` prop.', 'Check the documentation for more details: https://mui.com/x/react-tree-view/rich-tree-view/items/.']);\n    }\n  }\n  const {\n    getRootProps,\n    contextValue,\n    instance\n  } = useTreeView({\n    plugins: RICH_TREE_VIEW_PLUGINS,\n    rootRef: ref,\n    props\n  });\n  const {\n    slots,\n    slotProps\n  } = props;\n  const classes = useUtilityClasses(props);\n  const Root = slots?.root ?? RichTreeViewRoot;\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps?.root,\n    className: classes.root,\n    getSlotProps: getRootProps,\n    ownerState: props\n  });\n  const itemsToRender = instance.getItemsToRender();\n  const renderItem = ({\n    label,\n    itemId,\n    id,\n    children\n  }) => {\n    return /*#__PURE__*/_jsx(WrappedTreeItem, {\n      slots: slots,\n      slotProps: slotProps,\n      label: label,\n      id: id,\n      itemId: itemId,\n      children: children?.map(renderItem)\n    }, itemId);\n  };\n  return /*#__PURE__*/_jsx(TreeViewProvider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n      children: itemsToRender.map(renderItem)\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? RichTreeView.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The ref object that allows Tree View manipulation. Can be instantiated with `useTreeViewApiRef()`.\n   */\n  apiRef: PropTypes.shape({\n    current: PropTypes.shape({\n      focusItem: PropTypes.func.isRequired,\n      getItem: PropTypes.func.isRequired,\n      getItemDOMElement: PropTypes.func.isRequired,\n      getItemOrderedChildrenIds: PropTypes.func.isRequired,\n      getItemTree: PropTypes.func.isRequired,\n      selectItem: PropTypes.func.isRequired,\n      setItemExpansion: PropTypes.func.isRequired,\n      updateItemLabel: PropTypes.func.isRequired\n    })\n  }),\n  /**\n   * If `true`, the tree view renders a checkbox at the left of its label that allows selecting it.\n   * @default false\n   */\n  checkboxSelection: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * Expanded item ids.\n   * Used when the item's expansion is not controlled.\n   * @default []\n   */\n  defaultExpandedItems: PropTypes.arrayOf(PropTypes.string),\n  /**\n   * Selected item ids. (Uncontrolled)\n   * When `multiSelect` is true this takes an array of strings; when false (default) a string.\n   * @default []\n   */\n  defaultSelectedItems: PropTypes.any,\n  /**\n   * If `true`, will allow focus on disabled items.\n   * @default false\n   */\n  disabledItemsFocusable: PropTypes.bool,\n  /**\n   * If `true` selection is disabled.\n   * @default false\n   */\n  disableSelection: PropTypes.bool,\n  /**\n   * Expanded item ids.\n   * Used when the item's expansion is controlled.\n   */\n  expandedItems: PropTypes.arrayOf(PropTypes.string),\n  /**\n   * The slot that triggers the item's expansion when clicked.\n   * @default 'content'\n   */\n  expansionTrigger: PropTypes.oneOf(['content', 'iconContainer']),\n  /**\n   * Unstable features, breaking changes might be introduced.\n   * For each feature, if the flag is not explicitly set to `true`,\n   * the feature will be fully disabled and any property / method call will not have any effect.\n   */\n  experimentalFeatures: PropTypes.shape({\n    indentationAtItemLevel: PropTypes.bool,\n    labelEditing: PropTypes.bool\n  }),\n  /**\n   * Used to determine the id of a given item.\n   *\n   * @template R\n   * @param {R} item The item to check.\n   * @returns {string} The id of the item.\n   * @default (item) => item.id\n   */\n  getItemId: PropTypes.func,\n  /**\n   * Used to determine the string label for a given item.\n   *\n   * @template R\n   * @param {R} item The item to check.\n   * @returns {string} The label of the item.\n   * @default (item) => item.label\n   */\n  getItemLabel: PropTypes.func,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * Used to determine if a given item should be disabled.\n   * @template R\n   * @param {R} item The item to check.\n   * @returns {boolean} `true` if the item should be disabled.\n   */\n  isItemDisabled: PropTypes.func,\n  /**\n   * Determines if a given item is editable or not.\n   * Make sure to also enable the `labelEditing` experimental feature:\n   * `<RichTreeViewPro experimentalFeatures={{ labelEditing: true }}  />`.\n   * By default, the items are not editable.\n   * @template R\n   * @param {R} item The item to check.\n   * @returns {boolean} `true` if the item is editable.\n   */\n  isItemEditable: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * Horizontal indentation between an item and its children.\n   * Examples: 24, \"24px\", \"2rem\", \"2em\".\n   * @default 12px\n   */\n  itemChildrenIndentation: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  items: PropTypes.array.isRequired,\n  /**\n   * If `true`, `ctrl` and `shift` will trigger multiselect.\n   * @default false\n   */\n  multiSelect: PropTypes.bool,\n  /**\n   * Callback fired when tree items are expanded/collapsed.\n   * @param {React.SyntheticEvent} event The DOM event that triggered the change.\n   * @param {array} itemIds The ids of the expanded items.\n   */\n  onExpandedItemsChange: PropTypes.func,\n  /**\n   * Callback fired when the `content` slot of a given tree item is clicked.\n   * @param {React.MouseEvent} event The DOM event that triggered the change.\n   * @param {string} itemId The id of the focused item.\n   */\n  onItemClick: PropTypes.func,\n  /**\n   * Callback fired when a tree item is expanded or collapsed.\n   * @param {React.SyntheticEvent} event The DOM event that triggered the change.\n   * @param {array} itemId The itemId of the modified item.\n   * @param {array} isExpanded `true` if the item has just been expanded, `false` if it has just been collapsed.\n   */\n  onItemExpansionToggle: PropTypes.func,\n  /**\n   * Callback fired when a given tree item is focused.\n   * @param {React.SyntheticEvent | null} event The DOM event that triggered the change. **Warning**: This is a generic event not a focus event.\n   * @param {string} itemId The id of the focused item.\n   */\n  onItemFocus: PropTypes.func,\n  /**\n   * Callback fired when the label of an item changes.\n   * @param {TreeViewItemId} itemId The id of the item that was edited.\n   * @param {string} newLabel The new label of the items.\n   */\n  onItemLabelChange: PropTypes.func,\n  /**\n   * Callback fired when a tree item is selected or deselected.\n   * @param {React.SyntheticEvent} event The DOM event that triggered the change.\n   * @param {array} itemId The itemId of the modified item.\n   * @param {array} isSelected `true` if the item has just been selected, `false` if it has just been deselected.\n   */\n  onItemSelectionToggle: PropTypes.func,\n  /**\n   * Callback fired when tree items are selected/deselected.\n   * @param {React.SyntheticEvent} event The DOM event that triggered the change.\n   * @param {string[] | string} itemIds The ids of the selected items.\n   * When `multiSelect` is `true`, this is an array of strings; when false (default) a string.\n   */\n  onSelectedItemsChange: PropTypes.func,\n  /**\n   * Selected item ids. (Controlled)\n   * When `multiSelect` is true this takes an array of strings; when false (default) a string.\n   */\n  selectedItems: PropTypes.any,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { RichTreeView };", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getRichTreeViewUtilityClass(slot) {\n  return generateUtilityClass('MuiRichTreeView', slot);\n}\nexport const richTreeViewClasses = generateUtilityClasses('MuiRichTreeView', ['root']);", "import { useThemeProps } from '@mui/material/styles';\nexport { styled } from '@mui/material/styles';\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function createUseThemeProps(name) {\n  return useThemeProps;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useTreeViewModels } from \"./useTreeViewModels.js\";\nimport { TREE_VIEW_CORE_PLUGINS } from \"../corePlugins/index.js\";\nimport { extractPluginParamsFromProps } from \"./extractPluginParamsFromProps.js\";\nimport { useTreeViewBuildContext } from \"./useTreeViewBuildContext.js\";\nexport function useTreeViewApiInitialization(inputApiRef) {\n  const fallbackPublicApiRef = React.useRef({});\n  if (inputApiRef) {\n    if (inputApiRef.current == null) {\n      inputApiRef.current = {};\n    }\n    return inputApiRef.current;\n  }\n  return fallbackPublicApiRef.current;\n}\nexport const useTreeView = ({\n  plugins: inPlugins,\n  rootRef,\n  props\n}) => {\n  const plugins = [...TREE_VIEW_CORE_PLUGINS, ...inPlugins];\n  const {\n    pluginParams,\n    forwardedProps,\n    apiRef,\n    experimentalFeatures,\n    slots,\n    slotProps\n  } = extractPluginParamsFromProps({\n    plugins,\n    props\n  });\n  const models = useTreeViewModels(plugins, pluginParams);\n  const instanceRef = React.useRef({});\n  const instance = instanceRef.current;\n  const publicAPI = useTreeViewApiInitialization(apiRef);\n  const innerRootRef = React.useRef(null);\n  const handleRootRef = useForkRef(innerRootRef, rootRef);\n  const contextValue = useTreeViewBuildContext({\n    plugins,\n    instance,\n    publicAPI,\n    rootRef: innerRootRef\n  });\n  const [state, setState] = React.useState(() => {\n    const temp = {};\n    plugins.forEach(plugin => {\n      if (plugin.getInitialState) {\n        Object.assign(temp, plugin.getInitialState(pluginParams));\n      }\n    });\n    return temp;\n  });\n  const rootPropsGetters = [];\n  const runPlugin = plugin => {\n    const pluginResponse = plugin({\n      instance,\n      params: pluginParams,\n      slots,\n      slotProps,\n      experimentalFeatures,\n      state,\n      setState,\n      rootRef: innerRootRef,\n      models,\n      plugins\n    });\n    if (pluginResponse.getRootProps) {\n      rootPropsGetters.push(pluginResponse.getRootProps);\n    }\n    if (pluginResponse.publicAPI) {\n      Object.assign(publicAPI, pluginResponse.publicAPI);\n    }\n    if (pluginResponse.instance) {\n      Object.assign(instance, pluginResponse.instance);\n    }\n    if (pluginResponse.contextValue) {\n      Object.assign(contextValue, pluginResponse.contextValue);\n    }\n  };\n  plugins.forEach(runPlugin);\n  const getRootProps = (otherHandlers = {}) => {\n    const rootProps = _extends({\n      role: 'tree'\n    }, forwardedProps, otherHandlers, {\n      ref: handleRootRef\n    });\n    rootPropsGetters.forEach(rootPropsGetter => {\n      Object.assign(rootProps, rootPropsGetter(otherHandlers));\n    });\n    return rootProps;\n  };\n  return {\n    getRootProps,\n    rootRef: handleRootRef,\n    contextValue,\n    instance\n  };\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\n/**\n * Implements the same behavior as `useControlled` but for several models.\n * The controlled models are never stored in the state, and the state is only updated if the model is not controlled.\n */\nexport const useTreeViewModels = (plugins, props) => {\n  const modelsRef = React.useRef({});\n  const [modelsState, setModelsState] = React.useState(() => {\n    const initialState = {};\n    plugins.forEach(plugin => {\n      if (plugin.models) {\n        Object.entries(plugin.models).forEach(([modelName, modelInitializer]) => {\n          modelsRef.current[modelName] = {\n            isControlled: props[modelName] !== undefined,\n            getDefaultValue: modelInitializer.getDefaultValue\n          };\n          initialState[modelName] = modelInitializer.getDefaultValue(props);\n        });\n      }\n    });\n    return initialState;\n  });\n  const models = Object.fromEntries(Object.entries(modelsRef.current).map(([modelName, model]) => {\n    const value = props[modelName] ?? modelsState[modelName];\n    return [modelName, {\n      value,\n      setControlledValue: newValue => {\n        if (!model.isControlled) {\n          setModelsState(prevState => _extends({}, prevState, {\n            [modelName]: newValue\n          }));\n        }\n      }\n    }];\n  }));\n\n  // We know that `modelsRef` do not vary across renders.\n  /* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n  if (process.env.NODE_ENV !== 'production') {\n    Object.entries(modelsRef.current).forEach(([modelName, model]) => {\n      const controlled = props[modelName];\n      const newDefaultValue = model.getDefaultValue(props);\n      React.useEffect(() => {\n        if (model.isControlled !== (controlled !== undefined)) {\n          console.error([`MUI X: A component is changing the ${model.isControlled ? '' : 'un'}controlled ${modelName} state of TreeView to be ${model.isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled ${modelName} ` + 'element for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n        }\n      }, [controlled]);\n      const {\n        current: defaultValue\n      } = React.useRef(newDefaultValue);\n      React.useEffect(() => {\n        if (!model.isControlled && defaultValue !== newDefaultValue) {\n          console.error([`MUI X: A component is changing the default ${modelName} state of an uncontrolled TreeView after being initialized. ` + `To suppress this warning opt to use a controlled TreeView.`].join('\\n'));\n        }\n      }, [JSON.stringify(newDefaultValue)]);\n    });\n  }\n  /* eslint-enable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\n\n  return models;\n};", "import * as React from 'react';\nimport { EventManager } from '@mui/x-internals/EventManager';\nconst isSyntheticEvent = event => {\n  return event.isPropagationStopped !== undefined;\n};\nexport const useTreeViewInstanceEvents = () => {\n  const [eventManager] = React.useState(() => new EventManager());\n  const publishEvent = React.useCallback((...args) => {\n    const [name, params, event = {}] = args;\n    event.defaultMuiPrevented = false;\n    if (isSyntheticEvent(event) && event.isPropagationStopped()) {\n      return;\n    }\n    eventManager.emit(name, params, event);\n  }, [eventManager]);\n  const subscribeEvent = React.useCallback((event, handler) => {\n    eventManager.on(event, handler);\n    return () => {\n      eventManager.removeListener(event, handler);\n    };\n  }, [eventManager]);\n  return {\n    instance: {\n      $$publishEvent: publishEvent,\n      $$subscribeEvent: subscribeEvent\n    }\n  };\n};\nuseTreeViewInstanceEvents.params = {};", "// Used https://gist.github.com/mudge/5830382 as a starting point.\n// See https://github.com/browserify/events/blob/master/events.js for\n// the Node.js (https://nodejs.org/api/events.html) polyfill used by webpack.\nexport class EventManager {\n  constructor() {\n    this.maxListeners = 20;\n    this.warnOnce = false;\n    this.events = {};\n  }\n  on(eventName, listener, options = {}) {\n    let collection = this.events[eventName];\n    if (!collection) {\n      collection = {\n        highPriority: new Map(),\n        regular: new Map()\n      };\n      this.events[eventName] = collection;\n    }\n    if (options.isFirst) {\n      collection.highPriority.set(listener, true);\n    } else {\n      collection.regular.set(listener, true);\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const collectionSize = collection.highPriority.size + collection.regular.size;\n      if (collectionSize > this.maxListeners && !this.warnOnce) {\n        this.warnOnce = true;\n        console.warn([`Possible EventEmitter memory leak detected. ${collectionSize} ${eventName} listeners added.`].join('\\n'));\n      }\n    }\n  }\n  removeListener(eventName, listener) {\n    if (this.events[eventName]) {\n      this.events[eventName].regular.delete(listener);\n      this.events[eventName].highPriority.delete(listener);\n    }\n  }\n  removeAllListeners() {\n    this.events = {};\n  }\n  emit(eventName, ...args) {\n    const collection = this.events[eventName];\n    if (!collection) {\n      return;\n    }\n    const highPriorityListeners = Array.from(collection.highPriority.keys());\n    const regularListeners = Array.from(collection.regular.keys());\n    for (let i = highPriorityListeners.length - 1; i >= 0; i -= 1) {\n      const listener = highPriorityListeners[i];\n      if (collection.highPriority.has(listener)) {\n        listener.apply(this, args);\n      }\n    }\n    for (let i = 0; i < regularListeners.length; i += 1) {\n      const listener = regularListeners[i];\n      if (collection.regular.has(listener)) {\n        listener.apply(this, args);\n      }\n    }\n  }\n  once(eventName, listener) {\n    // eslint-disable-next-line consistent-this\n    const that = this;\n    this.on(eventName, function oneTimeListener(...args) {\n      that.removeListener(eventName, oneTimeListener);\n      listener.apply(that, args);\n    });\n  }\n}", "export const useTreeViewOptionalPlugins = ({\n  plugins\n}) => {\n  const pluginSet = new Set(plugins);\n  const getAvailablePlugins = () => pluginSet;\n  return {\n    instance: {\n      getAvailablePlugins\n    }\n  };\n};\nuseTreeViewOptionalPlugins.params = {};", "import * as React from 'react';\nimport useId from '@mui/utils/useId';\nexport const useTreeViewId = ({\n  params\n}) => {\n  const treeId = useId(params.id);\n  const getTreeItemIdAttribute = React.useCallback((itemId, idAttribute) => idAttribute ?? `${treeId}-${itemId}`, [treeId]);\n  return {\n    getRootProps: () => ({\n      id: treeId\n    }),\n    instance: {\n      getTreeItemIdAttribute\n    }\n  };\n};\nuseTreeViewId.params = {\n  id: true\n};", "import { useTreeViewInstanceEvents } from \"./useTreeViewInstanceEvents/index.js\";\nimport { useTreeViewOptionalPlugins } from \"./useTreeViewOptionalPlugins/index.js\";\nimport { useTreeViewId } from \"./useTreeViewId/index.js\";\n/**\n * Internal plugins that create the tools used by the other plugins.\n * These plugins are used by the tree view components.\n */\nexport const TREE_VIEW_CORE_PLUGINS = [useTreeViewInstanceEvents, useTreeViewOptionalPlugins, useTreeViewId];", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"slots\", \"slotProps\", \"apiRef\", \"experimentalFeatures\"];\nexport const extractPluginParamsFromProps = _ref => {\n  let {\n      props: {\n        slots,\n        slotProps,\n        apiRef,\n        experimentalFeatures\n      },\n      plugins\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref.props, _excluded);\n  const paramsLookup = {};\n  plugins.forEach(plugin => {\n    Object.assign(paramsLookup, plugin.params);\n  });\n  const pluginParams = {};\n  const forwardedProps = {};\n  Object.keys(props).forEach(propName => {\n    const prop = props[propName];\n    if (paramsLookup[propName]) {\n      pluginParams[propName] = prop;\n    } else {\n      forwardedProps[propName] = prop;\n    }\n  });\n  const defaultizedPluginParams = plugins.reduce((acc, plugin) => {\n    if (plugin.getDefaultizedParams) {\n      return plugin.getDefaultizedParams(acc);\n    }\n    return acc;\n  }, pluginParams);\n  return {\n    apiRef,\n    forwardedProps,\n    pluginParams: defaultizedPluginParams,\n    slots: slots ?? {},\n    slotProps: slotProps ?? {},\n    experimentalFeatures: experimentalFeatures ?? {}\n  };\n};", "export const useTreeViewBuildContext = ({\n  plugins,\n  instance,\n  publicAPI,\n  rootRef\n}) => {\n  const runItemPlugins = itemPluginProps => {\n    let finalRootRef = null;\n    let finalContentRef = null;\n    const pluginPropEnhancers = [];\n    const pluginPropEnhancersNames = {};\n    plugins.forEach(plugin => {\n      if (!plugin.itemPlugin) {\n        return;\n      }\n      const itemPluginResponse = plugin.itemPlugin({\n        props: itemPluginProps,\n        rootRef: finalRootRef,\n        contentRef: finalContentRef\n      });\n      if (itemPluginResponse?.rootRef) {\n        finalRootRef = itemPluginResponse.rootRef;\n      }\n      if (itemPluginResponse?.contentRef) {\n        finalContentRef = itemPluginResponse.contentRef;\n      }\n      if (itemPluginResponse?.propsEnhancers) {\n        pluginPropEnhancers.push(itemPluginResponse.propsEnhancers);\n\n        // Prepare a list of all the slots which are enhanced by at least one plugin\n        Object.keys(itemPluginResponse.propsEnhancers).forEach(propsEnhancerName => {\n          pluginPropEnhancersNames[propsEnhancerName] = true;\n        });\n      }\n    });\n    const resolvePropsEnhancer = currentSlotName => currentSlotParams => {\n      const enhancedProps = {};\n      pluginPropEnhancers.forEach(propsEnhancersForCurrentPlugin => {\n        const propsEnhancerForCurrentPluginAndSlot = propsEnhancersForCurrentPlugin[currentSlotName];\n        if (propsEnhancerForCurrentPluginAndSlot != null) {\n          Object.assign(enhancedProps, propsEnhancerForCurrentPluginAndSlot(currentSlotParams));\n        }\n      });\n      return enhancedProps;\n    };\n    const propsEnhancers = Object.fromEntries(Object.keys(pluginPropEnhancersNames).map(propEnhancerName => [propEnhancerName, resolvePropsEnhancer(propEnhancerName)]));\n    return {\n      contentRef: finalContentRef,\n      rootRef: finalRootRef,\n      propsEnhancers\n    };\n  };\n  const wrapItem = ({\n    itemId,\n    children\n  }) => {\n    let finalChildren = children;\n    // The wrappers are reversed to ensure that the first wrapper is the outermost one.\n    for (let i = plugins.length - 1; i >= 0; i -= 1) {\n      const plugin = plugins[i];\n      if (plugin.wrapItem) {\n        finalChildren = plugin.wrapItem({\n          itemId,\n          children: finalChildren,\n          instance\n        });\n      }\n    }\n    return finalChildren;\n  };\n  const wrapRoot = ({\n    children\n  }) => {\n    let finalChildren = children;\n    // The wrappers are reversed to ensure that the first wrapper is the outermost one.\n    for (let i = plugins.length - 1; i >= 0; i -= 1) {\n      const plugin = plugins[i];\n      if (plugin.wrapRoot) {\n        finalChildren = plugin.wrapRoot({\n          children: finalChildren,\n          instance\n        });\n      }\n    }\n    return finalChildren;\n  };\n  return {\n    runItemPlugins,\n    wrapItem,\n    wrapRoot,\n    instance,\n    rootRef,\n    publicAPI\n  };\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\"];\nimport * as React from 'react';\nimport { publishTreeViewEvent } from \"../../utils/publishTreeViewEvent.js\";\nimport { buildSiblingIndexes, TREE_VIEW_ROOT_PARENT_ID } from \"./useTreeViewItems.utils.js\";\nimport { TreeViewItemDepthContext } from \"../../TreeViewItemDepthContext/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst updateItemsState = ({\n  items,\n  isItemDisabled,\n  getItemLabel,\n  getItemId\n}) => {\n  const itemMetaMap = {};\n  const itemMap = {};\n  const itemOrderedChildrenIds = {\n    [TREE_VIEW_ROOT_PARENT_ID]: []\n  };\n  const processItem = (item, depth, parentId) => {\n    const id = getItemId ? getItemId(item) : item.id;\n    if (id == null) {\n      throw new Error(['MUI X: The Tree View component requires all items to have a unique `id` property.', 'Alternatively, you can use the `getItemId` prop to specify a custom id for each item.', 'An item was provided without id in the `items` prop:', JSON.stringify(item)].join('\\n'));\n    }\n    if (itemMetaMap[id] != null) {\n      throw new Error(['MUI X: The Tree View component requires all items to have a unique `id` property.', 'Alternatively, you can use the `getItemId` prop to specify a custom id for each item.', `Two items were provided with the same id in the \\`items\\` prop: \"${id}\"`].join('\\n'));\n    }\n    const label = getItemLabel ? getItemLabel(item) : item.label;\n    if (label == null) {\n      throw new Error(['MUI X: The Tree View component requires all items to have a `label` property.', 'Alternatively, you can use the `getItemLabel` prop to specify a custom label for each item.', 'An item was provided without label in the `items` prop:', JSON.stringify(item)].join('\\n'));\n    }\n    itemMetaMap[id] = {\n      id,\n      label,\n      parentId,\n      idAttribute: undefined,\n      expandable: !!item.children?.length,\n      disabled: isItemDisabled ? isItemDisabled(item) : false,\n      depth\n    };\n    itemMap[id] = item;\n    const parentIdWithDefault = parentId ?? TREE_VIEW_ROOT_PARENT_ID;\n    if (!itemOrderedChildrenIds[parentIdWithDefault]) {\n      itemOrderedChildrenIds[parentIdWithDefault] = [];\n    }\n    itemOrderedChildrenIds[parentIdWithDefault].push(id);\n    item.children?.forEach(child => processItem(child, depth + 1, id));\n  };\n  items.forEach(item => processItem(item, 0, null));\n  const itemChildrenIndexes = {};\n  Object.keys(itemOrderedChildrenIds).forEach(parentId => {\n    itemChildrenIndexes[parentId] = buildSiblingIndexes(itemOrderedChildrenIds[parentId]);\n  });\n  return {\n    itemMetaMap,\n    itemMap,\n    itemOrderedChildrenIds,\n    itemChildrenIndexes\n  };\n};\nexport const useTreeViewItems = ({\n  instance,\n  params,\n  state,\n  setState,\n  experimentalFeatures\n}) => {\n  const getItemMeta = React.useCallback(itemId => state.items.itemMetaMap[itemId], [state.items.itemMetaMap]);\n  const getItem = React.useCallback(itemId => state.items.itemMap[itemId], [state.items.itemMap]);\n  const getItemTree = React.useCallback(() => {\n    const getItemFromItemId = id => {\n      const _state$items$itemMap$ = state.items.itemMap[id],\n        item = _objectWithoutPropertiesLoose(_state$items$itemMap$, _excluded);\n      const newChildren = state.items.itemOrderedChildrenIds[id];\n      if (newChildren) {\n        item.children = newChildren.map(getItemFromItemId);\n      }\n      return item;\n    };\n    return state.items.itemOrderedChildrenIds[TREE_VIEW_ROOT_PARENT_ID].map(getItemFromItemId);\n  }, [state.items.itemMap, state.items.itemOrderedChildrenIds]);\n  const isItemDisabled = React.useCallback(itemId => {\n    if (itemId == null) {\n      return false;\n    }\n    let itemMeta = instance.getItemMeta(itemId);\n\n    // This can be called before the item has been added to the item map.\n    if (!itemMeta) {\n      return false;\n    }\n    if (itemMeta.disabled) {\n      return true;\n    }\n    while (itemMeta.parentId != null) {\n      itemMeta = instance.getItemMeta(itemMeta.parentId);\n      if (itemMeta.disabled) {\n        return true;\n      }\n    }\n    return false;\n  }, [instance]);\n  const getItemIndex = React.useCallback(itemId => {\n    const parentId = instance.getItemMeta(itemId).parentId ?? TREE_VIEW_ROOT_PARENT_ID;\n    return state.items.itemChildrenIndexes[parentId][itemId];\n  }, [instance, state.items.itemChildrenIndexes]);\n  const getItemOrderedChildrenIds = React.useCallback(itemId => state.items.itemOrderedChildrenIds[itemId ?? TREE_VIEW_ROOT_PARENT_ID] ?? [], [state.items.itemOrderedChildrenIds]);\n  const getItemDOMElement = itemId => {\n    const itemMeta = instance.getItemMeta(itemId);\n    if (itemMeta == null) {\n      return null;\n    }\n    return document.getElementById(instance.getTreeItemIdAttribute(itemId, itemMeta.idAttribute));\n  };\n  const isItemNavigable = itemId => {\n    if (params.disabledItemsFocusable) {\n      return true;\n    }\n    return !instance.isItemDisabled(itemId);\n  };\n  const areItemUpdatesPreventedRef = React.useRef(false);\n  const preventItemUpdates = React.useCallback(() => {\n    areItemUpdatesPreventedRef.current = true;\n  }, []);\n  const areItemUpdatesPrevented = React.useCallback(() => areItemUpdatesPreventedRef.current, []);\n  React.useEffect(() => {\n    if (instance.areItemUpdatesPrevented()) {\n      return;\n    }\n    setState(prevState => {\n      const newState = updateItemsState({\n        items: params.items,\n        isItemDisabled: params.isItemDisabled,\n        getItemId: params.getItemId,\n        getItemLabel: params.getItemLabel\n      });\n      Object.values(prevState.items.itemMetaMap).forEach(item => {\n        if (!newState.itemMetaMap[item.id]) {\n          publishTreeViewEvent(instance, 'removeItem', {\n            id: item.id\n          });\n        }\n      });\n      return _extends({}, prevState, {\n        items: newState\n      });\n    });\n  }, [instance, setState, params.items, params.isItemDisabled, params.getItemId, params.getItemLabel]);\n  const getItemsToRender = () => {\n    const getPropsFromItemId = id => {\n      const item = state.items.itemMetaMap[id];\n      return {\n        label: item.label,\n        itemId: item.id,\n        id: item.idAttribute,\n        children: state.items.itemOrderedChildrenIds[id]?.map(getPropsFromItemId)\n      };\n    };\n    return state.items.itemOrderedChildrenIds[TREE_VIEW_ROOT_PARENT_ID].map(getPropsFromItemId);\n  };\n  return {\n    getRootProps: () => ({\n      style: {\n        '--TreeView-itemChildrenIndentation': typeof params.itemChildrenIndentation === 'number' ? `${params.itemChildrenIndentation}px` : params.itemChildrenIndentation\n      }\n    }),\n    publicAPI: {\n      getItem,\n      getItemDOMElement,\n      getItemTree,\n      getItemOrderedChildrenIds\n    },\n    instance: {\n      getItemMeta,\n      getItem,\n      getItemTree,\n      getItemsToRender,\n      getItemIndex,\n      getItemDOMElement,\n      getItemOrderedChildrenIds,\n      isItemDisabled,\n      isItemNavigable,\n      preventItemUpdates,\n      areItemUpdatesPrevented\n    },\n    contextValue: {\n      items: {\n        onItemClick: params.onItemClick,\n        disabledItemsFocusable: params.disabledItemsFocusable,\n        indentationAtItemLevel: experimentalFeatures.indentationAtItemLevel ?? false\n      }\n    }\n  };\n};\nuseTreeViewItems.getInitialState = params => ({\n  items: updateItemsState({\n    items: params.items,\n    isItemDisabled: params.isItemDisabled,\n    getItemId: params.getItemId,\n    getItemLabel: params.getItemLabel\n  })\n});\nuseTreeViewItems.getDefaultizedParams = params => _extends({}, params, {\n  disabledItemsFocusable: params.disabledItemsFocusable ?? false,\n  itemChildrenIndentation: params.itemChildrenIndentation ?? '12px'\n});\nuseTreeViewItems.wrapRoot = ({\n  children,\n  instance\n}) => {\n  return /*#__PURE__*/_jsx(TreeViewItemDepthContext.Provider, {\n    value: itemId => instance.getItemMeta(itemId)?.depth ?? 0,\n    children: children\n  });\n};\nuseTreeViewItems.params = {\n  disabledItemsFocusable: true,\n  items: true,\n  isItemDisabled: true,\n  getItemLabel: true,\n  getItemId: true,\n  onItemClick: true,\n  itemChildrenIndentation: true\n};", "export const publishTreeViewEvent = (instance, eventName, params) => {\n  instance.$$publishEvent(eventName, params);\n};", "export const TREE_VIEW_ROOT_PARENT_ID = '__TREE_VIEW_ROOT_PARENT_ID__';\nexport const buildSiblingIndexes = siblings => {\n  const siblingsIndexLookup = {};\n  siblings.forEach((childId, index) => {\n    siblingsIndexLookup[childId] = index;\n  });\n  return siblingsIndexLookup;\n};", "import * as React from 'react';\nexport const TreeViewItemDepthContext = /*#__PURE__*/React.createContext(() => -1);\nif (process.env.NODE_ENV !== 'production') {\n  TreeViewItemDepthContext.displayName = 'TreeViewItemDepthContext';\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nexport const useTreeViewExpansion = ({\n  instance,\n  params,\n  models\n}) => {\n  const expandedItemsMap = React.useMemo(() => {\n    const temp = new Map();\n    models.expandedItems.value.forEach(id => {\n      temp.set(id, true);\n    });\n    return temp;\n  }, [models.expandedItems.value]);\n  const setExpandedItems = (event, value) => {\n    params.onExpandedItemsChange?.(event, value);\n    models.expandedItems.setControlledValue(value);\n  };\n  const isItemExpanded = React.useCallback(itemId => expandedItemsMap.has(itemId), [expandedItemsMap]);\n  const isItemExpandable = React.useCallback(itemId => !!instance.getItemMeta(itemId)?.expandable, [instance]);\n  const toggleItemExpansion = useEventCallback((event, itemId) => {\n    const isExpandedBefore = instance.isItemExpanded(itemId);\n    instance.setItemExpansion(event, itemId, !isExpandedBefore);\n  });\n  const setItemExpansion = useEventCallback((event, itemId, isExpanded) => {\n    const isExpandedBefore = instance.isItemExpanded(itemId);\n    if (isExpandedBefore === isExpanded) {\n      return;\n    }\n    let newExpanded;\n    if (isExpanded) {\n      newExpanded = [itemId].concat(models.expandedItems.value);\n    } else {\n      newExpanded = models.expandedItems.value.filter(id => id !== itemId);\n    }\n    if (params.onItemExpansionToggle) {\n      params.onItemExpansionToggle(event, itemId, isExpanded);\n    }\n    setExpandedItems(event, newExpanded);\n  });\n  const expandAllSiblings = (event, itemId) => {\n    const itemMeta = instance.getItemMeta(itemId);\n    const siblings = instance.getItemOrderedChildrenIds(itemMeta.parentId);\n    const diff = siblings.filter(child => instance.isItemExpandable(child) && !instance.isItemExpanded(child));\n    const newExpanded = models.expandedItems.value.concat(diff);\n    if (diff.length > 0) {\n      if (params.onItemExpansionToggle) {\n        diff.forEach(newlyExpandedItemId => {\n          params.onItemExpansionToggle(event, newlyExpandedItemId, true);\n        });\n      }\n      setExpandedItems(event, newExpanded);\n    }\n  };\n  const expansionTrigger = React.useMemo(() => {\n    if (params.expansionTrigger) {\n      return params.expansionTrigger;\n    }\n    if (instance.isTreeViewEditable) {\n      return 'iconContainer';\n    }\n    return 'content';\n  }, [params.expansionTrigger, instance.isTreeViewEditable]);\n  return {\n    publicAPI: {\n      setItemExpansion\n    },\n    instance: {\n      isItemExpanded,\n      isItemExpandable,\n      setItemExpansion,\n      toggleItemExpansion,\n      expandAllSiblings\n    },\n    contextValue: {\n      expansion: {\n        expansionTrigger\n      }\n    }\n  };\n};\nuseTreeViewExpansion.models = {\n  expandedItems: {\n    getDefaultValue: params => params.defaultExpandedItems\n  }\n};\nconst DEFAULT_EXPANDED_ITEMS = [];\nuseTreeViewExpansion.getDefaultizedParams = params => _extends({}, params, {\n  defaultExpandedItems: params.defaultExpandedItems ?? DEFAULT_EXPANDED_ITEMS\n});\nuseTreeViewExpansion.params = {\n  expandedItems: true,\n  defaultExpandedItems: true,\n  onExpandedItemsChange: true,\n  onItemExpansionToggle: true,\n  expansionTrigger: true\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { findOrderInTremauxTree, getAllNavigableItems, getFirstNavigableItem, getLastNavigableItem, getNonDisabledItemsInRange } from \"../../utils/tree.js\";\nimport { convertSelectedItemsToArray, getLookupFromArray } from \"./useTreeViewSelection.utils.js\";\nexport const useTreeViewSelection = ({\n  instance,\n  params,\n  models\n}) => {\n  const lastSelectedItem = React.useRef(null);\n  const lastSelectedRange = React.useRef({});\n  const selectedItemsMap = React.useMemo(() => {\n    const temp = new Map();\n    if (Array.isArray(models.selectedItems.value)) {\n      models.selectedItems.value.forEach(id => {\n        temp.set(id, true);\n      });\n    } else if (models.selectedItems.value != null) {\n      temp.set(models.selectedItems.value, true);\n    }\n    return temp;\n  }, [models.selectedItems.value]);\n  const setSelectedItems = (event, newSelectedItems) => {\n    if (params.onItemSelectionToggle) {\n      if (params.multiSelect) {\n        const addedItems = newSelectedItems.filter(itemId => !instance.isItemSelected(itemId));\n        const removedItems = models.selectedItems.value.filter(itemId => !newSelectedItems.includes(itemId));\n        addedItems.forEach(itemId => {\n          params.onItemSelectionToggle(event, itemId, true);\n        });\n        removedItems.forEach(itemId => {\n          params.onItemSelectionToggle(event, itemId, false);\n        });\n      } else if (newSelectedItems !== models.selectedItems.value) {\n        if (models.selectedItems.value != null) {\n          params.onItemSelectionToggle(event, models.selectedItems.value, false);\n        }\n        if (newSelectedItems != null) {\n          params.onItemSelectionToggle(event, newSelectedItems, true);\n        }\n      }\n    }\n    if (params.onSelectedItemsChange) {\n      params.onSelectedItemsChange(event, newSelectedItems);\n    }\n    models.selectedItems.setControlledValue(newSelectedItems);\n  };\n  const isItemSelected = itemId => selectedItemsMap.has(itemId);\n  const selectItem = ({\n    event,\n    itemId,\n    keepExistingSelection = false,\n    shouldBeSelected\n  }) => {\n    if (params.disableSelection) {\n      return;\n    }\n    let newSelected;\n    if (keepExistingSelection) {\n      const cleanSelectedItems = convertSelectedItemsToArray(models.selectedItems.value);\n      const isSelectedBefore = instance.isItemSelected(itemId);\n      if (isSelectedBefore && (shouldBeSelected === false || shouldBeSelected == null)) {\n        newSelected = cleanSelectedItems.filter(id => id !== itemId);\n      } else if (!isSelectedBefore && (shouldBeSelected === true || shouldBeSelected == null)) {\n        newSelected = [itemId].concat(cleanSelectedItems);\n      } else {\n        newSelected = cleanSelectedItems;\n      }\n    } else {\n      // eslint-disable-next-line no-lonely-if\n      if (shouldBeSelected === false || shouldBeSelected == null && instance.isItemSelected(itemId)) {\n        newSelected = params.multiSelect ? [] : null;\n      } else {\n        newSelected = params.multiSelect ? [itemId] : itemId;\n      }\n    }\n    setSelectedItems(event, newSelected);\n    lastSelectedItem.current = itemId;\n    lastSelectedRange.current = {};\n  };\n  const selectRange = (event, [start, end]) => {\n    if (params.disableSelection || !params.multiSelect) {\n      return;\n    }\n    let newSelectedItems = convertSelectedItemsToArray(models.selectedItems.value).slice();\n\n    // If the last selection was a range selection,\n    // remove the items that were part of the last range from the model\n    if (Object.keys(lastSelectedRange.current).length > 0) {\n      newSelectedItems = newSelectedItems.filter(id => !lastSelectedRange.current[id]);\n    }\n\n    // Add to the model the items that are part of the new range and not already part of the model.\n    const selectedItemsLookup = getLookupFromArray(newSelectedItems);\n    const range = getNonDisabledItemsInRange(instance, start, end);\n    const itemsToAddToModel = range.filter(id => !selectedItemsLookup[id]);\n    newSelectedItems = newSelectedItems.concat(itemsToAddToModel);\n    setSelectedItems(event, newSelectedItems);\n    lastSelectedRange.current = getLookupFromArray(range);\n  };\n  const expandSelectionRange = (event, itemId) => {\n    if (lastSelectedItem.current != null) {\n      const [start, end] = findOrderInTremauxTree(instance, itemId, lastSelectedItem.current);\n      selectRange(event, [start, end]);\n    }\n  };\n  const selectRangeFromStartToItem = (event, itemId) => {\n    selectRange(event, [getFirstNavigableItem(instance), itemId]);\n  };\n  const selectRangeFromItemToEnd = (event, itemId) => {\n    selectRange(event, [itemId, getLastNavigableItem(instance)]);\n  };\n  const selectAllNavigableItems = event => {\n    if (params.disableSelection || !params.multiSelect) {\n      return;\n    }\n    const navigableItems = getAllNavigableItems(instance);\n    setSelectedItems(event, navigableItems);\n    lastSelectedRange.current = getLookupFromArray(navigableItems);\n  };\n  const selectItemFromArrowNavigation = (event, currentItem, nextItem) => {\n    if (params.disableSelection || !params.multiSelect) {\n      return;\n    }\n    let newSelectedItems = convertSelectedItemsToArray(models.selectedItems.value).slice();\n    if (Object.keys(lastSelectedRange.current).length === 0) {\n      newSelectedItems.push(nextItem);\n      lastSelectedRange.current = {\n        [currentItem]: true,\n        [nextItem]: true\n      };\n    } else {\n      if (!lastSelectedRange.current[currentItem]) {\n        lastSelectedRange.current = {};\n      }\n      if (lastSelectedRange.current[nextItem]) {\n        newSelectedItems = newSelectedItems.filter(id => id !== currentItem);\n        delete lastSelectedRange.current[currentItem];\n      } else {\n        newSelectedItems.push(nextItem);\n        lastSelectedRange.current[nextItem] = true;\n      }\n    }\n    setSelectedItems(event, newSelectedItems);\n  };\n  return {\n    getRootProps: () => ({\n      'aria-multiselectable': params.multiSelect\n    }),\n    publicAPI: {\n      selectItem\n    },\n    instance: {\n      isItemSelected,\n      selectItem,\n      selectAllNavigableItems,\n      expandSelectionRange,\n      selectRangeFromStartToItem,\n      selectRangeFromItemToEnd,\n      selectItemFromArrowNavigation\n    },\n    contextValue: {\n      selection: {\n        multiSelect: params.multiSelect,\n        checkboxSelection: params.checkboxSelection,\n        disableSelection: params.disableSelection\n      }\n    }\n  };\n};\nuseTreeViewSelection.models = {\n  selectedItems: {\n    getDefaultValue: params => params.defaultSelectedItems\n  }\n};\nconst DEFAULT_SELECTED_ITEMS = [];\nuseTreeViewSelection.getDefaultizedParams = params => _extends({}, params, {\n  disableSelection: params.disableSelection ?? false,\n  multiSelect: params.multiSelect ?? false,\n  checkboxSelection: params.checkboxSelection ?? false,\n  defaultSelectedItems: params.defaultSelectedItems ?? (params.multiSelect ? DEFAULT_SELECTED_ITEMS : null)\n});\nuseTreeViewSelection.params = {\n  disableSelection: true,\n  multiSelect: true,\n  checkboxSelection: true,\n  defaultSelectedItems: true,\n  selectedItems: true,\n  onSelectedItemsChange: true,\n  onItemSelectionToggle: true\n};", "const getLastNavigableItemInArray = (instance, items) => {\n  // Equivalent to Array.prototype.findLastIndex\n  let itemIndex = items.length - 1;\n  while (itemIndex >= 0 && !instance.isItemNavigable(items[itemIndex])) {\n    itemIndex -= 1;\n  }\n  if (itemIndex === -1) {\n    return undefined;\n  }\n  return items[itemIndex];\n};\nexport const getPreviousNavigableItem = (instance, itemId) => {\n  const itemMeta = instance.getItemMeta(itemId);\n  const siblings = instance.getItemOrderedChildrenIds(itemMeta.parentId);\n  const itemIndex = instance.getItemIndex(itemId);\n\n  // TODO: What should we do if the parent is not navigable?\n  if (itemIndex === 0) {\n    return itemMeta.parentId;\n  }\n\n  // Finds the previous navigable sibling.\n  let previousNavigableSiblingIndex = itemIndex - 1;\n  while (!instance.isItemNavigable(siblings[previousNavigableSiblingIndex]) && previousNavigableSiblingIndex >= 0) {\n    previousNavigableSiblingIndex -= 1;\n  }\n  if (previousNavigableSiblingIndex === -1) {\n    // If we are at depth 0, then it means all the items above the current item are not navigable.\n    if (itemMeta.parentId == null) {\n      return null;\n    }\n\n    // Otherwise, we can try to go up a level and find the previous navigable item.\n    return getPreviousNavigableItem(instance, itemMeta.parentId);\n  }\n\n  // Finds the last navigable ancestor of the previous navigable sibling.\n  let currentItemId = siblings[previousNavigableSiblingIndex];\n  let lastNavigableChild = getLastNavigableItemInArray(instance, instance.getItemOrderedChildrenIds(currentItemId));\n  while (instance.isItemExpanded(currentItemId) && lastNavigableChild != null) {\n    currentItemId = lastNavigableChild;\n    lastNavigableChild = instance.getItemOrderedChildrenIds(currentItemId).find(instance.isItemNavigable);\n  }\n  return currentItemId;\n};\nexport const getNextNavigableItem = (instance, itemId) => {\n  // If the item is expanded and has some navigable children, return the first of them.\n  if (instance.isItemExpanded(itemId)) {\n    const firstNavigableChild = instance.getItemOrderedChildrenIds(itemId).find(instance.isItemNavigable);\n    if (firstNavigableChild != null) {\n      return firstNavigableChild;\n    }\n  }\n  let itemMeta = instance.getItemMeta(itemId);\n  while (itemMeta != null) {\n    // Try to find the first navigable sibling after the current item.\n    const siblings = instance.getItemOrderedChildrenIds(itemMeta.parentId);\n    const currentItemIndex = instance.getItemIndex(itemMeta.id);\n    if (currentItemIndex < siblings.length - 1) {\n      let nextItemIndex = currentItemIndex + 1;\n      while (!instance.isItemNavigable(siblings[nextItemIndex]) && nextItemIndex < siblings.length - 1) {\n        nextItemIndex += 1;\n      }\n      if (instance.isItemNavigable(siblings[nextItemIndex])) {\n        return siblings[nextItemIndex];\n      }\n    }\n\n    // If the sibling does not exist, go up a level to the parent and try again.\n    itemMeta = instance.getItemMeta(itemMeta.parentId);\n  }\n  return null;\n};\nexport const getLastNavigableItem = instance => {\n  let itemId = null;\n  while (itemId == null || instance.isItemExpanded(itemId)) {\n    const children = instance.getItemOrderedChildrenIds(itemId);\n    const lastNavigableChild = getLastNavigableItemInArray(instance, children);\n\n    // The item has no navigable children.\n    if (lastNavigableChild == null) {\n      return itemId;\n    }\n    itemId = lastNavigableChild;\n  }\n  return itemId;\n};\nexport const getFirstNavigableItem = instance => instance.getItemOrderedChildrenIds(null).find(instance.isItemNavigable);\n\n/**\n * This is used to determine the start and end of a selection range so\n * we can get the items between the two border items.\n *\n * It finds the items' common ancestor using\n * a naive implementation of a lowest common ancestor algorithm\n * (https://en.wikipedia.org/wiki/Lowest_common_ancestor).\n * Then compares the ancestor's 2 children that are ancestors of itemA and ItemB\n * so we can compare their indexes to work out which item comes first in a depth first search.\n * (https://en.wikipedia.org/wiki/Depth-first_search)\n *\n * Another way to put it is which item is shallower in a trémaux tree\n * https://en.wikipedia.org/wiki/Tr%C3%A9maux_tree\n */\nexport const findOrderInTremauxTree = (instance, itemAId, itemBId) => {\n  if (itemAId === itemBId) {\n    return [itemAId, itemBId];\n  }\n  const itemMetaA = instance.getItemMeta(itemAId);\n  const itemMetaB = instance.getItemMeta(itemBId);\n  if (itemMetaA.parentId === itemMetaB.id || itemMetaB.parentId === itemMetaA.id) {\n    return itemMetaB.parentId === itemMetaA.id ? [itemMetaA.id, itemMetaB.id] : [itemMetaB.id, itemMetaA.id];\n  }\n  const aFamily = [itemMetaA.id];\n  const bFamily = [itemMetaB.id];\n  let aAncestor = itemMetaA.parentId;\n  let bAncestor = itemMetaB.parentId;\n  let aAncestorIsCommon = bFamily.indexOf(aAncestor) !== -1;\n  let bAncestorIsCommon = aFamily.indexOf(bAncestor) !== -1;\n  let continueA = true;\n  let continueB = true;\n  while (!bAncestorIsCommon && !aAncestorIsCommon) {\n    if (continueA) {\n      aFamily.push(aAncestor);\n      aAncestorIsCommon = bFamily.indexOf(aAncestor) !== -1;\n      continueA = aAncestor !== null;\n      if (!aAncestorIsCommon && continueA) {\n        aAncestor = instance.getItemMeta(aAncestor).parentId;\n      }\n    }\n    if (continueB && !aAncestorIsCommon) {\n      bFamily.push(bAncestor);\n      bAncestorIsCommon = aFamily.indexOf(bAncestor) !== -1;\n      continueB = bAncestor !== null;\n      if (!bAncestorIsCommon && continueB) {\n        bAncestor = instance.getItemMeta(bAncestor).parentId;\n      }\n    }\n  }\n  const commonAncestor = aAncestorIsCommon ? aAncestor : bAncestor;\n  const ancestorFamily = instance.getItemOrderedChildrenIds(commonAncestor);\n  const aSide = aFamily[aFamily.indexOf(commonAncestor) - 1];\n  const bSide = bFamily[bFamily.indexOf(commonAncestor) - 1];\n  return ancestorFamily.indexOf(aSide) < ancestorFamily.indexOf(bSide) ? [itemAId, itemBId] : [itemBId, itemAId];\n};\nexport const getNonDisabledItemsInRange = (instance, itemAId, itemBId) => {\n  const getNextItem = itemId => {\n    // If the item is expanded and has some children, return the first of them.\n    if (instance.isItemExpandable(itemId) && instance.isItemExpanded(itemId)) {\n      return instance.getItemOrderedChildrenIds(itemId)[0];\n    }\n    let itemMeta = instance.getItemMeta(itemId);\n    while (itemMeta != null) {\n      // Try to find the first navigable sibling after the current item.\n      const siblings = instance.getItemOrderedChildrenIds(itemMeta.parentId);\n      const currentItemIndex = instance.getItemIndex(itemMeta.id);\n      if (currentItemIndex < siblings.length - 1) {\n        return siblings[currentItemIndex + 1];\n      }\n\n      // If the item is the last of its siblings, go up a level to the parent and try again.\n      itemMeta = instance.getItemMeta(itemMeta.parentId);\n    }\n    throw new Error('Invalid range');\n  };\n  const [first, last] = findOrderInTremauxTree(instance, itemAId, itemBId);\n  const items = [first];\n  let current = first;\n  while (current !== last) {\n    current = getNextItem(current);\n    if (!instance.isItemDisabled(current)) {\n      items.push(current);\n    }\n  }\n  return items;\n};\nexport const getAllNavigableItems = instance => {\n  let item = getFirstNavigableItem(instance);\n  const navigableItems = [];\n  while (item != null) {\n    navigableItems.push(item);\n    item = getNextNavigableItem(instance, item);\n  }\n  return navigableItems;\n};\n\n/**\n * Checks if the target is in a descendant of this item.\n * This can prevent from firing some logic on the ancestors on the interacted item when the event handler is on the root.\n * @param {HTMLElement} target The target to check\n * @param {HTMLElement | null} itemRoot The root of the item to check if the event target is in its descendants\n * @returns {boolean} Whether the target is in a descendant of this item\n */\nexport const isTargetInDescendants = (target, itemRoot) => {\n  return itemRoot !== target.closest('*[role=\"treeitem\"]');\n};", "/**\n * Transform the `selectedItems` model to be an array if it was a string or null.\n * @param {string[] | string | null} model The raw model.\n * @returns {string[]} The converted model.\n */\nexport const convertSelectedItemsToArray = model => {\n  if (Array.isArray(model)) {\n    return model;\n  }\n  if (model != null) {\n    return [model];\n  }\n  return [];\n};\nexport const getLookupFromArray = array => {\n  const lookup = {};\n  array.forEach(itemId => {\n    lookup[itemId] = true;\n  });\n  return lookup;\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport { useInstanceEventHandler } from \"../../hooks/useInstanceEventHandler.js\";\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { getFirstNavigableItem } from \"../../utils/tree.js\";\nimport { convertSelectedItemsToArray } from \"../useTreeViewSelection/useTreeViewSelection.utils.js\";\nconst useDefaultFocusableItemId = (instance, selectedItems) => {\n  let tabbableItemId = convertSelectedItemsToArray(selectedItems).find(itemId => {\n    if (!instance.isItemNavigable(itemId)) {\n      return false;\n    }\n    const itemMeta = instance.getItemMeta(itemId);\n    return itemMeta && (itemMeta.parentId == null || instance.isItemExpanded(itemMeta.parentId));\n  });\n  if (tabbableItemId == null) {\n    tabbableItemId = getFirstNavigableItem(instance);\n  }\n  return tabbableItemId;\n};\nexport const useTreeViewFocus = ({\n  instance,\n  params,\n  state,\n  setState,\n  models,\n  rootRef\n}) => {\n  const defaultFocusableItemId = useDefaultFocusableItemId(instance, models.selectedItems.value);\n  const setFocusedItemId = useEventCallback(itemId => {\n    const cleanItemId = typeof itemId === 'function' ? itemId(state.focusedItemId) : itemId;\n    if (state.focusedItemId !== cleanItemId) {\n      setState(prevState => _extends({}, prevState, {\n        focusedItemId: cleanItemId\n      }));\n    }\n  });\n  const isTreeViewFocused = React.useCallback(() => !!rootRef.current && rootRef.current.contains(getActiveElement(ownerDocument(rootRef.current))), [rootRef]);\n  const isItemFocused = React.useCallback(itemId => state.focusedItemId === itemId && isTreeViewFocused(), [state.focusedItemId, isTreeViewFocused]);\n  const isItemVisible = itemId => {\n    const itemMeta = instance.getItemMeta(itemId);\n    return itemMeta && (itemMeta.parentId == null || instance.isItemExpanded(itemMeta.parentId));\n  };\n  const innerFocusItem = (event, itemId) => {\n    const itemElement = instance.getItemDOMElement(itemId);\n    if (itemElement) {\n      itemElement.focus();\n    }\n    setFocusedItemId(itemId);\n    if (params.onItemFocus) {\n      params.onItemFocus(event, itemId);\n    }\n  };\n  const focusItem = useEventCallback((event, itemId) => {\n    // If we receive an itemId, and it is visible, the focus will be set to it\n    if (isItemVisible(itemId)) {\n      innerFocusItem(event, itemId);\n    }\n  });\n  const removeFocusedItem = useEventCallback(() => {\n    if (state.focusedItemId == null) {\n      return;\n    }\n    const itemMeta = instance.getItemMeta(state.focusedItemId);\n    if (itemMeta) {\n      const itemElement = document.getElementById(instance.getTreeItemIdAttribute(state.focusedItemId, itemMeta.idAttribute));\n      if (itemElement) {\n        itemElement.blur();\n      }\n    }\n    setFocusedItemId(null);\n  });\n  const canItemBeTabbed = itemId => itemId === defaultFocusableItemId;\n  useInstanceEventHandler(instance, 'removeItem', ({\n    id\n  }) => {\n    if (state.focusedItemId === id) {\n      innerFocusItem(null, defaultFocusableItemId);\n    }\n  });\n  const createRootHandleFocus = otherHandlers => event => {\n    otherHandlers.onFocus?.(event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n\n    // if the event bubbled (which is React specific) we don't want to steal focus\n    if (event.target === event.currentTarget) {\n      innerFocusItem(event, defaultFocusableItemId);\n    }\n  };\n  return {\n    getRootProps: otherHandlers => ({\n      onFocus: createRootHandleFocus(otherHandlers)\n    }),\n    publicAPI: {\n      focusItem\n    },\n    instance: {\n      isItemFocused,\n      canItemBeTabbed,\n      focusItem,\n      removeFocusedItem\n    }\n  };\n};\nuseTreeViewFocus.getInitialState = () => ({\n  focusedItemId: null\n});\nuseTreeViewFocus.params = {\n  onItemFocus: true\n};", "import * as React from 'react';\nimport { TimerBasedCleanupTracking } from \"../utils/cleanupTracking/TimerBasedCleanupTracking.js\";\nimport { FinalizationRegistryBasedCleanupTracking } from \"../utils/cleanupTracking/FinalizationRegistryBasedCleanupTracking.js\";\n// We use class to make it easier to detect in heap snapshots by name\nclass ObjectToBeRetainedByReact {}\n\n// Based on https://github.com/Bnaya/use-dispose-uncommitted/blob/main/src/finalization-registry-based-impl.ts\n// Check https://github.com/facebook/react/issues/15317 to get more information\nexport function createUseInstanceEventHandler(registryContainer) {\n  let cleanupTokensCounter = 0;\n  return function useInstanceEventHandler(instance, eventName, handler) {\n    if (registryContainer.registry === null) {\n      registryContainer.registry = typeof FinalizationRegistry !== 'undefined' ? new FinalizationRegistryBasedCleanupTracking() : new TimerBasedCleanupTracking();\n    }\n    const [objectRetainedByReact] = React.useState(new ObjectToBeRetainedByReact());\n    const subscription = React.useRef(null);\n    const handlerRef = React.useRef();\n    handlerRef.current = handler;\n    const cleanupTokenRef = React.useRef(null);\n    if (!subscription.current && handlerRef.current) {\n      const enhancedHandler = (params, event) => {\n        if (!event.defaultMuiPrevented) {\n          handlerRef.current?.(params, event);\n        }\n      };\n      subscription.current = instance.$$subscribeEvent(eventName, enhancedHandler);\n      cleanupTokensCounter += 1;\n      cleanupTokenRef.current = {\n        cleanupToken: cleanupTokensCounter\n      };\n      registryContainer.registry.register(objectRetainedByReact,\n      // The callback below will be called once this reference stops being retained\n      () => {\n        subscription.current?.();\n        subscription.current = null;\n        cleanupTokenRef.current = null;\n      }, cleanupTokenRef.current);\n    } else if (!handlerRef.current && subscription.current) {\n      subscription.current();\n      subscription.current = null;\n      if (cleanupTokenRef.current) {\n        registryContainer.registry.unregister(cleanupTokenRef.current);\n        cleanupTokenRef.current = null;\n      }\n    }\n    React.useEffect(() => {\n      if (!subscription.current && handlerRef.current) {\n        const enhancedHandler = (params, event) => {\n          if (!event.defaultMuiPrevented) {\n            handlerRef.current?.(params, event);\n          }\n        };\n        subscription.current = instance.$$subscribeEvent(eventName, enhancedHandler);\n      }\n      if (cleanupTokenRef.current && registryContainer.registry) {\n        // If the effect was called, it means that this render was committed\n        // so we can trust the cleanup function to remove the listener.\n        registryContainer.registry.unregister(cleanupTokenRef.current);\n        cleanupTokenRef.current = null;\n      }\n      return () => {\n        subscription.current?.();\n        subscription.current = null;\n      };\n    }, [instance, eventName]);\n  };\n}\nconst registryContainer = {\n  registry: null\n};\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const unstable_resetCleanupTracking = () => {\n  registryContainer.registry?.reset();\n  registryContainer.registry = null;\n};\nexport const useInstanceEventHandler = createUseInstanceEventHandler(registryContainer);", "// If no effect ran after this amount of time, we assume that the render was not committed by React\nconst CLEANUP_TIMER_LOOP_MILLIS = 1000;\nexport class TimerBasedCleanupTracking {\n  constructor(timeout = CLEANUP_TIMER_LOOP_MILLIS) {\n    this.timeouts = new Map();\n    this.cleanupTimeout = CLEANUP_TIMER_LOOP_MILLIS;\n    this.cleanupTimeout = timeout;\n  }\n  register(object, unsubscribe, unregisterToken) {\n    if (!this.timeouts) {\n      this.timeouts = new Map();\n    }\n    const timeout = setTimeout(() => {\n      if (typeof unsubscribe === 'function') {\n        unsubscribe();\n      }\n      this.timeouts.delete(unregisterToken.cleanupToken);\n    }, this.cleanupTimeout);\n    this.timeouts.set(unregisterToken.cleanupToken, timeout);\n  }\n  unregister(unregisterToken) {\n    const timeout = this.timeouts.get(unregisterToken.cleanupToken);\n    if (timeout) {\n      this.timeouts.delete(unregisterToken.cleanupToken);\n      clearTimeout(timeout);\n    }\n  }\n  reset() {\n    if (this.timeouts) {\n      this.timeouts.forEach((value, key) => {\n        this.unregister({\n          cleanupToken: key\n        });\n      });\n      this.timeouts = undefined;\n    }\n  }\n}", "export class FinalizationRegistryBasedCleanupTracking {\n  constructor() {\n    this.registry = new FinalizationRegistry(unsubscribe => {\n      if (typeof unsubscribe === 'function') {\n        unsubscribe();\n      }\n    });\n  }\n  register(object, unsubscribe, unregisterToken) {\n    this.registry.register(object, unsubscribe, unregisterToken);\n  }\n  unregister(unregisterToken) {\n    this.registry.unregister(unregisterToken);\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  reset() {}\n}", "// https://www.abeautifulsite.net/posts/finding-the-active-element-in-a-shadow-root/\nexport const getActiveElement = (root = document) => {\n  const activeEl = root.activeElement;\n  if (!activeEl) {\n    return null;\n  }\n  if (activeEl.shadowRoot) {\n    return getActiveElement(activeEl.shadowRoot);\n  }\n  return activeEl;\n};\n\n// TODO, eventually replaces this function with CSS.escape, once available in jsdom, either added manually or built in\n// https://github.com/jsdom/jsdom/issues/1550#issuecomment-236734471\nexport function escapeOperandAttributeSelector(operand) {\n  return operand.replace(/[\"\\\\]/g, '\\\\$&');\n}", "import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { getFirstNavigableItem, getLastNavigableItem, getNextNavigableItem, getPreviousNavigableItem, isTargetInDescendants } from \"../../utils/tree.js\";\nimport { hasPlugin } from \"../../utils/plugins.js\";\nimport { useTreeViewLabel } from \"../useTreeViewLabel/index.js\";\nfunction isPrintableCharacter(string) {\n  return !!string && string.length === 1 && !!string.match(/\\S/);\n}\nexport const useTreeViewKeyboardNavigation = ({\n  instance,\n  params,\n  state\n}) => {\n  const isRtl = useRtl();\n  const firstCharMap = React.useRef({});\n  const updateFirstCharMap = useEventCallback(callback => {\n    firstCharMap.current = callback(firstCharMap.current);\n  });\n  React.useEffect(() => {\n    if (instance.areItemUpdatesPrevented()) {\n      return;\n    }\n    const newFirstCharMap = {};\n    const processItem = item => {\n      newFirstCharMap[item.id] = item.label.substring(0, 1).toLowerCase();\n    };\n    Object.values(state.items.itemMetaMap).forEach(processItem);\n    firstCharMap.current = newFirstCharMap;\n  }, [state.items.itemMetaMap, params.getItemId, instance]);\n  const getFirstMatchingItem = (itemId, query) => {\n    const cleanQuery = query.toLowerCase();\n    const getNextItem = itemIdToCheck => {\n      const nextItemId = getNextNavigableItem(instance, itemIdToCheck);\n      // We reached the end of the tree, check from the beginning\n      if (nextItemId === null) {\n        return getFirstNavigableItem(instance);\n      }\n      return nextItemId;\n    };\n    let matchingItemId = null;\n    let currentItemId = getNextItem(itemId);\n    const checkedItems = {};\n    // The \"!checkedItems[currentItemId]\" condition avoids an infinite loop when there is no matching item.\n    while (matchingItemId == null && !checkedItems[currentItemId]) {\n      if (firstCharMap.current[currentItemId] === cleanQuery) {\n        matchingItemId = currentItemId;\n      } else {\n        checkedItems[currentItemId] = true;\n        currentItemId = getNextItem(currentItemId);\n      }\n    }\n    return matchingItemId;\n  };\n  const canToggleItemSelection = itemId => !params.disableSelection && !instance.isItemDisabled(itemId);\n  const canToggleItemExpansion = itemId => {\n    return !instance.isItemDisabled(itemId) && instance.isItemExpandable(itemId);\n  };\n\n  // ARIA specification: https://www.w3.org/WAI/ARIA/apg/patterns/treeview/#keyboardinteraction\n  const handleItemKeyDown = (event, itemId) => {\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (event.altKey || isTargetInDescendants(event.target, event.currentTarget)) {\n      return;\n    }\n    const ctrlPressed = event.ctrlKey || event.metaKey;\n    const key = event.key;\n\n    // eslint-disable-next-line default-case\n    switch (true) {\n      // Select the item when pressing \"Space\"\n      case key === ' ' && canToggleItemSelection(itemId):\n        {\n          event.preventDefault();\n          if (params.multiSelect && event.shiftKey) {\n            instance.expandSelectionRange(event, itemId);\n          } else {\n            instance.selectItem({\n              event,\n              itemId,\n              keepExistingSelection: params.multiSelect,\n              shouldBeSelected: params.multiSelect ? undefined : true\n            });\n          }\n          break;\n        }\n\n      // If the focused item has children, we expand it.\n      // If the focused item has no children, we select it.\n      case key === 'Enter':\n        {\n          if (hasPlugin(instance, useTreeViewLabel) && instance.isItemEditable(itemId) && !instance.isItemBeingEdited(itemId)) {\n            instance.setEditedItemId(itemId);\n          } else if (canToggleItemExpansion(itemId)) {\n            instance.toggleItemExpansion(event, itemId);\n            event.preventDefault();\n          } else if (canToggleItemSelection(itemId)) {\n            if (params.multiSelect) {\n              event.preventDefault();\n              instance.selectItem({\n                event,\n                itemId,\n                keepExistingSelection: true\n              });\n            } else if (!instance.isItemSelected(itemId)) {\n              instance.selectItem({\n                event,\n                itemId\n              });\n              event.preventDefault();\n            }\n          }\n          break;\n        }\n\n      // Focus the next focusable item\n      case key === 'ArrowDown':\n        {\n          const nextItem = getNextNavigableItem(instance, itemId);\n          if (nextItem) {\n            event.preventDefault();\n            instance.focusItem(event, nextItem);\n\n            // Multi select behavior when pressing Shift + ArrowDown\n            // Toggles the selection state of the next item\n            if (params.multiSelect && event.shiftKey && canToggleItemSelection(nextItem)) {\n              instance.selectItemFromArrowNavigation(event, itemId, nextItem);\n            }\n          }\n          break;\n        }\n\n      // Focuses the previous focusable item\n      case key === 'ArrowUp':\n        {\n          const previousItem = getPreviousNavigableItem(instance, itemId);\n          if (previousItem) {\n            event.preventDefault();\n            instance.focusItem(event, previousItem);\n\n            // Multi select behavior when pressing Shift + ArrowUp\n            // Toggles the selection state of the previous item\n            if (params.multiSelect && event.shiftKey && canToggleItemSelection(previousItem)) {\n              instance.selectItemFromArrowNavigation(event, itemId, previousItem);\n            }\n          }\n          break;\n        }\n\n      // If the focused item is expanded, we move the focus to its first child\n      // If the focused item is collapsed and has children, we expand it\n      case key === 'ArrowRight' && !isRtl || key === 'ArrowLeft' && isRtl:\n        {\n          if (instance.isItemExpanded(itemId)) {\n            const nextItemId = getNextNavigableItem(instance, itemId);\n            if (nextItemId) {\n              instance.focusItem(event, nextItemId);\n              event.preventDefault();\n            }\n          } else if (canToggleItemExpansion(itemId)) {\n            instance.toggleItemExpansion(event, itemId);\n            event.preventDefault();\n          }\n          break;\n        }\n\n      // If the focused item is expanded, we collapse it\n      // If the focused item is collapsed and has a parent, we move the focus to this parent\n      case key === 'ArrowLeft' && !isRtl || key === 'ArrowRight' && isRtl:\n        {\n          if (canToggleItemExpansion(itemId) && instance.isItemExpanded(itemId)) {\n            instance.toggleItemExpansion(event, itemId);\n            event.preventDefault();\n          } else {\n            const parent = instance.getItemMeta(itemId).parentId;\n            if (parent) {\n              instance.focusItem(event, parent);\n              event.preventDefault();\n            }\n          }\n          break;\n        }\n\n      // Focuses the first item in the tree\n      case key === 'Home':\n        {\n          // Multi select behavior when pressing Ctrl + Shift + Home\n          // Selects the focused item and all items up to the first item.\n          if (canToggleItemSelection(itemId) && params.multiSelect && ctrlPressed && event.shiftKey) {\n            instance.selectRangeFromStartToItem(event, itemId);\n          } else {\n            instance.focusItem(event, getFirstNavigableItem(instance));\n          }\n          event.preventDefault();\n          break;\n        }\n\n      // Focuses the last item in the tree\n      case key === 'End':\n        {\n          // Multi select behavior when pressing Ctrl + Shirt + End\n          // Selects the focused item and all the items down to the last item.\n          if (canToggleItemSelection(itemId) && params.multiSelect && ctrlPressed && event.shiftKey) {\n            instance.selectRangeFromItemToEnd(event, itemId);\n          } else {\n            instance.focusItem(event, getLastNavigableItem(instance));\n          }\n          event.preventDefault();\n          break;\n        }\n\n      // Expand all siblings that are at the same level as the focused item\n      case key === '*':\n        {\n          instance.expandAllSiblings(event, itemId);\n          event.preventDefault();\n          break;\n        }\n\n      // Multi select behavior when pressing Ctrl + a\n      // Selects all the items\n      case key === 'a' && ctrlPressed && params.multiSelect && !params.disableSelection:\n        {\n          instance.selectAllNavigableItems(event);\n          event.preventDefault();\n          break;\n        }\n\n      // Type-ahead\n      // TODO: Support typing multiple characters\n      case !ctrlPressed && !event.shiftKey && isPrintableCharacter(key):\n        {\n          const matchingItem = getFirstMatchingItem(itemId, key);\n          if (matchingItem != null) {\n            instance.focusItem(event, matchingItem);\n            event.preventDefault();\n          }\n          break;\n        }\n    }\n  };\n  return {\n    instance: {\n      updateFirstCharMap,\n      handleItemKeyDown\n    }\n  };\n};\nuseTreeViewKeyboardNavigation.params = {};", "export const useTreeViewIcons = ({\n  slots,\n  slotProps\n}) => {\n  return {\n    contextValue: {\n      icons: {\n        slots: {\n          collapseIcon: slots.collapseIcon,\n          expandIcon: slots.expandIcon,\n          endIcon: slots.endIcon\n        },\n        slotProps: {\n          collapseIcon: slotProps.collapseIcon,\n          expandIcon: slotProps.expandIcon,\n          endIcon: slotProps.endIcon\n        }\n      }\n    }\n  };\n};\nuseTreeViewIcons.params = {};", "import { useTreeViewItems } from \"../internals/plugins/useTreeViewItems/index.js\";\nimport { useTreeViewExpansion } from \"../internals/plugins/useTreeViewExpansion/index.js\";\nimport { useTreeViewSelection } from \"../internals/plugins/useTreeViewSelection/index.js\";\nimport { useTreeViewFocus } from \"../internals/plugins/useTreeViewFocus/index.js\";\nimport { useTreeViewKeyboardNavigation } from \"../internals/plugins/useTreeViewKeyboardNavigation/index.js\";\nimport { useTreeViewIcons } from \"../internals/plugins/useTreeViewIcons/index.js\";\nimport { useTreeViewLabel } from \"../internals/plugins/useTreeViewLabel/index.js\";\nexport const RICH_TREE_VIEW_PLUGINS = [useTreeViewItems, useTreeViewExpansion, useTreeViewSelection, useTreeViewFocus, useTreeViewKeyboardNavigation, useTreeViewIcons, useTreeViewLabel];\n\n// We can't infer this type from the plugin, otherwise we would lose the generics.", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"slots\", \"slotProps\", \"ContentComponent\", \"ContentProps\", \"itemId\", \"id\", \"label\", \"onClick\", \"onMouseDown\", \"onFocus\", \"onBlur\", \"onKeyDown\"],\n  _excluded2 = [\"ownerState\"],\n  _excluded3 = [\"ownerState\"],\n  _excluded4 = [\"ownerState\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport Collapse from '@mui/material/Collapse';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { shouldForwardProp } from '@mui/system/createStyled';\nimport { alpha } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport unsupportedProp from '@mui/utils/unsupportedProp';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport { styled, createUseThemeProps } from \"../internals/zero-styled/index.js\";\nimport { TreeItemContent } from \"./TreeItemContent.js\";\nimport { treeItemClasses, getTreeItemUtilityClass } from \"./treeItemClasses.js\";\nimport { useTreeViewContext } from \"../internals/TreeViewProvider/index.js\";\nimport { TreeViewCollapseIcon, TreeViewExpandIcon } from \"../icons/index.js\";\nimport { TreeItem2Provider } from \"../TreeItem2Provider/index.js\";\nimport { TreeViewItemDepthContext } from \"../internals/TreeViewItemDepthContext/index.js\";\nimport { useTreeItemState } from \"./useTreeItemState.js\";\nimport { isTargetInDescendants } from \"../internals/utils/tree.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useThemeProps = createUseThemeProps('MuiTreeItem');\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    content: ['content'],\n    expanded: ['expanded'],\n    selected: ['selected'],\n    focused: ['focused'],\n    disabled: ['disabled'],\n    iconContainer: ['iconContainer'],\n    checkbox: ['checkbox'],\n    label: ['label'],\n    labelInput: ['labelInput'],\n    editing: ['editing'],\n    editable: ['editable'],\n    groupTransition: ['groupTransition']\n  };\n  return composeClasses(slots, getTreeItemUtilityClass, classes);\n};\nconst TreeItemRoot = styled('li', {\n  name: 'MuiTreeItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  listStyle: 'none',\n  margin: 0,\n  padding: 0,\n  outline: 0\n});\nconst StyledTreeItemContent = styled(TreeItemContent, {\n  name: 'MuiTreeItem',\n  slot: 'Content',\n  overridesResolver: (props, styles) => {\n    return [styles.content, styles.iconContainer && {\n      [`& .${treeItemClasses.iconContainer}`]: styles.iconContainer\n    }, styles.label && {\n      [`& .${treeItemClasses.label}`]: styles.label\n    }];\n  },\n  shouldForwardProp: prop => shouldForwardProp(prop) && prop !== 'indentationAtItemLevel'\n})(({\n  theme\n}) => ({\n  padding: theme.spacing(0.5, 1),\n  borderRadius: theme.shape.borderRadius,\n  width: '100%',\n  boxSizing: 'border-box',\n  // prevent width + padding to overflow\n  position: 'relative',\n  display: 'flex',\n  alignItems: 'center',\n  gap: theme.spacing(1),\n  cursor: 'pointer',\n  WebkitTapHighlightColor: 'transparent',\n  '&:hover': {\n    backgroundColor: (theme.vars || theme).palette.action.hover,\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  },\n  [`&.${treeItemClasses.disabled}`]: {\n    opacity: (theme.vars || theme).palette.action.disabledOpacity,\n    backgroundColor: 'transparent'\n  },\n  [`&.${treeItemClasses.focused}`]: {\n    backgroundColor: (theme.vars || theme).palette.action.focus\n  },\n  [`&.${treeItemClasses.selected}`]: {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),\n    '&:hover': {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity)\n      }\n    },\n    [`&.${treeItemClasses.focused}`]: {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n    }\n  },\n  [`& .${treeItemClasses.iconContainer}`]: {\n    width: 16,\n    display: 'flex',\n    flexShrink: 0,\n    justifyContent: 'center',\n    '& svg': {\n      fontSize: 18\n    }\n  },\n  [`& .${treeItemClasses.label}`]: _extends({\n    width: '100%',\n    boxSizing: 'border-box',\n    // prevent width + padding to overflow\n    // fixes overflow - see https://github.com/mui/material-ui/issues/27372\n    minWidth: 0,\n    position: 'relative'\n  }, theme.typography.body1),\n  [`& .${treeItemClasses.checkbox}`]: {\n    padding: 0\n  },\n  variants: [{\n    props: {\n      indentationAtItemLevel: true\n    },\n    style: {\n      paddingLeft: `calc(${theme.spacing(1)} + var(--TreeView-itemChildrenIndentation) * var(--TreeView-itemDepth))`\n    }\n  }]\n}));\nconst TreeItemGroup = styled(Collapse, {\n  name: 'MuiTreeItem',\n  slot: 'GroupTransition',\n  overridesResolver: (props, styles) => styles.groupTransition,\n  shouldForwardProp: prop => shouldForwardProp(prop) && prop !== 'indentationAtItemLevel'\n})({\n  margin: 0,\n  padding: 0,\n  paddingLeft: 'var(--TreeView-itemChildrenIndentation)',\n  variants: [{\n    props: {\n      indentationAtItemLevel: true\n    },\n    style: {\n      paddingLeft: 0\n    }\n  }]\n});\n\n/**\n *\n * Demos:\n *\n * - [Tree View](https://mui.com/x/react-tree-view/)\n *\n * API:\n *\n * - [TreeItem API](https://mui.com/x/api/tree-view/tree-item/)\n */\nexport const TreeItem = /*#__PURE__*/React.forwardRef(function TreeItem(inProps, inRef) {\n  const {\n    icons: contextIcons,\n    runItemPlugins,\n    items: {\n      disabledItemsFocusable,\n      indentationAtItemLevel\n    },\n    selection: {\n      multiSelect\n    },\n    expansion: {\n      expansionTrigger\n    },\n    instance\n  } = useTreeViewContext();\n  const depthContext = React.useContext(TreeViewItemDepthContext);\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTreeItem'\n  });\n  const {\n      children,\n      className,\n      slots: inSlots,\n      slotProps: inSlotProps,\n      ContentComponent = TreeItemContent,\n      ContentProps,\n      itemId,\n      id,\n      label,\n      onClick,\n      onMouseDown,\n      onBlur,\n      onKeyDown\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    expanded,\n    focused,\n    selected,\n    disabled,\n    editing,\n    handleExpansion,\n    handleCancelItemLabelEditing,\n    handleSaveItemLabel\n  } = useTreeItemState(itemId);\n  const {\n    contentRef,\n    rootRef,\n    propsEnhancers\n  } = runItemPlugins(props);\n  const rootRefObject = React.useRef(null);\n  const contentRefObject = React.useRef(null);\n  const handleRootRef = useForkRef(inRef, rootRef, rootRefObject);\n  const handleContentRef = useForkRef(ContentProps?.ref, contentRef, contentRefObject);\n  const slots = {\n    expandIcon: inSlots?.expandIcon ?? contextIcons.slots.expandIcon ?? TreeViewExpandIcon,\n    collapseIcon: inSlots?.collapseIcon ?? contextIcons.slots.collapseIcon ?? TreeViewCollapseIcon,\n    endIcon: inSlots?.endIcon ?? contextIcons.slots.endIcon,\n    icon: inSlots?.icon,\n    groupTransition: inSlots?.groupTransition\n  };\n  const isExpandable = reactChildren => {\n    if (Array.isArray(reactChildren)) {\n      return reactChildren.length > 0 && reactChildren.some(isExpandable);\n    }\n    return Boolean(reactChildren);\n  };\n  const expandable = isExpandable(children);\n  const ownerState = _extends({}, props, {\n    expanded,\n    focused,\n    selected,\n    disabled,\n    indentationAtItemLevel\n  });\n  const classes = useUtilityClasses(ownerState);\n  const GroupTransition = slots.groupTransition ?? undefined;\n  const groupTransitionProps = useSlotProps({\n    elementType: GroupTransition,\n    ownerState: {},\n    externalSlotProps: inSlotProps?.groupTransition,\n    additionalProps: _extends({\n      unmountOnExit: true,\n      in: expanded,\n      component: 'ul',\n      role: 'group'\n    }, indentationAtItemLevel ? {\n      indentationAtItemLevel: true\n    } : {}),\n    className: classes.groupTransition\n  });\n  const handleIconContainerClick = event => {\n    if (expansionTrigger === 'iconContainer') {\n      handleExpansion(event);\n    }\n  };\n  const ExpansionIcon = expanded ? slots.collapseIcon : slots.expandIcon;\n  const _useSlotProps = useSlotProps({\n      elementType: ExpansionIcon,\n      ownerState: {},\n      externalSlotProps: tempOwnerState => {\n        if (expanded) {\n          return _extends({}, resolveComponentProps(contextIcons.slotProps.collapseIcon, tempOwnerState), resolveComponentProps(inSlotProps?.collapseIcon, tempOwnerState));\n        }\n        return _extends({}, resolveComponentProps(contextIcons.slotProps.expandIcon, tempOwnerState), resolveComponentProps(inSlotProps?.expandIcon, tempOwnerState));\n      },\n      additionalProps: {\n        onClick: handleIconContainerClick\n      }\n    }),\n    expansionIconProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  const expansionIcon = expandable && !!ExpansionIcon ? /*#__PURE__*/_jsx(ExpansionIcon, _extends({}, expansionIconProps)) : null;\n  const DisplayIcon = expandable ? undefined : slots.endIcon;\n  const _useSlotProps2 = useSlotProps({\n      elementType: DisplayIcon,\n      ownerState: {},\n      externalSlotProps: tempOwnerState => {\n        if (expandable) {\n          return {};\n        }\n        return _extends({}, resolveComponentProps(contextIcons.slotProps.endIcon, tempOwnerState), resolveComponentProps(inSlotProps?.endIcon, tempOwnerState));\n      }\n    }),\n    displayIconProps = _objectWithoutPropertiesLoose(_useSlotProps2, _excluded3);\n  const displayIcon = DisplayIcon ? /*#__PURE__*/_jsx(DisplayIcon, _extends({}, displayIconProps)) : null;\n  const Icon = slots.icon;\n  const _useSlotProps3 = useSlotProps({\n      elementType: Icon,\n      ownerState: {},\n      externalSlotProps: inSlotProps?.icon\n    }),\n    iconProps = _objectWithoutPropertiesLoose(_useSlotProps3, _excluded4);\n  const icon = Icon ? /*#__PURE__*/_jsx(Icon, _extends({}, iconProps)) : null;\n  let ariaSelected;\n  if (multiSelect) {\n    ariaSelected = selected;\n  } else if (selected) {\n    /* single-selection trees unset aria-selected on un-selected items.\n     *\n     * If the tree does not support multiple selection, aria-selected\n     * is set to true for the selected item and it is not present on any other item in the tree.\n     * Source: https://www.w3.org/WAI/ARIA/apg/patterns/treeview/\n     */\n    ariaSelected = true;\n  }\n  function handleFocus(event) {\n    const canBeFocused = !disabled || disabledItemsFocusable;\n    if (!focused && canBeFocused && event.currentTarget === event.target) {\n      instance.focusItem(event, itemId);\n    }\n  }\n  function handleBlur(event) {\n    onBlur?.(event);\n    if (editing ||\n    // we can exit the editing state by clicking outside the input (within the tree item) or by pressing Enter or Escape -> we don't want to remove the focused item from the state in these cases\n    // we can also exit the editing state by clicking on the root itself -> want to remove the focused item from the state in this case\n    event.relatedTarget && isTargetInDescendants(event.relatedTarget, rootRefObject.current) && (event.target && event.target?.dataset?.element === 'labelInput' && isTargetInDescendants(event.target, rootRefObject.current) || event.relatedTarget?.dataset?.element === 'labelInput')) {\n      return;\n    }\n    instance.removeFocusedItem();\n  }\n  const handleKeyDown = event => {\n    onKeyDown?.(event);\n    if (event.target?.dataset?.element === 'labelInput') {\n      return;\n    }\n    instance.handleItemKeyDown(event, itemId);\n  };\n  const idAttribute = instance.getTreeItemIdAttribute(itemId, id);\n  const tabIndex = instance.canItemBeTabbed(itemId) ? 0 : -1;\n  const sharedPropsEnhancerParams = {\n    rootRefObject,\n    contentRefObject,\n    interactions: {\n      handleSaveItemLabel,\n      handleCancelItemLabelEditing\n    }\n  };\n  const enhancedRootProps = propsEnhancers.root?.(_extends({}, sharedPropsEnhancerParams, {\n    externalEventHandlers: extractEventHandlers(other)\n  })) ?? {};\n  const enhancedContentProps = propsEnhancers.content?.(_extends({}, sharedPropsEnhancerParams, {\n    externalEventHandlers: extractEventHandlers(ContentProps)\n  })) ?? {};\n  const enhancedDragAndDropOverlayProps = propsEnhancers.dragAndDropOverlay?.(_extends({}, sharedPropsEnhancerParams, {\n    externalEventHandlers: {}\n  })) ?? {};\n  const enhancedLabelInputProps = propsEnhancers.labelInput?.(_extends({}, sharedPropsEnhancerParams, {\n    externalEventHandlers: {}\n  })) ?? {};\n  return /*#__PURE__*/_jsx(TreeItem2Provider, {\n    itemId: itemId,\n    children: /*#__PURE__*/_jsxs(TreeItemRoot, _extends({\n      className: clsx(classes.root, className),\n      role: \"treeitem\",\n      \"aria-expanded\": expandable ? expanded : undefined,\n      \"aria-selected\": ariaSelected,\n      \"aria-disabled\": disabled || undefined,\n      id: idAttribute,\n      tabIndex: tabIndex\n    }, other, {\n      ownerState: ownerState,\n      onFocus: handleFocus,\n      onBlur: handleBlur,\n      onKeyDown: handleKeyDown,\n      ref: handleRootRef,\n      style: indentationAtItemLevel ? _extends({}, other.style, {\n        '--TreeView-itemDepth': typeof depthContext === 'function' ? depthContext(itemId) : depthContext\n      }) : other.style\n    }, enhancedRootProps, {\n      children: [/*#__PURE__*/_jsx(StyledTreeItemContent, _extends({\n        as: ContentComponent,\n        classes: {\n          root: classes.content,\n          expanded: classes.expanded,\n          selected: classes.selected,\n          focused: classes.focused,\n          disabled: classes.disabled,\n          editable: classes.editable,\n          editing: classes.editing,\n          iconContainer: classes.iconContainer,\n          label: classes.label,\n          labelInput: classes.labelInput,\n          checkbox: classes.checkbox\n        },\n        label: label,\n        itemId: itemId,\n        onClick: onClick,\n        onMouseDown: onMouseDown,\n        icon: icon,\n        expansionIcon: expansionIcon,\n        displayIcon: displayIcon,\n        ownerState: ownerState\n      }, ContentProps, enhancedContentProps, enhancedDragAndDropOverlayProps.action == null ? {} : {\n        dragAndDropOverlayProps: enhancedDragAndDropOverlayProps\n      }, enhancedLabelInputProps.value == null ? {} : {\n        labelInputProps: enhancedLabelInputProps\n      }, {\n        ref: handleContentRef\n      })), children && /*#__PURE__*/_jsx(TreeItemGroup, _extends({\n        as: GroupTransition\n      }, groupTransitionProps, {\n        children: children\n      }))]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TreeItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The component used to render the content of the item.\n   * @default TreeItemContent\n   */\n  ContentComponent: elementTypeAcceptingRef,\n  /**\n   * Props applied to ContentComponent.\n   */\n  ContentProps: PropTypes.object,\n  /**\n   * If `true`, the item is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The id of the item.\n   */\n  itemId: PropTypes.string.isRequired,\n  /**\n   * The tree item label.\n   */\n  label: PropTypes.node,\n  /**\n   * This prop isn't supported.\n   * Use the `onItemFocus` callback on the tree if you need to monitor a item's focus.\n   */\n  onFocus: unsupportedProp,\n  /**\n   * Callback fired when a key of the keyboard is pressed on the item.\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"classes\", \"className\", \"displayIcon\", \"expansionIcon\", \"icon\", \"label\", \"itemId\", \"onClick\", \"onMouseDown\", \"dragAndDropOverlayProps\", \"labelInputProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport Checkbox from '@mui/material/Checkbox';\nimport { useTreeItemState } from \"./useTreeItemState.js\";\nimport { TreeItem2DragAndDropOverlay } from \"../TreeItem2DragAndDropOverlay/index.js\";\nimport { TreeItem2LabelInput } from \"../TreeItem2LabelInput/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * @ignore - internal component.\n */\nconst TreeItemContent = /*#__PURE__*/React.forwardRef(function TreeItemContent(props, ref) {\n  const {\n      classes,\n      className,\n      displayIcon,\n      expansionIcon,\n      icon: iconProp,\n      label,\n      itemId,\n      onClick,\n      onMouseDown,\n      dragAndDropOverlayProps,\n      labelInputProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    disabled,\n    expanded,\n    selected,\n    focused,\n    editing,\n    editable,\n    disableSelection,\n    checkboxSelection,\n    handleExpansion,\n    handleSelection,\n    handleCheckboxSelection,\n    handleContentClick,\n    preventSelection,\n    expansionTrigger,\n    toggleItemEditing\n  } = useTreeItemState(itemId);\n  const icon = iconProp || expansionIcon || displayIcon;\n  const checkboxRef = React.useRef(null);\n  const handleMouseDown = event => {\n    preventSelection(event);\n    if (onMouseDown) {\n      onMouseDown(event);\n    }\n  };\n  const handleClick = event => {\n    handleContentClick?.(event, itemId);\n    if (checkboxRef.current?.contains(event.target)) {\n      return;\n    }\n    if (expansionTrigger === 'content') {\n      handleExpansion(event);\n    }\n    if (!checkboxSelection) {\n      handleSelection(event);\n    }\n    if (onClick) {\n      onClick(event);\n    }\n  };\n  const handleLabelDoubleClick = event => {\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    toggleItemEditing();\n  };\n  return (\n    /*#__PURE__*/\n    /* eslint-disable-next-line jsx-a11y/click-events-have-key-events,jsx-a11y/no-static-element-interactions -- Key event is handled by the TreeView */\n    _jsxs(\"div\", _extends({}, other, {\n      className: clsx(className, classes.root, expanded && classes.expanded, selected && classes.selected, focused && classes.focused, disabled && classes.disabled, editing && classes.editing, editable && classes.editable),\n      onClick: handleClick,\n      onMouseDown: handleMouseDown,\n      ref: ref,\n      children: [/*#__PURE__*/_jsx(\"div\", {\n        className: classes.iconContainer,\n        children: icon\n      }), checkboxSelection && /*#__PURE__*/_jsx(Checkbox, {\n        className: classes.checkbox,\n        checked: selected,\n        onChange: handleCheckboxSelection,\n        disabled: disabled || disableSelection,\n        ref: checkboxRef,\n        tabIndex: -1\n      }), editing ? /*#__PURE__*/_jsx(TreeItem2LabelInput, _extends({}, labelInputProps, {\n        className: classes.labelInput\n      })) : /*#__PURE__*/_jsx(\"div\", _extends({\n        className: classes.label\n      }, editable && {\n        onDoubleClick: handleLabelDoubleClick\n      }, {\n        children: label\n      })), dragAndDropOverlayProps && /*#__PURE__*/_jsx(TreeItem2DragAndDropOverlay, _extends({}, dragAndDropOverlayProps))]\n    }))\n  );\n});\nprocess.env.NODE_ENV !== \"production\" ? TreeItemContent.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object.isRequired,\n  className: PropTypes.string,\n  /**\n   * The icon to display next to the tree item's label. Either a parent or end icon.\n   */\n  displayIcon: PropTypes.node,\n  dragAndDropOverlayProps: PropTypes.shape({\n    action: PropTypes.oneOf(['make-child', 'move-to-parent', 'reorder-above', 'reorder-below']),\n    style: PropTypes.object\n  }),\n  /**\n   * The icon to display next to the tree item's label. Either an expansion or collapse icon.\n   */\n  expansionIcon: PropTypes.node,\n  /**\n   * The icon to display next to the tree item's label.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the item.\n   */\n  itemId: PropTypes.string.isRequired,\n  /**\n   * The tree item label.\n   */\n  label: PropTypes.node,\n  labelInputProps: PropTypes.shape({\n    autoFocus: PropTypes.oneOf([true]),\n    'data-element': PropTypes.oneOf(['labelInput']),\n    onBlur: PropTypes.func,\n    onChange: PropTypes.func,\n    onKeyDown: PropTypes.func,\n    type: PropTypes.oneOf(['text']),\n    value: PropTypes.string\n  })\n} : void 0;\nexport { TreeItemContent };", "import { useTreeViewContext } from \"../internals/TreeViewProvider/index.js\";\nimport { useTreeViewLabel } from \"../internals/plugins/useTreeViewLabel/index.js\";\nimport { hasPlugin } from \"../internals/utils/plugins.js\";\nexport function useTreeItemState(itemId) {\n  const {\n    instance,\n    items: {\n      onItemClick\n    },\n    selection: {\n      multiSelect,\n      checkboxSelection,\n      disableSelection\n    },\n    expansion: {\n      expansionTrigger\n    }\n  } = useTreeViewContext();\n  const expandable = instance.isItemExpandable(itemId);\n  const expanded = instance.isItemExpanded(itemId);\n  const focused = instance.isItemFocused(itemId);\n  const selected = instance.isItemSelected(itemId);\n  const disabled = instance.isItemDisabled(itemId);\n  const editing = instance?.isItemBeingEdited ? instance?.isItemBeingEdited(itemId) : false;\n  const editable = instance.isItemEditable ? instance.isItemEditable(itemId) : false;\n  const handleExpansion = event => {\n    if (!disabled) {\n      if (!focused) {\n        instance.focusItem(event, itemId);\n      }\n      const multiple = multiSelect && (event.shiftKey || event.ctrlKey || event.metaKey);\n\n      // If already expanded and trying to toggle selection don't close\n      if (expandable && !(multiple && instance.isItemExpanded(itemId))) {\n        instance.toggleItemExpansion(event, itemId);\n      }\n    }\n  };\n  const handleSelection = event => {\n    if (!disabled) {\n      if (!focused) {\n        instance.focusItem(event, itemId);\n      }\n      const multiple = multiSelect && (event.shiftKey || event.ctrlKey || event.metaKey);\n      if (multiple) {\n        if (event.shiftKey) {\n          instance.expandSelectionRange(event, itemId);\n        } else {\n          instance.selectItem({\n            event,\n            itemId,\n            keepExistingSelection: true\n          });\n        }\n      } else {\n        instance.selectItem({\n          event,\n          itemId,\n          shouldBeSelected: true\n        });\n      }\n    }\n  };\n  const handleCheckboxSelection = event => {\n    if (disableSelection || disabled) {\n      return;\n    }\n    const hasShift = event.nativeEvent.shiftKey;\n    if (multiSelect && hasShift) {\n      instance.expandSelectionRange(event, itemId);\n    } else {\n      instance.selectItem({\n        event,\n        itemId,\n        keepExistingSelection: multiSelect,\n        shouldBeSelected: event.target.checked\n      });\n    }\n  };\n  const preventSelection = event => {\n    if (event.shiftKey || event.ctrlKey || event.metaKey || disabled) {\n      // Prevent text selection\n      event.preventDefault();\n    }\n  };\n  const toggleItemEditing = () => {\n    if (!hasPlugin(instance, useTreeViewLabel)) {\n      return;\n    }\n    if (instance.isItemEditable(itemId)) {\n      if (instance.isItemBeingEdited(itemId)) {\n        instance.setEditedItemId(null);\n      } else {\n        instance.setEditedItemId(itemId);\n      }\n    }\n  };\n  const handleSaveItemLabel = (event, label) => {\n    if (!hasPlugin(instance, useTreeViewLabel)) {\n      return;\n    }\n\n    // As a side effect of `instance.focusItem` called here and in `handleCancelItemLabelEditing` the `labelInput` is blurred\n    // The `onBlur` event is triggered, which calls `handleSaveItemLabel` again.\n    // To avoid creating an unwanted behavior we need to check if the item is being edited before calling `updateItemLabel`\n    // using `instance.isItemBeingEditedRef` instead of `instance.isItemBeingEdited` since the state is not yet updated in this point\n    if (instance.isItemBeingEditedRef(itemId)) {\n      instance.updateItemLabel(itemId, label);\n      toggleItemEditing();\n      instance.focusItem(event, itemId);\n    }\n  };\n  const handleCancelItemLabelEditing = event => {\n    if (!hasPlugin(instance, useTreeViewLabel)) {\n      return;\n    }\n    if (instance.isItemBeingEditedRef(itemId)) {\n      toggleItemEditing();\n      instance.focusItem(event, itemId);\n    }\n  };\n  return {\n    disabled,\n    expanded,\n    selected,\n    focused,\n    editable,\n    editing,\n    disableSelection,\n    checkboxSelection,\n    handleExpansion,\n    handleSelection,\n    handleCheckboxSelection,\n    handleContentClick: onItemClick,\n    preventSelection,\n    expansionTrigger,\n    toggleItemEditing,\n    handleSaveItemLabel,\n    handleCancelItemLabelEditing\n  };\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { alpha } from '@mui/material/styles';\nimport { shouldForwardProp } from '@mui/system';\nimport { styled } from \"../internals/zero-styled/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TreeItem2DragAndDropOverlayRoot = styled('div', {\n  name: 'MuiTreeItem2DragAndDropOverlay',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root,\n  shouldForwardProp: prop => shouldForwardProp(prop) && prop !== 'action'\n})(({\n  theme\n}) => ({\n  position: 'absolute',\n  left: 0,\n  display: 'flex',\n  top: 0,\n  bottom: 0,\n  right: 0,\n  pointerEvents: 'none',\n  variants: [{\n    props: {\n      action: 'make-child'\n    },\n    style: {\n      marginLeft: 'calc(var(--TreeView-indentMultiplier) * var(--TreeView-itemDepth))',\n      borderRadius: theme.shape.borderRadius,\n      backgroundColor: alpha((theme.vars || theme).palette.primary.dark, 0.15)\n    }\n  }, {\n    props: {\n      action: 'reorder-above'\n    },\n    style: _extends({\n      marginLeft: 'calc(var(--TreeView-indentMultiplier) * var(--TreeView-itemDepth))',\n      borderTop: `1px solid ${alpha((theme.vars || theme).palette.grey[900], 0.6)}`\n    }, theme.applyStyles('dark', {\n      borderTopColor: alpha((theme.vars || theme).palette.grey[100], 0.6)\n    }))\n  }, {\n    props: {\n      action: 'reorder-below'\n    },\n    style: _extends({\n      marginLeft: 'calc(var(--TreeView-indentMultiplier) * var(--TreeView-itemDepth))',\n      borderBottom: `1px solid ${alpha((theme.vars || theme).palette.grey[900], 0.6)}`\n    }, theme.applyStyles('dark', {\n      borderBottomColor: alpha((theme.vars || theme).palette.grey[100], 0.6)\n    }))\n  }, {\n    props: {\n      action: 'move-to-parent'\n    },\n    style: _extends({\n      marginLeft: 'calc(var(--TreeView-indentMultiplier) * calc(var(--TreeView-itemDepth) - 1))',\n      borderBottom: `1px solid ${alpha((theme.vars || theme).palette.grey[900], 0.6)}`\n    }, theme.applyStyles('dark', {\n      borderBottomColor: alpha((theme.vars || theme).palette.grey[900], 0.6)\n    }))\n  }]\n}));\nfunction TreeItem2DragAndDropOverlay(props) {\n  if (props.action == null) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(TreeItem2DragAndDropOverlayRoot, _extends({}, props));\n}\nexport { TreeItem2DragAndDropOverlay };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { styled } from \"../internals/zero-styled/index.js\";\nconst TreeItem2LabelInput = styled('input', {\n  name: 'MuiTreeItem2',\n  slot: 'LabelInput',\n  overridesResolver: (props, styles) => styles.labelInput\n})(({\n  theme\n}) => _extends({}, theme.typography.body1, {\n  width: '100%',\n  backgroundColor: theme.palette.background.paper,\n  borderRadius: theme.shape.borderRadius,\n  border: 'none',\n  padding: '0 2px',\n  boxSizing: 'border-box',\n  '&:focus': {\n    outline: `1px solid ${theme.palette.primary.main}`\n  }\n}));\nexport { TreeItem2LabelInput };", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTreeItemUtilityClass(slot) {\n  return generateUtilityClass('MuiTreeItem', slot);\n}\nexport const treeItemClasses = generateUtilityClasses('MuiTreeItem', ['root', 'groupTransition', 'content', 'expanded', 'selected', 'focused', 'disabled', 'iconContainer', 'label', 'checkbox', 'labelInput', 'editable', 'editing', 'dragAndDropOverlay']);", "import { createSvgIcon } from '@mui/material/utils';\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const TreeViewExpandIcon = createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"\n}), 'TreeViewExpandIcon');\nexport const TreeViewCollapseIcon = createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z\"\n}), 'TreeViewCollapseIcon');", "import PropTypes from 'prop-types';\nimport { useTreeViewContext } from \"../internals/TreeViewProvider/index.js\";\nfunction TreeItem2Provider(props) {\n  const {\n    children,\n    itemId\n  } = props;\n  const {\n    wrapItem,\n    instance\n  } = useTreeViewContext();\n  return wrapItem({\n    children,\n    itemId,\n    instance\n  });\n}\nTreeItem2Provider.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node,\n  itemId: PropTypes.string.isRequired\n};\nexport { TreeItem2Provider };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA,IAAAA,UAAuB;AACvB,IAAAC,qBAAsB;;;ACAf,SAAS,4BAA4B,MAAM;AAChD,SAAO,qBAAqB,mBAAmB,IAAI;AACrD;AACO,IAAM,sBAAsB,uBAAuB,mBAAmB,CAAC,MAAM,CAAC;;;ACD9E,SAAS,oBAAoB,MAAM;AACxC,SAAO;AACT;;;ACNA;AACA,IAAAC,SAAuB;;;ACDvB;AACA,YAAuB;AAKhB,IAAM,oBAAoB,CAAC,SAAS,UAAU;AACnD,QAAM,YAAkB,aAAO,CAAC,CAAC;AACjC,QAAM,CAAC,aAAa,cAAc,IAAU,eAAS,MAAM;AACzD,UAAM,eAAe,CAAC;AACtB,YAAQ,QAAQ,YAAU;AACxB,UAAI,OAAO,QAAQ;AACjB,eAAO,QAAQ,OAAO,MAAM,EAAE,QAAQ,CAAC,CAAC,WAAW,gBAAgB,MAAM;AACvE,oBAAU,QAAQ,SAAS,IAAI;AAAA,YAC7B,cAAc,MAAM,SAAS,MAAM;AAAA,YACnC,iBAAiB,iBAAiB;AAAA,UACpC;AACA,uBAAa,SAAS,IAAI,iBAAiB,gBAAgB,KAAK;AAAA,QAClE,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,CAAC;AACD,QAAM,SAAS,OAAO,YAAY,OAAO,QAAQ,UAAU,OAAO,EAAE,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM;AAC9F,UAAM,QAAQ,MAAM,SAAS,KAAK,YAAY,SAAS;AACvD,WAAO,CAAC,WAAW;AAAA,MACjB;AAAA,MACA,oBAAoB,cAAY;AAC9B,YAAI,CAAC,MAAM,cAAc;AACvB,yBAAe,eAAa,SAAS,CAAC,GAAG,WAAW;AAAA,YAClD,CAAC,SAAS,GAAG;AAAA,UACf,CAAC,CAAC;AAAA,QACJ;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC,CAAC;AAIF,MAAI,MAAuC;AACzC,WAAO,QAAQ,UAAU,OAAO,EAAE,QAAQ,CAAC,CAAC,WAAW,KAAK,MAAM;AAChE,YAAM,aAAa,MAAM,SAAS;AAClC,YAAM,kBAAkB,MAAM,gBAAgB,KAAK;AACnD,MAAM,gBAAU,MAAM;AACpB,YAAI,MAAM,kBAAkB,eAAe,SAAY;AACrD,kBAAQ,MAAM,CAAC,sCAAsC,MAAM,eAAe,KAAK,IAAI,cAAc,SAAS,4BAA4B,MAAM,eAAe,OAAO,EAAE,eAAe,+EAA+E,qDAAqD,SAAS,+CAAoD,8HAA8H,sDAAsD,EAAE,KAAK,IAAI,CAAC;AAAA,QACtjB;AAAA,MACF,GAAG,CAAC,UAAU,CAAC;AACf,YAAM;AAAA,QACJ,SAAS;AAAA,MACX,IAAU,aAAO,eAAe;AAChC,MAAM,gBAAU,MAAM;AACpB,YAAI,CAAC,MAAM,gBAAgB,iBAAiB,iBAAiB;AAC3D,kBAAQ,MAAM,CAAC,8CAA8C,SAAS,wHAA6H,EAAE,KAAK,IAAI,CAAC;AAAA,QACjN;AAAA,MACF,GAAG,CAAC,KAAK,UAAU,eAAe,CAAC,CAAC;AAAA,IACtC,CAAC;AAAA,EACH;AAGA,SAAO;AACT;;;AC7DA,IAAAC,SAAuB;;;ACGhB,IAAM,eAAN,MAAmB;AAAA,EACxB,cAAc;AACZ,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,SAAS,CAAC;AAAA,EACjB;AAAA,EACA,GAAG,WAAW,UAAU,UAAU,CAAC,GAAG;AACpC,QAAI,aAAa,KAAK,OAAO,SAAS;AACtC,QAAI,CAAC,YAAY;AACf,mBAAa;AAAA,QACX,cAAc,oBAAI,IAAI;AAAA,QACtB,SAAS,oBAAI,IAAI;AAAA,MACnB;AACA,WAAK,OAAO,SAAS,IAAI;AAAA,IAC3B;AACA,QAAI,QAAQ,SAAS;AACnB,iBAAW,aAAa,IAAI,UAAU,IAAI;AAAA,IAC5C,OAAO;AACL,iBAAW,QAAQ,IAAI,UAAU,IAAI;AAAA,IACvC;AACA,QAAI,MAAuC;AACzC,YAAM,iBAAiB,WAAW,aAAa,OAAO,WAAW,QAAQ;AACzE,UAAI,iBAAiB,KAAK,gBAAgB,CAAC,KAAK,UAAU;AACxD,aAAK,WAAW;AAChB,gBAAQ,KAAK,CAAC,+CAA+C,cAAc,IAAI,SAAS,mBAAmB,EAAE,KAAK,IAAI,CAAC;AAAA,MACzH;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,WAAW,UAAU;AAClC,QAAI,KAAK,OAAO,SAAS,GAAG;AAC1B,WAAK,OAAO,SAAS,EAAE,QAAQ,OAAO,QAAQ;AAC9C,WAAK,OAAO,SAAS,EAAE,aAAa,OAAO,QAAQ;AAAA,IACrD;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,SAAS,CAAC;AAAA,EACjB;AAAA,EACA,KAAK,cAAc,MAAM;AACvB,UAAM,aAAa,KAAK,OAAO,SAAS;AACxC,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,UAAM,wBAAwB,MAAM,KAAK,WAAW,aAAa,KAAK,CAAC;AACvE,UAAM,mBAAmB,MAAM,KAAK,WAAW,QAAQ,KAAK,CAAC;AAC7D,aAAS,IAAI,sBAAsB,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAC7D,YAAM,WAAW,sBAAsB,CAAC;AACxC,UAAI,WAAW,aAAa,IAAI,QAAQ,GAAG;AACzC,iBAAS,MAAM,MAAM,IAAI;AAAA,MAC3B;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK,GAAG;AACnD,YAAM,WAAW,iBAAiB,CAAC;AACnC,UAAI,WAAW,QAAQ,IAAI,QAAQ,GAAG;AACpC,iBAAS,MAAM,MAAM,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK,WAAW,UAAU;AAExB,UAAM,OAAO;AACb,SAAK,GAAG,WAAW,SAAS,mBAAmB,MAAM;AACnD,WAAK,eAAe,WAAW,eAAe;AAC9C,eAAS,MAAM,MAAM,IAAI;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;;;ADlEA,IAAM,mBAAmB,WAAS;AAChC,SAAO,MAAM,yBAAyB;AACxC;AACO,IAAM,4BAA4B,MAAM;AAC7C,QAAM,CAAC,YAAY,IAAU,gBAAS,MAAM,IAAI,aAAa,CAAC;AAC9D,QAAM,eAAqB,mBAAY,IAAI,SAAS;AAClD,UAAM,CAAC,MAAM,QAAQ,QAAQ,CAAC,CAAC,IAAI;AACnC,UAAM,sBAAsB;AAC5B,QAAI,iBAAiB,KAAK,KAAK,MAAM,qBAAqB,GAAG;AAC3D;AAAA,IACF;AACA,iBAAa,KAAK,MAAM,QAAQ,KAAK;AAAA,EACvC,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,iBAAuB,mBAAY,CAAC,OAAO,YAAY;AAC3D,iBAAa,GAAG,OAAO,OAAO;AAC9B,WAAO,MAAM;AACX,mBAAa,eAAe,OAAO,OAAO;AAAA,IAC5C;AAAA,EACF,GAAG,CAAC,YAAY,CAAC;AACjB,SAAO;AAAA,IACL,UAAU;AAAA,MACR,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,IACpB;AAAA,EACF;AACF;AACA,0BAA0B,SAAS,CAAC;;;AE5B7B,IAAM,6BAA6B,CAAC;AAAA,EACzC;AACF,MAAM;AACJ,QAAM,YAAY,IAAI,IAAI,OAAO;AACjC,QAAM,sBAAsB,MAAM;AAClC,SAAO;AAAA,IACL,UAAU;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AACA,2BAA2B,SAAS,CAAC;;;ACXrC,IAAAC,SAAuB;AAEhB,IAAM,gBAAgB,CAAC;AAAA,EAC5B;AACF,MAAM;AACJ,QAAM,SAAS,MAAM,OAAO,EAAE;AAC9B,QAAM,yBAA+B,mBAAY,CAAC,QAAQ,gBAAgB,eAAe,GAAG,MAAM,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC;AACxH,SAAO;AAAA,IACL,cAAc,OAAO;AAAA,MACnB,IAAI;AAAA,IACN;AAAA,IACA,UAAU;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AACA,cAAc,SAAS;AAAA,EACrB,IAAI;AACN;;;ACXO,IAAM,yBAAyB,CAAC,2BAA2B,4BAA4B,aAAa;;;ACN3G,IAAM,YAAY,CAAC,SAAS,aAAa,UAAU,sBAAsB;AAClE,IAAM,+BAA+B,UAAQ;AAClD,MAAI;AAAA,IACA,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,MACJ,QAAQ,8BAA8B,KAAK,OAAO,SAAS;AAC7D,QAAM,eAAe,CAAC;AACtB,UAAQ,QAAQ,YAAU;AACxB,WAAO,OAAO,cAAc,OAAO,MAAM;AAAA,EAC3C,CAAC;AACD,QAAM,eAAe,CAAC;AACtB,QAAM,iBAAiB,CAAC;AACxB,SAAO,KAAK,KAAK,EAAE,QAAQ,cAAY;AACrC,UAAM,OAAO,MAAM,QAAQ;AAC3B,QAAI,aAAa,QAAQ,GAAG;AAC1B,mBAAa,QAAQ,IAAI;AAAA,IAC3B,OAAO;AACL,qBAAe,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF,CAAC;AACD,QAAM,0BAA0B,QAAQ,OAAO,CAAC,KAAK,WAAW;AAC9D,QAAI,OAAO,sBAAsB;AAC/B,aAAO,OAAO,qBAAqB,GAAG;AAAA,IACxC;AACA,WAAO;AAAA,EACT,GAAG,YAAY;AACf,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,OAAO,SAAS,CAAC;AAAA,IACjB,WAAW,aAAa,CAAC;AAAA,IACzB,sBAAsB,wBAAwB,CAAC;AAAA,EACjD;AACF;;;ACzCO,IAAM,0BAA0B,CAAC;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,iBAAiB,qBAAmB;AACxC,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,UAAM,sBAAsB,CAAC;AAC7B,UAAM,2BAA2B,CAAC;AAClC,YAAQ,QAAQ,YAAU;AACxB,UAAI,CAAC,OAAO,YAAY;AACtB;AAAA,MACF;AACA,YAAM,qBAAqB,OAAO,WAAW;AAAA,QAC3C,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AACD,UAAI,yDAAoB,SAAS;AAC/B,uBAAe,mBAAmB;AAAA,MACpC;AACA,UAAI,yDAAoB,YAAY;AAClC,0BAAkB,mBAAmB;AAAA,MACvC;AACA,UAAI,yDAAoB,gBAAgB;AACtC,4BAAoB,KAAK,mBAAmB,cAAc;AAG1D,eAAO,KAAK,mBAAmB,cAAc,EAAE,QAAQ,uBAAqB;AAC1E,mCAAyB,iBAAiB,IAAI;AAAA,QAChD,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,UAAM,uBAAuB,qBAAmB,uBAAqB;AACnE,YAAM,gBAAgB,CAAC;AACvB,0BAAoB,QAAQ,oCAAkC;AAC5D,cAAM,uCAAuC,+BAA+B,eAAe;AAC3F,YAAI,wCAAwC,MAAM;AAChD,iBAAO,OAAO,eAAe,qCAAqC,iBAAiB,CAAC;AAAA,QACtF;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,OAAO,YAAY,OAAO,KAAK,wBAAwB,EAAE,IAAI,sBAAoB,CAAC,kBAAkB,qBAAqB,gBAAgB,CAAC,CAAC,CAAC;AACnK,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,WAAW,CAAC;AAAA,IAChB;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,gBAAgB;AAEpB,aAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAC/C,YAAM,SAAS,QAAQ,CAAC;AACxB,UAAI,OAAO,UAAU;AACnB,wBAAgB,OAAO,SAAS;AAAA,UAC9B;AAAA,UACA,UAAU;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,WAAW,CAAC;AAAA,IAChB;AAAA,EACF,MAAM;AACJ,QAAI,gBAAgB;AAEpB,aAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAC/C,YAAM,SAAS,QAAQ,CAAC;AACxB,UAAI,OAAO,UAAU;AACnB,wBAAgB,OAAO,SAAS;AAAA,UAC9B,UAAU;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ARvFO,SAAS,6BAA6B,aAAa;AACxD,QAAM,uBAA6B,cAAO,CAAC,CAAC;AAC5C,MAAI,aAAa;AACf,QAAI,YAAY,WAAW,MAAM;AAC/B,kBAAY,UAAU,CAAC;AAAA,IACzB;AACA,WAAO,YAAY;AAAA,EACrB;AACA,SAAO,qBAAqB;AAC9B;AACO,IAAM,cAAc,CAAC;AAAA,EAC1B,SAAS;AAAA,EACT;AAAA,EACA;AACF,MAAM;AACJ,QAAM,UAAU,CAAC,GAAG,wBAAwB,GAAG,SAAS;AACxD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,6BAA6B;AAAA,IAC/B;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,SAAS,kBAAkB,SAAS,YAAY;AACtD,QAAM,cAAoB,cAAO,CAAC,CAAC;AACnC,QAAM,WAAW,YAAY;AAC7B,QAAM,YAAY,6BAA6B,MAAM;AACrD,QAAM,eAAqB,cAAO,IAAI;AACtC,QAAM,gBAAgB,WAAW,cAAc,OAAO;AACtD,QAAM,eAAe,wBAAwB;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX,CAAC;AACD,QAAM,CAAC,OAAO,QAAQ,IAAU,gBAAS,MAAM;AAC7C,UAAM,OAAO,CAAC;AACd,YAAQ,QAAQ,YAAU;AACxB,UAAI,OAAO,iBAAiB;AAC1B,eAAO,OAAO,MAAM,OAAO,gBAAgB,YAAY,CAAC;AAAA,MAC1D;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,CAAC;AACD,QAAM,mBAAmB,CAAC;AAC1B,QAAM,YAAY,YAAU;AAC1B,UAAM,iBAAiB,OAAO;AAAA,MAC5B;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,eAAe,cAAc;AAC/B,uBAAiB,KAAK,eAAe,YAAY;AAAA,IACnD;AACA,QAAI,eAAe,WAAW;AAC5B,aAAO,OAAO,WAAW,eAAe,SAAS;AAAA,IACnD;AACA,QAAI,eAAe,UAAU;AAC3B,aAAO,OAAO,UAAU,eAAe,QAAQ;AAAA,IACjD;AACA,QAAI,eAAe,cAAc;AAC/B,aAAO,OAAO,cAAc,eAAe,YAAY;AAAA,IACzD;AAAA,EACF;AACA,UAAQ,QAAQ,SAAS;AACzB,QAAM,eAAe,CAAC,gBAAgB,CAAC,MAAM;AAC3C,UAAM,YAAY,SAAS;AAAA,MACzB,MAAM;AAAA,IACR,GAAG,gBAAgB,eAAe;AAAA,MAChC,KAAK;AAAA,IACP,CAAC;AACD,qBAAiB,QAAQ,qBAAmB;AAC1C,aAAO,OAAO,WAAW,gBAAgB,aAAa,CAAC;AAAA,IACzD,CAAC;AACD,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACF;;;ASpGA;AAGA,IAAAC,SAAuB;;;ACHhB,IAAM,uBAAuB,CAAC,UAAU,WAAW,WAAW;AACnE,WAAS,eAAe,WAAW,MAAM;AAC3C;;;ACFO,IAAM,2BAA2B;AACjC,IAAM,sBAAsB,cAAY;AAC7C,QAAM,sBAAsB,CAAC;AAC7B,WAAS,QAAQ,CAAC,SAAS,UAAU;AACnC,wBAAoB,OAAO,IAAI;AAAA,EACjC,CAAC;AACD,SAAO;AACT;;;ACPA,IAAAC,SAAuB;AAChB,IAAM,2BAA8C,qBAAc,MAAM,EAAE;AACjF,IAAI,MAAuC;AACzC,2BAAyB,cAAc;AACzC;;;AHGA,yBAA4B;AAL5B,IAAMC,aAAY,CAAC,UAAU;AAM7B,IAAM,mBAAmB,CAAC;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,cAAc,CAAC;AACrB,QAAM,UAAU,CAAC;AACjB,QAAM,yBAAyB;AAAA,IAC7B,CAAC,wBAAwB,GAAG,CAAC;AAAA,EAC/B;AACA,QAAM,cAAc,CAAC,MAAM,OAAO,aAAa;AAnBjD;AAoBI,UAAM,KAAK,YAAY,UAAU,IAAI,IAAI,KAAK;AAC9C,QAAI,MAAM,MAAM;AACd,YAAM,IAAI,MAAM,CAAC,qFAAqF,yFAAyF,wDAAwD,KAAK,UAAU,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,IACzR;AACA,QAAI,YAAY,EAAE,KAAK,MAAM;AAC3B,YAAM,IAAI,MAAM,CAAC,qFAAqF,yFAAyF,oEAAoE,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,IACtR;AACA,UAAM,QAAQ,eAAe,aAAa,IAAI,IAAI,KAAK;AACvD,QAAI,SAAS,MAAM;AACjB,YAAM,IAAI,MAAM,CAAC,iFAAiF,+FAA+F,2DAA2D,KAAK,UAAU,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,IAC9R;AACA,gBAAY,EAAE,IAAI;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa;AAAA,MACb,YAAY,CAAC,GAAC,UAAK,aAAL,mBAAe;AAAA,MAC7B,UAAU,iBAAiB,eAAe,IAAI,IAAI;AAAA,MAClD;AAAA,IACF;AACA,YAAQ,EAAE,IAAI;AACd,UAAM,sBAAsB,YAAY;AACxC,QAAI,CAAC,uBAAuB,mBAAmB,GAAG;AAChD,6BAAuB,mBAAmB,IAAI,CAAC;AAAA,IACjD;AACA,2BAAuB,mBAAmB,EAAE,KAAK,EAAE;AACnD,eAAK,aAAL,mBAAe,QAAQ,WAAS,YAAY,OAAO,QAAQ,GAAG,EAAE;AAAA,EAClE;AACA,QAAM,QAAQ,UAAQ,YAAY,MAAM,GAAG,IAAI,CAAC;AAChD,QAAM,sBAAsB,CAAC;AAC7B,SAAO,KAAK,sBAAsB,EAAE,QAAQ,cAAY;AACtD,wBAAoB,QAAQ,IAAI,oBAAoB,uBAAuB,QAAQ,CAAC;AAAA,EACtF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACO,IAAM,mBAAmB,CAAC;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,cAAoB,mBAAY,YAAU,MAAM,MAAM,YAAY,MAAM,GAAG,CAAC,MAAM,MAAM,WAAW,CAAC;AAC1G,QAAM,UAAgB,mBAAY,YAAU,MAAM,MAAM,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM,OAAO,CAAC;AAC9F,QAAM,cAAoB,mBAAY,MAAM;AAC1C,UAAM,oBAAoB,QAAM;AAC9B,YAAM,wBAAwB,MAAM,MAAM,QAAQ,EAAE,GAClD,OAAO,8BAA8B,uBAAuBA,UAAS;AACvE,YAAM,cAAc,MAAM,MAAM,uBAAuB,EAAE;AACzD,UAAI,aAAa;AACf,aAAK,WAAW,YAAY,IAAI,iBAAiB;AAAA,MACnD;AACA,aAAO;AAAA,IACT;AACA,WAAO,MAAM,MAAM,uBAAuB,wBAAwB,EAAE,IAAI,iBAAiB;AAAA,EAC3F,GAAG,CAAC,MAAM,MAAM,SAAS,MAAM,MAAM,sBAAsB,CAAC;AAC5D,QAAM,iBAAuB,mBAAY,YAAU;AACjD,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,SAAS,YAAY,MAAM;AAG1C,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI,SAAS,UAAU;AACrB,aAAO;AAAA,IACT;AACA,WAAO,SAAS,YAAY,MAAM;AAChC,iBAAW,SAAS,YAAY,SAAS,QAAQ;AACjD,UAAI,SAAS,UAAU;AACrB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,QAAQ,CAAC;AACb,QAAM,eAAqB,mBAAY,YAAU;AAC/C,UAAM,WAAW,SAAS,YAAY,MAAM,EAAE,YAAY;AAC1D,WAAO,MAAM,MAAM,oBAAoB,QAAQ,EAAE,MAAM;AAAA,EACzD,GAAG,CAAC,UAAU,MAAM,MAAM,mBAAmB,CAAC;AAC9C,QAAM,4BAAkC,mBAAY,YAAU,MAAM,MAAM,uBAAuB,UAAU,wBAAwB,KAAK,CAAC,GAAG,CAAC,MAAM,MAAM,sBAAsB,CAAC;AAChL,QAAM,oBAAoB,YAAU;AAClC,UAAM,WAAW,SAAS,YAAY,MAAM;AAC5C,QAAI,YAAY,MAAM;AACpB,aAAO;AAAA,IACT;AACA,WAAO,SAAS,eAAe,SAAS,uBAAuB,QAAQ,SAAS,WAAW,CAAC;AAAA,EAC9F;AACA,QAAM,kBAAkB,YAAU;AAChC,QAAI,OAAO,wBAAwB;AACjC,aAAO;AAAA,IACT;AACA,WAAO,CAAC,SAAS,eAAe,MAAM;AAAA,EACxC;AACA,QAAM,6BAAmC,cAAO,KAAK;AACrD,QAAM,qBAA2B,mBAAY,MAAM;AACjD,+BAA2B,UAAU;AAAA,EACvC,GAAG,CAAC,CAAC;AACL,QAAM,0BAAgC,mBAAY,MAAM,2BAA2B,SAAS,CAAC,CAAC;AAC9F,EAAM,iBAAU,MAAM;AACpB,QAAI,SAAS,wBAAwB,GAAG;AACtC;AAAA,IACF;AACA,aAAS,eAAa;AACpB,YAAM,WAAW,iBAAiB;AAAA,QAChC,OAAO,OAAO;AAAA,QACd,gBAAgB,OAAO;AAAA,QACvB,WAAW,OAAO;AAAA,QAClB,cAAc,OAAO;AAAA,MACvB,CAAC;AACD,aAAO,OAAO,UAAU,MAAM,WAAW,EAAE,QAAQ,UAAQ;AACzD,YAAI,CAAC,SAAS,YAAY,KAAK,EAAE,GAAG;AAClC,+BAAqB,UAAU,cAAc;AAAA,YAC3C,IAAI,KAAK;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,SAAS,CAAC,GAAG,WAAW;AAAA,QAC7B,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,CAAC,UAAU,UAAU,OAAO,OAAO,OAAO,gBAAgB,OAAO,WAAW,OAAO,YAAY,CAAC;AACnG,QAAM,mBAAmB,MAAM;AAC7B,UAAM,qBAAqB,QAAM;AArJrC;AAsJM,YAAM,OAAO,MAAM,MAAM,YAAY,EAAE;AACvC,aAAO;AAAA,QACL,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,QACb,IAAI,KAAK;AAAA,QACT,WAAU,WAAM,MAAM,uBAAuB,EAAE,MAArC,mBAAwC,IAAI;AAAA,MACxD;AAAA,IACF;AACA,WAAO,MAAM,MAAM,uBAAuB,wBAAwB,EAAE,IAAI,kBAAkB;AAAA,EAC5F;AACA,SAAO;AAAA,IACL,cAAc,OAAO;AAAA,MACnB,OAAO;AAAA,QACL,sCAAsC,OAAO,OAAO,4BAA4B,WAAW,GAAG,OAAO,uBAAuB,OAAO,OAAO;AAAA,MAC5I;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,OAAO;AAAA,QACL,aAAa,OAAO;AAAA,QACpB,wBAAwB,OAAO;AAAA,QAC/B,wBAAwB,qBAAqB,0BAA0B;AAAA,MACzE;AAAA,IACF;AAAA,EACF;AACF;AACA,iBAAiB,kBAAkB,aAAW;AAAA,EAC5C,OAAO,iBAAiB;AAAA,IACtB,OAAO,OAAO;AAAA,IACd,gBAAgB,OAAO;AAAA,IACvB,WAAW,OAAO;AAAA,IAClB,cAAc,OAAO;AAAA,EACvB,CAAC;AACH;AACA,iBAAiB,uBAAuB,YAAU,SAAS,CAAC,GAAG,QAAQ;AAAA,EACrE,wBAAwB,OAAO,0BAA0B;AAAA,EACzD,yBAAyB,OAAO,2BAA2B;AAC7D,CAAC;AACD,iBAAiB,WAAW,CAAC;AAAA,EAC3B;AAAA,EACA;AACF,MAAM;AACJ,aAAoB,mBAAAC,KAAK,yBAAyB,UAAU;AAAA,IAC1D,OAAO,YAAO;AAnNlB;AAmNqB,6BAAS,YAAY,MAAM,MAA3B,mBAA8B,UAAS;AAAA;AAAA,IACxD;AAAA,EACF,CAAC;AACH;AACA,iBAAiB,SAAS;AAAA,EACxB,wBAAwB;AAAA,EACxB,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,aAAa;AAAA,EACb,yBAAyB;AAC3B;;;AI/NA;AACA,IAAAC,SAAuB;AAEhB,IAAM,uBAAuB,CAAC;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,mBAAyB,eAAQ,MAAM;AAC3C,UAAM,OAAO,oBAAI,IAAI;AACrB,WAAO,cAAc,MAAM,QAAQ,QAAM;AACvC,WAAK,IAAI,IAAI,IAAI;AAAA,IACnB,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,OAAO,cAAc,KAAK,CAAC;AAC/B,QAAM,mBAAmB,CAAC,OAAO,UAAU;AAf7C;AAgBI,iBAAO,0BAAP,gCAA+B,OAAO;AACtC,WAAO,cAAc,mBAAmB,KAAK;AAAA,EAC/C;AACA,QAAM,iBAAuB,mBAAY,YAAU,iBAAiB,IAAI,MAAM,GAAG,CAAC,gBAAgB,CAAC;AACnG,QAAM,mBAAyB,mBAAY,YAAO;AApBpD;AAoBuD,YAAC,GAAC,cAAS,YAAY,MAAM,MAA3B,mBAA8B;AAAA,KAAY,CAAC,QAAQ,CAAC;AAC3G,QAAM,sBAAsB,yBAAiB,CAAC,OAAO,WAAW;AAC9D,UAAM,mBAAmB,SAAS,eAAe,MAAM;AACvD,aAAS,iBAAiB,OAAO,QAAQ,CAAC,gBAAgB;AAAA,EAC5D,CAAC;AACD,QAAM,mBAAmB,yBAAiB,CAAC,OAAO,QAAQ,eAAe;AACvE,UAAM,mBAAmB,SAAS,eAAe,MAAM;AACvD,QAAI,qBAAqB,YAAY;AACnC;AAAA,IACF;AACA,QAAI;AACJ,QAAI,YAAY;AACd,oBAAc,CAAC,MAAM,EAAE,OAAO,OAAO,cAAc,KAAK;AAAA,IAC1D,OAAO;AACL,oBAAc,OAAO,cAAc,MAAM,OAAO,QAAM,OAAO,MAAM;AAAA,IACrE;AACA,QAAI,OAAO,uBAAuB;AAChC,aAAO,sBAAsB,OAAO,QAAQ,UAAU;AAAA,IACxD;AACA,qBAAiB,OAAO,WAAW;AAAA,EACrC,CAAC;AACD,QAAM,oBAAoB,CAAC,OAAO,WAAW;AAC3C,UAAM,WAAW,SAAS,YAAY,MAAM;AAC5C,UAAM,WAAW,SAAS,0BAA0B,SAAS,QAAQ;AACrE,UAAM,OAAO,SAAS,OAAO,WAAS,SAAS,iBAAiB,KAAK,KAAK,CAAC,SAAS,eAAe,KAAK,CAAC;AACzG,UAAM,cAAc,OAAO,cAAc,MAAM,OAAO,IAAI;AAC1D,QAAI,KAAK,SAAS,GAAG;AACnB,UAAI,OAAO,uBAAuB;AAChC,aAAK,QAAQ,yBAAuB;AAClC,iBAAO,sBAAsB,OAAO,qBAAqB,IAAI;AAAA,QAC/D,CAAC;AAAA,MACH;AACA,uBAAiB,OAAO,WAAW;AAAA,IACrC;AAAA,EACF;AACA,QAAM,mBAAyB,eAAQ,MAAM;AAC3C,QAAI,OAAO,kBAAkB;AAC3B,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,SAAS,oBAAoB;AAC/B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,GAAG,CAAC,OAAO,kBAAkB,SAAS,kBAAkB,CAAC;AACzD,SAAO;AAAA,IACL,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,qBAAqB,SAAS;AAAA,EAC5B,eAAe;AAAA,IACb,iBAAiB,YAAU,OAAO;AAAA,EACpC;AACF;AACA,IAAM,yBAAyB,CAAC;AAChC,qBAAqB,uBAAuB,YAAU,SAAS,CAAC,GAAG,QAAQ;AAAA,EACzE,sBAAsB,OAAO,wBAAwB;AACvD,CAAC;AACD,qBAAqB,SAAS;AAAA,EAC5B,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,kBAAkB;AACpB;;;ACjGA;AACA,IAAAC,SAAuB;;;ACDvB,IAAM,8BAA8B,CAAC,UAAU,UAAU;AAEvD,MAAI,YAAY,MAAM,SAAS;AAC/B,SAAO,aAAa,KAAK,CAAC,SAAS,gBAAgB,MAAM,SAAS,CAAC,GAAG;AACpE,iBAAa;AAAA,EACf;AACA,MAAI,cAAc,IAAI;AACpB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,SAAS;AACxB;AACO,IAAM,2BAA2B,CAAC,UAAU,WAAW;AAC5D,QAAM,WAAW,SAAS,YAAY,MAAM;AAC5C,QAAM,WAAW,SAAS,0BAA0B,SAAS,QAAQ;AACrE,QAAM,YAAY,SAAS,aAAa,MAAM;AAG9C,MAAI,cAAc,GAAG;AACnB,WAAO,SAAS;AAAA,EAClB;AAGA,MAAI,gCAAgC,YAAY;AAChD,SAAO,CAAC,SAAS,gBAAgB,SAAS,6BAA6B,CAAC,KAAK,iCAAiC,GAAG;AAC/G,qCAAiC;AAAA,EACnC;AACA,MAAI,kCAAkC,IAAI;AAExC,QAAI,SAAS,YAAY,MAAM;AAC7B,aAAO;AAAA,IACT;AAGA,WAAO,yBAAyB,UAAU,SAAS,QAAQ;AAAA,EAC7D;AAGA,MAAI,gBAAgB,SAAS,6BAA6B;AAC1D,MAAI,qBAAqB,4BAA4B,UAAU,SAAS,0BAA0B,aAAa,CAAC;AAChH,SAAO,SAAS,eAAe,aAAa,KAAK,sBAAsB,MAAM;AAC3E,oBAAgB;AAChB,yBAAqB,SAAS,0BAA0B,aAAa,EAAE,KAAK,SAAS,eAAe;AAAA,EACtG;AACA,SAAO;AACT;AACO,IAAM,uBAAuB,CAAC,UAAU,WAAW;AAExD,MAAI,SAAS,eAAe,MAAM,GAAG;AACnC,UAAM,sBAAsB,SAAS,0BAA0B,MAAM,EAAE,KAAK,SAAS,eAAe;AACpG,QAAI,uBAAuB,MAAM;AAC/B,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,WAAW,SAAS,YAAY,MAAM;AAC1C,SAAO,YAAY,MAAM;AAEvB,UAAM,WAAW,SAAS,0BAA0B,SAAS,QAAQ;AACrE,UAAM,mBAAmB,SAAS,aAAa,SAAS,EAAE;AAC1D,QAAI,mBAAmB,SAAS,SAAS,GAAG;AAC1C,UAAI,gBAAgB,mBAAmB;AACvC,aAAO,CAAC,SAAS,gBAAgB,SAAS,aAAa,CAAC,KAAK,gBAAgB,SAAS,SAAS,GAAG;AAChG,yBAAiB;AAAA,MACnB;AACA,UAAI,SAAS,gBAAgB,SAAS,aAAa,CAAC,GAAG;AACrD,eAAO,SAAS,aAAa;AAAA,MAC/B;AAAA,IACF;AAGA,eAAW,SAAS,YAAY,SAAS,QAAQ;AAAA,EACnD;AACA,SAAO;AACT;AACO,IAAM,uBAAuB,cAAY;AAC9C,MAAI,SAAS;AACb,SAAO,UAAU,QAAQ,SAAS,eAAe,MAAM,GAAG;AACxD,UAAM,WAAW,SAAS,0BAA0B,MAAM;AAC1D,UAAM,qBAAqB,4BAA4B,UAAU,QAAQ;AAGzE,QAAI,sBAAsB,MAAM;AAC9B,aAAO;AAAA,IACT;AACA,aAAS;AAAA,EACX;AACA,SAAO;AACT;AACO,IAAM,wBAAwB,cAAY,SAAS,0BAA0B,IAAI,EAAE,KAAK,SAAS,eAAe;AAgBhH,IAAM,yBAAyB,CAAC,UAAU,SAAS,YAAY;AACpE,MAAI,YAAY,SAAS;AACvB,WAAO,CAAC,SAAS,OAAO;AAAA,EAC1B;AACA,QAAM,YAAY,SAAS,YAAY,OAAO;AAC9C,QAAM,YAAY,SAAS,YAAY,OAAO;AAC9C,MAAI,UAAU,aAAa,UAAU,MAAM,UAAU,aAAa,UAAU,IAAI;AAC9E,WAAO,UAAU,aAAa,UAAU,KAAK,CAAC,UAAU,IAAI,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,UAAU,EAAE;AAAA,EACzG;AACA,QAAM,UAAU,CAAC,UAAU,EAAE;AAC7B,QAAM,UAAU,CAAC,UAAU,EAAE;AAC7B,MAAI,YAAY,UAAU;AAC1B,MAAI,YAAY,UAAU;AAC1B,MAAI,oBAAoB,QAAQ,QAAQ,SAAS,MAAM;AACvD,MAAI,oBAAoB,QAAQ,QAAQ,SAAS,MAAM;AACvD,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,SAAO,CAAC,qBAAqB,CAAC,mBAAmB;AAC/C,QAAI,WAAW;AACb,cAAQ,KAAK,SAAS;AACtB,0BAAoB,QAAQ,QAAQ,SAAS,MAAM;AACnD,kBAAY,cAAc;AAC1B,UAAI,CAAC,qBAAqB,WAAW;AACnC,oBAAY,SAAS,YAAY,SAAS,EAAE;AAAA,MAC9C;AAAA,IACF;AACA,QAAI,aAAa,CAAC,mBAAmB;AACnC,cAAQ,KAAK,SAAS;AACtB,0BAAoB,QAAQ,QAAQ,SAAS,MAAM;AACnD,kBAAY,cAAc;AAC1B,UAAI,CAAC,qBAAqB,WAAW;AACnC,oBAAY,SAAS,YAAY,SAAS,EAAE;AAAA,MAC9C;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,oBAAoB,YAAY;AACvD,QAAM,iBAAiB,SAAS,0BAA0B,cAAc;AACxE,QAAM,QAAQ,QAAQ,QAAQ,QAAQ,cAAc,IAAI,CAAC;AACzD,QAAM,QAAQ,QAAQ,QAAQ,QAAQ,cAAc,IAAI,CAAC;AACzD,SAAO,eAAe,QAAQ,KAAK,IAAI,eAAe,QAAQ,KAAK,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO;AAC/G;AACO,IAAM,6BAA6B,CAAC,UAAU,SAAS,YAAY;AACxE,QAAM,cAAc,YAAU;AAE5B,QAAI,SAAS,iBAAiB,MAAM,KAAK,SAAS,eAAe,MAAM,GAAG;AACxE,aAAO,SAAS,0BAA0B,MAAM,EAAE,CAAC;AAAA,IACrD;AACA,QAAI,WAAW,SAAS,YAAY,MAAM;AAC1C,WAAO,YAAY,MAAM;AAEvB,YAAM,WAAW,SAAS,0BAA0B,SAAS,QAAQ;AACrE,YAAM,mBAAmB,SAAS,aAAa,SAAS,EAAE;AAC1D,UAAI,mBAAmB,SAAS,SAAS,GAAG;AAC1C,eAAO,SAAS,mBAAmB,CAAC;AAAA,MACtC;AAGA,iBAAW,SAAS,YAAY,SAAS,QAAQ;AAAA,IACnD;AACA,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AACA,QAAM,CAAC,OAAO,IAAI,IAAI,uBAAuB,UAAU,SAAS,OAAO;AACvE,QAAM,QAAQ,CAAC,KAAK;AACpB,MAAI,UAAU;AACd,SAAO,YAAY,MAAM;AACvB,cAAU,YAAY,OAAO;AAC7B,QAAI,CAAC,SAAS,eAAe,OAAO,GAAG;AACrC,YAAM,KAAK,OAAO;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AACT;AACO,IAAM,uBAAuB,cAAY;AAC9C,MAAI,OAAO,sBAAsB,QAAQ;AACzC,QAAM,iBAAiB,CAAC;AACxB,SAAO,QAAQ,MAAM;AACnB,mBAAe,KAAK,IAAI;AACxB,WAAO,qBAAqB,UAAU,IAAI;AAAA,EAC5C;AACA,SAAO;AACT;AASO,IAAM,wBAAwB,CAAC,QAAQ,aAAa;AACzD,SAAO,aAAa,OAAO,QAAQ,oBAAoB;AACzD;;;AC7LO,IAAM,8BAA8B,WAAS;AAClD,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,MAAM;AACjB,WAAO,CAAC,KAAK;AAAA,EACf;AACA,SAAO,CAAC;AACV;AACO,IAAM,qBAAqB,WAAS;AACzC,QAAM,SAAS,CAAC;AAChB,QAAM,QAAQ,YAAU;AACtB,WAAO,MAAM,IAAI;AAAA,EACnB,CAAC;AACD,SAAO;AACT;;;AFhBO,IAAM,uBAAuB,CAAC;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,mBAAyB,cAAO,IAAI;AAC1C,QAAM,oBAA0B,cAAO,CAAC,CAAC;AACzC,QAAM,mBAAyB,eAAQ,MAAM;AAC3C,UAAM,OAAO,oBAAI,IAAI;AACrB,QAAI,MAAM,QAAQ,OAAO,cAAc,KAAK,GAAG;AAC7C,aAAO,cAAc,MAAM,QAAQ,QAAM;AACvC,aAAK,IAAI,IAAI,IAAI;AAAA,MACnB,CAAC;AAAA,IACH,WAAW,OAAO,cAAc,SAAS,MAAM;AAC7C,WAAK,IAAI,OAAO,cAAc,OAAO,IAAI;AAAA,IAC3C;AACA,WAAO;AAAA,EACT,GAAG,CAAC,OAAO,cAAc,KAAK,CAAC;AAC/B,QAAM,mBAAmB,CAAC,OAAO,qBAAqB;AACpD,QAAI,OAAO,uBAAuB;AAChC,UAAI,OAAO,aAAa;AACtB,cAAM,aAAa,iBAAiB,OAAO,YAAU,CAAC,SAAS,eAAe,MAAM,CAAC;AACrF,cAAM,eAAe,OAAO,cAAc,MAAM,OAAO,YAAU,CAAC,iBAAiB,SAAS,MAAM,CAAC;AACnG,mBAAW,QAAQ,YAAU;AAC3B,iBAAO,sBAAsB,OAAO,QAAQ,IAAI;AAAA,QAClD,CAAC;AACD,qBAAa,QAAQ,YAAU;AAC7B,iBAAO,sBAAsB,OAAO,QAAQ,KAAK;AAAA,QACnD,CAAC;AAAA,MACH,WAAW,qBAAqB,OAAO,cAAc,OAAO;AAC1D,YAAI,OAAO,cAAc,SAAS,MAAM;AACtC,iBAAO,sBAAsB,OAAO,OAAO,cAAc,OAAO,KAAK;AAAA,QACvE;AACA,YAAI,oBAAoB,MAAM;AAC5B,iBAAO,sBAAsB,OAAO,kBAAkB,IAAI;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,uBAAuB;AAChC,aAAO,sBAAsB,OAAO,gBAAgB;AAAA,IACtD;AACA,WAAO,cAAc,mBAAmB,gBAAgB;AAAA,EAC1D;AACA,QAAM,iBAAiB,YAAU,iBAAiB,IAAI,MAAM;AAC5D,QAAM,aAAa,CAAC;AAAA,IAClB;AAAA,IACA;AAAA,IACA,wBAAwB;AAAA,IACxB;AAAA,EACF,MAAM;AACJ,QAAI,OAAO,kBAAkB;AAC3B;AAAA,IACF;AACA,QAAI;AACJ,QAAI,uBAAuB;AACzB,YAAM,qBAAqB,4BAA4B,OAAO,cAAc,KAAK;AACjF,YAAM,mBAAmB,SAAS,eAAe,MAAM;AACvD,UAAI,qBAAqB,qBAAqB,SAAS,oBAAoB,OAAO;AAChF,sBAAc,mBAAmB,OAAO,QAAM,OAAO,MAAM;AAAA,MAC7D,WAAW,CAAC,qBAAqB,qBAAqB,QAAQ,oBAAoB,OAAO;AACvF,sBAAc,CAAC,MAAM,EAAE,OAAO,kBAAkB;AAAA,MAClD,OAAO;AACL,sBAAc;AAAA,MAChB;AAAA,IACF,OAAO;AAEL,UAAI,qBAAqB,SAAS,oBAAoB,QAAQ,SAAS,eAAe,MAAM,GAAG;AAC7F,sBAAc,OAAO,cAAc,CAAC,IAAI;AAAA,MAC1C,OAAO;AACL,sBAAc,OAAO,cAAc,CAAC,MAAM,IAAI;AAAA,MAChD;AAAA,IACF;AACA,qBAAiB,OAAO,WAAW;AACnC,qBAAiB,UAAU;AAC3B,sBAAkB,UAAU,CAAC;AAAA,EAC/B;AACA,QAAM,cAAc,CAAC,OAAO,CAAC,OAAO,GAAG,MAAM;AAC3C,QAAI,OAAO,oBAAoB,CAAC,OAAO,aAAa;AAClD;AAAA,IACF;AACA,QAAI,mBAAmB,4BAA4B,OAAO,cAAc,KAAK,EAAE,MAAM;AAIrF,QAAI,OAAO,KAAK,kBAAkB,OAAO,EAAE,SAAS,GAAG;AACrD,yBAAmB,iBAAiB,OAAO,QAAM,CAAC,kBAAkB,QAAQ,EAAE,CAAC;AAAA,IACjF;AAGA,UAAM,sBAAsB,mBAAmB,gBAAgB;AAC/D,UAAM,QAAQ,2BAA2B,UAAU,OAAO,GAAG;AAC7D,UAAM,oBAAoB,MAAM,OAAO,QAAM,CAAC,oBAAoB,EAAE,CAAC;AACrE,uBAAmB,iBAAiB,OAAO,iBAAiB;AAC5D,qBAAiB,OAAO,gBAAgB;AACxC,sBAAkB,UAAU,mBAAmB,KAAK;AAAA,EACtD;AACA,QAAM,uBAAuB,CAAC,OAAO,WAAW;AAC9C,QAAI,iBAAiB,WAAW,MAAM;AACpC,YAAM,CAAC,OAAO,GAAG,IAAI,uBAAuB,UAAU,QAAQ,iBAAiB,OAAO;AACtF,kBAAY,OAAO,CAAC,OAAO,GAAG,CAAC;AAAA,IACjC;AAAA,EACF;AACA,QAAM,6BAA6B,CAAC,OAAO,WAAW;AACpD,gBAAY,OAAO,CAAC,sBAAsB,QAAQ,GAAG,MAAM,CAAC;AAAA,EAC9D;AACA,QAAM,2BAA2B,CAAC,OAAO,WAAW;AAClD,gBAAY,OAAO,CAAC,QAAQ,qBAAqB,QAAQ,CAAC,CAAC;AAAA,EAC7D;AACA,QAAM,0BAA0B,WAAS;AACvC,QAAI,OAAO,oBAAoB,CAAC,OAAO,aAAa;AAClD;AAAA,IACF;AACA,UAAM,iBAAiB,qBAAqB,QAAQ;AACpD,qBAAiB,OAAO,cAAc;AACtC,sBAAkB,UAAU,mBAAmB,cAAc;AAAA,EAC/D;AACA,QAAM,gCAAgC,CAAC,OAAO,aAAa,aAAa;AACtE,QAAI,OAAO,oBAAoB,CAAC,OAAO,aAAa;AAClD;AAAA,IACF;AACA,QAAI,mBAAmB,4BAA4B,OAAO,cAAc,KAAK,EAAE,MAAM;AACrF,QAAI,OAAO,KAAK,kBAAkB,OAAO,EAAE,WAAW,GAAG;AACvD,uBAAiB,KAAK,QAAQ;AAC9B,wBAAkB,UAAU;AAAA,QAC1B,CAAC,WAAW,GAAG;AAAA,QACf,CAAC,QAAQ,GAAG;AAAA,MACd;AAAA,IACF,OAAO;AACL,UAAI,CAAC,kBAAkB,QAAQ,WAAW,GAAG;AAC3C,0BAAkB,UAAU,CAAC;AAAA,MAC/B;AACA,UAAI,kBAAkB,QAAQ,QAAQ,GAAG;AACvC,2BAAmB,iBAAiB,OAAO,QAAM,OAAO,WAAW;AACnE,eAAO,kBAAkB,QAAQ,WAAW;AAAA,MAC9C,OAAO;AACL,yBAAiB,KAAK,QAAQ;AAC9B,0BAAkB,QAAQ,QAAQ,IAAI;AAAA,MACxC;AAAA,IACF;AACA,qBAAiB,OAAO,gBAAgB;AAAA,EAC1C;AACA,SAAO;AAAA,IACL,cAAc,OAAO;AAAA,MACnB,wBAAwB,OAAO;AAAA,IACjC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,QACT,aAAa,OAAO;AAAA,QACpB,mBAAmB,OAAO;AAAA,QAC1B,kBAAkB,OAAO;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACF;AACA,qBAAqB,SAAS;AAAA,EAC5B,eAAe;AAAA,IACb,iBAAiB,YAAU,OAAO;AAAA,EACpC;AACF;AACA,IAAM,yBAAyB,CAAC;AAChC,qBAAqB,uBAAuB,YAAU,SAAS,CAAC,GAAG,QAAQ;AAAA,EACzE,kBAAkB,OAAO,oBAAoB;AAAA,EAC7C,aAAa,OAAO,eAAe;AAAA,EACnC,mBAAmB,OAAO,qBAAqB;AAAA,EAC/C,sBAAsB,OAAO,yBAAyB,OAAO,cAAc,yBAAyB;AACtG,CAAC;AACD,qBAAqB,SAAS;AAAA,EAC5B,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,uBAAuB;AAAA,EACvB,uBAAuB;AACzB;;;AG9LA;AACA,IAAAC,UAAuB;;;ACDvB,IAAAC,SAAuB;;;ACCvB,IAAM,4BAA4B;AAC3B,IAAM,4BAAN,MAAgC;AAAA,EACrC,YAAY,UAAU,2BAA2B;AAC/C,SAAK,WAAW,oBAAI,IAAI;AACxB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,SAAS,QAAQ,aAAa,iBAAiB;AAC7C,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW,oBAAI,IAAI;AAAA,IAC1B;AACA,UAAM,UAAU,WAAW,MAAM;AAC/B,UAAI,OAAO,gBAAgB,YAAY;AACrC,oBAAY;AAAA,MACd;AACA,WAAK,SAAS,OAAO,gBAAgB,YAAY;AAAA,IACnD,GAAG,KAAK,cAAc;AACtB,SAAK,SAAS,IAAI,gBAAgB,cAAc,OAAO;AAAA,EACzD;AAAA,EACA,WAAW,iBAAiB;AAC1B,UAAM,UAAU,KAAK,SAAS,IAAI,gBAAgB,YAAY;AAC9D,QAAI,SAAS;AACX,WAAK,SAAS,OAAO,gBAAgB,YAAY;AACjD,mBAAa,OAAO;AAAA,IACtB;AAAA,EACF;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,QAAQ,CAAC,OAAO,QAAQ;AACpC,aAAK,WAAW;AAAA,UACd,cAAc;AAAA,QAChB,CAAC;AAAA,MACH,CAAC;AACD,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACF;;;ACrCO,IAAM,2CAAN,MAA+C;AAAA,EACpD,cAAc;AACZ,SAAK,WAAW,IAAI,qBAAqB,iBAAe;AACtD,UAAI,OAAO,gBAAgB,YAAY;AACrC,oBAAY;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS,QAAQ,aAAa,iBAAiB;AAC7C,SAAK,SAAS,SAAS,QAAQ,aAAa,eAAe;AAAA,EAC7D;AAAA,EACA,WAAW,iBAAiB;AAC1B,SAAK,SAAS,WAAW,eAAe;AAAA,EAC1C;AAAA;AAAA,EAGA,QAAQ;AAAA,EAAC;AACX;;;AFbA,IAAM,4BAAN,MAAgC;AAAC;AAI1B,SAAS,8BAA8BC,oBAAmB;AAC/D,MAAI,uBAAuB;AAC3B,SAAO,SAASC,yBAAwB,UAAU,WAAW,SAAS;AACpE,QAAID,mBAAkB,aAAa,MAAM;AACvC,MAAAA,mBAAkB,WAAW,OAAO,yBAAyB,cAAc,IAAI,yCAAyC,IAAI,IAAI,0BAA0B;AAAA,IAC5J;AACA,UAAM,CAAC,qBAAqB,IAAU,gBAAS,IAAI,0BAA0B,CAAC;AAC9E,UAAM,eAAqB,cAAO,IAAI;AACtC,UAAM,aAAmB,cAAO;AAChC,eAAW,UAAU;AACrB,UAAM,kBAAwB,cAAO,IAAI;AACzC,QAAI,CAAC,aAAa,WAAW,WAAW,SAAS;AAC/C,YAAM,kBAAkB,CAAC,QAAQ,UAAU;AApBjD;AAqBQ,YAAI,CAAC,MAAM,qBAAqB;AAC9B,2BAAW,YAAX,oCAAqB,QAAQ;AAAA,QAC/B;AAAA,MACF;AACA,mBAAa,UAAU,SAAS,iBAAiB,WAAW,eAAe;AAC3E,8BAAwB;AACxB,sBAAgB,UAAU;AAAA,QACxB,cAAc;AAAA,MAChB;AACA,MAAAA,mBAAkB,SAAS;AAAA,QAAS;AAAA;AAAA,QAEpC,MAAM;AAhCZ;AAiCQ,6BAAa,YAAb;AACA,uBAAa,UAAU;AACvB,0BAAgB,UAAU;AAAA,QAC5B;AAAA,QAAG,gBAAgB;AAAA,MAAO;AAAA,IAC5B,WAAW,CAAC,WAAW,WAAW,aAAa,SAAS;AACtD,mBAAa,QAAQ;AACrB,mBAAa,UAAU;AACvB,UAAI,gBAAgB,SAAS;AAC3B,QAAAA,mBAAkB,SAAS,WAAW,gBAAgB,OAAO;AAC7D,wBAAgB,UAAU;AAAA,MAC5B;AAAA,IACF;AACA,IAAM,iBAAU,MAAM;AACpB,UAAI,CAAC,aAAa,WAAW,WAAW,SAAS;AAC/C,cAAM,kBAAkB,CAAC,QAAQ,UAAU;AA/CnD;AAgDU,cAAI,CAAC,MAAM,qBAAqB;AAC9B,6BAAW,YAAX,oCAAqB,QAAQ;AAAA,UAC/B;AAAA,QACF;AACA,qBAAa,UAAU,SAAS,iBAAiB,WAAW,eAAe;AAAA,MAC7E;AACA,UAAI,gBAAgB,WAAWA,mBAAkB,UAAU;AAGzD,QAAAA,mBAAkB,SAAS,WAAW,gBAAgB,OAAO;AAC7D,wBAAgB,UAAU;AAAA,MAC5B;AACA,aAAO,MAAM;AA5DnB;AA6DQ,2BAAa,YAAb;AACA,qBAAa,UAAU;AAAA,MACzB;AAAA,IACF,GAAG,CAAC,UAAU,SAAS,CAAC;AAAA,EAC1B;AACF;AACA,IAAM,oBAAoB;AAAA,EACxB,UAAU;AACZ;AAOO,IAAM,0BAA0B,8BAA8B,iBAAiB;;;AG3E/E,IAAM,mBAAmB,CAAC,OAAO,aAAa;AACnD,QAAM,WAAW,KAAK;AACtB,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,MAAI,SAAS,YAAY;AACvB,WAAO,iBAAiB,SAAS,UAAU;AAAA,EAC7C;AACA,SAAO;AACT;;;AJFA,IAAM,4BAA4B,CAAC,UAAU,kBAAkB;AAC7D,MAAI,iBAAiB,4BAA4B,aAAa,EAAE,KAAK,YAAU;AAC7E,QAAI,CAAC,SAAS,gBAAgB,MAAM,GAAG;AACrC,aAAO;AAAA,IACT;AACA,UAAM,WAAW,SAAS,YAAY,MAAM;AAC5C,WAAO,aAAa,SAAS,YAAY,QAAQ,SAAS,eAAe,SAAS,QAAQ;AAAA,EAC5F,CAAC;AACD,MAAI,kBAAkB,MAAM;AAC1B,qBAAiB,sBAAsB,QAAQ;AAAA,EACjD;AACA,SAAO;AACT;AACO,IAAM,mBAAmB,CAAC;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,yBAAyB,0BAA0B,UAAU,OAAO,cAAc,KAAK;AAC7F,QAAM,mBAAmB,yBAAiB,YAAU;AAClD,UAAM,cAAc,OAAO,WAAW,aAAa,OAAO,MAAM,aAAa,IAAI;AACjF,QAAI,MAAM,kBAAkB,aAAa;AACvC,eAAS,eAAa,SAAS,CAAC,GAAG,WAAW;AAAA,QAC5C,eAAe;AAAA,MACjB,CAAC,CAAC;AAAA,IACJ;AAAA,EACF,CAAC;AACD,QAAM,oBAA0B,oBAAY,MAAM,CAAC,CAAC,QAAQ,WAAW,QAAQ,QAAQ,SAAS,iBAAiB,cAAc,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;AAC5J,QAAM,gBAAsB,oBAAY,YAAU,MAAM,kBAAkB,UAAU,kBAAkB,GAAG,CAAC,MAAM,eAAe,iBAAiB,CAAC;AACjJ,QAAM,gBAAgB,YAAU;AAC9B,UAAM,WAAW,SAAS,YAAY,MAAM;AAC5C,WAAO,aAAa,SAAS,YAAY,QAAQ,SAAS,eAAe,SAAS,QAAQ;AAAA,EAC5F;AACA,QAAM,iBAAiB,CAAC,OAAO,WAAW;AACxC,UAAM,cAAc,SAAS,kBAAkB,MAAM;AACrD,QAAI,aAAa;AACf,kBAAY,MAAM;AAAA,IACpB;AACA,qBAAiB,MAAM;AACvB,QAAI,OAAO,aAAa;AACtB,aAAO,YAAY,OAAO,MAAM;AAAA,IAClC;AAAA,EACF;AACA,QAAM,YAAY,yBAAiB,CAAC,OAAO,WAAW;AAEpD,QAAI,cAAc,MAAM,GAAG;AACzB,qBAAe,OAAO,MAAM;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,QAAM,oBAAoB,yBAAiB,MAAM;AAC/C,QAAI,MAAM,iBAAiB,MAAM;AAC/B;AAAA,IACF;AACA,UAAM,WAAW,SAAS,YAAY,MAAM,aAAa;AACzD,QAAI,UAAU;AACZ,YAAM,cAAc,SAAS,eAAe,SAAS,uBAAuB,MAAM,eAAe,SAAS,WAAW,CAAC;AACtH,UAAI,aAAa;AACf,oBAAY,KAAK;AAAA,MACnB;AAAA,IACF;AACA,qBAAiB,IAAI;AAAA,EACvB,CAAC;AACD,QAAM,kBAAkB,YAAU,WAAW;AAC7C,0BAAwB,UAAU,cAAc,CAAC;AAAA,IAC/C;AAAA,EACF,MAAM;AACJ,QAAI,MAAM,kBAAkB,IAAI;AAC9B,qBAAe,MAAM,sBAAsB;AAAA,IAC7C;AAAA,EACF,CAAC;AACD,QAAM,wBAAwB,mBAAiB,WAAS;AAjF1D;AAkFI,wBAAc,YAAd,uCAAwB;AACxB,QAAI,MAAM,qBAAqB;AAC7B;AAAA,IACF;AAGA,QAAI,MAAM,WAAW,MAAM,eAAe;AACxC,qBAAe,OAAO,sBAAsB;AAAA,IAC9C;AAAA,EACF;AACA,SAAO;AAAA,IACL,cAAc,oBAAkB;AAAA,MAC9B,SAAS,sBAAsB,aAAa;AAAA,IAC9C;AAAA,IACA,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,iBAAiB,kBAAkB,OAAO;AAAA,EACxC,eAAe;AACjB;AACA,iBAAiB,SAAS;AAAA,EACxB,aAAa;AACf;;;AKhHA,IAAAE,UAAuB;AAMvB,SAAS,qBAAqB,QAAQ;AACpC,SAAO,CAAC,CAAC,UAAU,OAAO,WAAW,KAAK,CAAC,CAAC,OAAO,MAAM,IAAI;AAC/D;AACO,IAAM,gCAAgC,CAAC;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,QAAQ,OAAO;AACrB,QAAM,eAAqB,eAAO,CAAC,CAAC;AACpC,QAAM,qBAAqB,yBAAiB,cAAY;AACtD,iBAAa,UAAU,SAAS,aAAa,OAAO;AAAA,EACtD,CAAC;AACD,EAAM,kBAAU,MAAM;AACpB,QAAI,SAAS,wBAAwB,GAAG;AACtC;AAAA,IACF;AACA,UAAM,kBAAkB,CAAC;AACzB,UAAM,cAAc,UAAQ;AAC1B,sBAAgB,KAAK,EAAE,IAAI,KAAK,MAAM,UAAU,GAAG,CAAC,EAAE,YAAY;AAAA,IACpE;AACA,WAAO,OAAO,MAAM,MAAM,WAAW,EAAE,QAAQ,WAAW;AAC1D,iBAAa,UAAU;AAAA,EACzB,GAAG,CAAC,MAAM,MAAM,aAAa,OAAO,WAAW,QAAQ,CAAC;AACxD,QAAM,uBAAuB,CAAC,QAAQ,UAAU;AAC9C,UAAM,aAAa,MAAM,YAAY;AACrC,UAAM,cAAc,mBAAiB;AACnC,YAAM,aAAa,qBAAqB,UAAU,aAAa;AAE/D,UAAI,eAAe,MAAM;AACvB,eAAO,sBAAsB,QAAQ;AAAA,MACvC;AACA,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB;AACrB,QAAI,gBAAgB,YAAY,MAAM;AACtC,UAAM,eAAe,CAAC;AAEtB,WAAO,kBAAkB,QAAQ,CAAC,aAAa,aAAa,GAAG;AAC7D,UAAI,aAAa,QAAQ,aAAa,MAAM,YAAY;AACtD,yBAAiB;AAAA,MACnB,OAAO;AACL,qBAAa,aAAa,IAAI;AAC9B,wBAAgB,YAAY,aAAa;AAAA,MAC3C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,yBAAyB,YAAU,CAAC,OAAO,oBAAoB,CAAC,SAAS,eAAe,MAAM;AACpG,QAAM,yBAAyB,YAAU;AACvC,WAAO,CAAC,SAAS,eAAe,MAAM,KAAK,SAAS,iBAAiB,MAAM;AAAA,EAC7E;AAGA,QAAM,oBAAoB,CAAC,OAAO,WAAW;AAC3C,QAAI,MAAM,qBAAqB;AAC7B;AAAA,IACF;AACA,QAAI,MAAM,UAAU,sBAAsB,MAAM,QAAQ,MAAM,aAAa,GAAG;AAC5E;AAAA,IACF;AACA,UAAM,cAAc,MAAM,WAAW,MAAM;AAC3C,UAAM,MAAM,MAAM;AAGlB,YAAQ,MAAM;AAAA,MAEZ,MAAK,QAAQ,OAAO,uBAAuB,MAAM,IAC/C;AACE,cAAM,eAAe;AACrB,YAAI,OAAO,eAAe,MAAM,UAAU;AACxC,mBAAS,qBAAqB,OAAO,MAAM;AAAA,QAC7C,OAAO;AACL,mBAAS,WAAW;AAAA,YAClB;AAAA,YACA;AAAA,YACA,uBAAuB,OAAO;AAAA,YAC9B,kBAAkB,OAAO,cAAc,SAAY;AAAA,UACrD,CAAC;AAAA,QACH;AACA;AAAA,MACF;AAAA,MAIF,KAAK,QAAQ,SACX;AACE,YAAI,UAAU,UAAU,gBAAgB,KAAK,SAAS,eAAe,MAAM,KAAK,CAAC,SAAS,kBAAkB,MAAM,GAAG;AACnH,mBAAS,gBAAgB,MAAM;AAAA,QACjC,WAAW,uBAAuB,MAAM,GAAG;AACzC,mBAAS,oBAAoB,OAAO,MAAM;AAC1C,gBAAM,eAAe;AAAA,QACvB,WAAW,uBAAuB,MAAM,GAAG;AACzC,cAAI,OAAO,aAAa;AACtB,kBAAM,eAAe;AACrB,qBAAS,WAAW;AAAA,cAClB;AAAA,cACA;AAAA,cACA,uBAAuB;AAAA,YACzB,CAAC;AAAA,UACH,WAAW,CAAC,SAAS,eAAe,MAAM,GAAG;AAC3C,qBAAS,WAAW;AAAA,cAClB;AAAA,cACA;AAAA,YACF,CAAC;AACD,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAGF,KAAK,QAAQ,aACX;AACE,cAAM,WAAW,qBAAqB,UAAU,MAAM;AACtD,YAAI,UAAU;AACZ,gBAAM,eAAe;AACrB,mBAAS,UAAU,OAAO,QAAQ;AAIlC,cAAI,OAAO,eAAe,MAAM,YAAY,uBAAuB,QAAQ,GAAG;AAC5E,qBAAS,8BAA8B,OAAO,QAAQ,QAAQ;AAAA,UAChE;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAGF,KAAK,QAAQ,WACX;AACE,cAAM,eAAe,yBAAyB,UAAU,MAAM;AAC9D,YAAI,cAAc;AAChB,gBAAM,eAAe;AACrB,mBAAS,UAAU,OAAO,YAAY;AAItC,cAAI,OAAO,eAAe,MAAM,YAAY,uBAAuB,YAAY,GAAG;AAChF,qBAAS,8BAA8B,OAAO,QAAQ,YAAY;AAAA,UACpE;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAIF,MAAK,QAAQ,gBAAgB,CAAC,SAAS,QAAQ,eAAe,QAC5D;AACE,YAAI,SAAS,eAAe,MAAM,GAAG;AACnC,gBAAM,aAAa,qBAAqB,UAAU,MAAM;AACxD,cAAI,YAAY;AACd,qBAAS,UAAU,OAAO,UAAU;AACpC,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF,WAAW,uBAAuB,MAAM,GAAG;AACzC,mBAAS,oBAAoB,OAAO,MAAM;AAC1C,gBAAM,eAAe;AAAA,QACvB;AACA;AAAA,MACF;AAAA,MAIF,MAAK,QAAQ,eAAe,CAAC,SAAS,QAAQ,gBAAgB,QAC5D;AACE,YAAI,uBAAuB,MAAM,KAAK,SAAS,eAAe,MAAM,GAAG;AACrE,mBAAS,oBAAoB,OAAO,MAAM;AAC1C,gBAAM,eAAe;AAAA,QACvB,OAAO;AACL,gBAAM,SAAS,SAAS,YAAY,MAAM,EAAE;AAC5C,cAAI,QAAQ;AACV,qBAAS,UAAU,OAAO,MAAM;AAChC,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAGF,KAAK,QAAQ,QACX;AAGE,YAAI,uBAAuB,MAAM,KAAK,OAAO,eAAe,eAAe,MAAM,UAAU;AACzF,mBAAS,2BAA2B,OAAO,MAAM;AAAA,QACnD,OAAO;AACL,mBAAS,UAAU,OAAO,sBAAsB,QAAQ,CAAC;AAAA,QAC3D;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MAGF,KAAK,QAAQ,OACX;AAGE,YAAI,uBAAuB,MAAM,KAAK,OAAO,eAAe,eAAe,MAAM,UAAU;AACzF,mBAAS,yBAAyB,OAAO,MAAM;AAAA,QACjD,OAAO;AACL,mBAAS,UAAU,OAAO,qBAAqB,QAAQ,CAAC;AAAA,QAC1D;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MAGF,KAAK,QAAQ,KACX;AACE,iBAAS,kBAAkB,OAAO,MAAM;AACxC,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MAIF,MAAK,QAAQ,OAAO,eAAe,OAAO,eAAe,CAAC,OAAO,mBAC/D;AACE,iBAAS,wBAAwB,KAAK;AACtC,cAAM,eAAe;AACrB;AAAA,MACF;AAAA,MAIF,MAAK,CAAC,eAAe,CAAC,MAAM,YAAY,qBAAqB,GAAG,IAC9D;AACE,cAAM,eAAe,qBAAqB,QAAQ,GAAG;AACrD,YAAI,gBAAgB,MAAM;AACxB,mBAAS,UAAU,OAAO,YAAY;AACtC,gBAAM,eAAe;AAAA,QACvB;AACA;AAAA,MACF;AAAA,IACJ;AAAA,EACF;AACA,SAAO;AAAA,IACL,UAAU;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,8BAA8B,SAAS,CAAC;;;AC1PjC,IAAM,mBAAmB,CAAC;AAAA,EAC/B;AAAA,EACA;AACF,MAAM;AACJ,SAAO;AAAA,IACL,cAAc;AAAA,MACZ,OAAO;AAAA,QACL,OAAO;AAAA,UACL,cAAc,MAAM;AAAA,UACpB,YAAY,MAAM;AAAA,UAClB,SAAS,MAAM;AAAA,QACjB;AAAA,QACA,WAAW;AAAA,UACT,cAAc,UAAU;AAAA,UACxB,YAAY,UAAU;AAAA,UACtB,SAAS,UAAU;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,iBAAiB,SAAS,CAAC;;;ACdpB,IAAM,yBAAyB,CAAC,kBAAkB,sBAAsB,sBAAsB,kBAAkB,+BAA+B,kBAAkB,gBAAgB;;;ACNxL;AAKA,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;AAItB,0BAAkC;;;ACXlC;AAGA,IAAAC,UAAuB;AACvB,wBAAsB;;;ACDf,SAAS,iBAAiB,QAAQ;AACvC,QAAM;AAAA,IACJ;AAAA,IACA,OAAO;AAAA,MACL;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT;AAAA,IACF;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM,aAAa,SAAS,iBAAiB,MAAM;AACnD,QAAM,WAAW,SAAS,eAAe,MAAM;AAC/C,QAAM,UAAU,SAAS,cAAc,MAAM;AAC7C,QAAM,WAAW,SAAS,eAAe,MAAM;AAC/C,QAAM,WAAW,SAAS,eAAe,MAAM;AAC/C,QAAM,WAAU,qCAAU,qBAAoB,qCAAU,kBAAkB,UAAU;AACpF,QAAM,WAAW,SAAS,iBAAiB,SAAS,eAAe,MAAM,IAAI;AAC7E,QAAM,kBAAkB,WAAS;AAC/B,QAAI,CAAC,UAAU;AACb,UAAI,CAAC,SAAS;AACZ,iBAAS,UAAU,OAAO,MAAM;AAAA,MAClC;AACA,YAAM,WAAW,gBAAgB,MAAM,YAAY,MAAM,WAAW,MAAM;AAG1E,UAAI,cAAc,EAAE,YAAY,SAAS,eAAe,MAAM,IAAI;AAChE,iBAAS,oBAAoB,OAAO,MAAM;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACA,QAAM,kBAAkB,WAAS;AAC/B,QAAI,CAAC,UAAU;AACb,UAAI,CAAC,SAAS;AACZ,iBAAS,UAAU,OAAO,MAAM;AAAA,MAClC;AACA,YAAM,WAAW,gBAAgB,MAAM,YAAY,MAAM,WAAW,MAAM;AAC1E,UAAI,UAAU;AACZ,YAAI,MAAM,UAAU;AAClB,mBAAS,qBAAqB,OAAO,MAAM;AAAA,QAC7C,OAAO;AACL,mBAAS,WAAW;AAAA,YAClB;AAAA,YACA;AAAA,YACA,uBAAuB;AAAA,UACzB,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,iBAAS,WAAW;AAAA,UAClB;AAAA,UACA;AAAA,UACA,kBAAkB;AAAA,QACpB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,QAAM,0BAA0B,WAAS;AACvC,QAAI,oBAAoB,UAAU;AAChC;AAAA,IACF;AACA,UAAM,WAAW,MAAM,YAAY;AACnC,QAAI,eAAe,UAAU;AAC3B,eAAS,qBAAqB,OAAO,MAAM;AAAA,IAC7C,OAAO;AACL,eAAS,WAAW;AAAA,QAClB;AAAA,QACA;AAAA,QACA,uBAAuB;AAAA,QACvB,kBAAkB,MAAM,OAAO;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,mBAAmB,WAAS;AAChC,QAAI,MAAM,YAAY,MAAM,WAAW,MAAM,WAAW,UAAU;AAEhE,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AACA,QAAM,oBAAoB,MAAM;AAC9B,QAAI,CAAC,UAAU,UAAU,gBAAgB,GAAG;AAC1C;AAAA,IACF;AACA,QAAI,SAAS,eAAe,MAAM,GAAG;AACnC,UAAI,SAAS,kBAAkB,MAAM,GAAG;AACtC,iBAAS,gBAAgB,IAAI;AAAA,MAC/B,OAAO;AACL,iBAAS,gBAAgB,MAAM;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,QAAM,sBAAsB,CAAC,OAAO,UAAU;AAC5C,QAAI,CAAC,UAAU,UAAU,gBAAgB,GAAG;AAC1C;AAAA,IACF;AAMA,QAAI,SAAS,qBAAqB,MAAM,GAAG;AACzC,eAAS,gBAAgB,QAAQ,KAAK;AACtC,wBAAkB;AAClB,eAAS,UAAU,OAAO,MAAM;AAAA,IAClC;AAAA,EACF;AACA,QAAM,+BAA+B,WAAS;AAC5C,QAAI,CAAC,UAAU,UAAU,gBAAgB,GAAG;AAC1C;AAAA,IACF;AACA,QAAI,SAAS,qBAAqB,MAAM,GAAG;AACzC,wBAAkB;AAClB,eAAS,UAAU,OAAO,MAAM;AAAA,IAClC;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC5IA;AACA,IAAAC,UAAuB;AAIvB,IAAAC,sBAA4B;AAC5B,IAAM,kCAAkC,eAAO,OAAO;AAAA,EACpD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAAA,EAC7C,mBAAmB,UAAQ,kBAAkB,IAAI,KAAK,SAAS;AACjE,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,UAAU;AAAA,EACV,MAAM;AAAA,EACN,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,eAAe;AAAA,EACf,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,cAAc,MAAM,MAAM;AAAA,MAC1B,iBAAiB,OAAO,MAAM,QAAQ,OAAO,QAAQ,QAAQ,MAAM,IAAI;AAAA,IACzE;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,OAAO,SAAS;AAAA,MACd,YAAY;AAAA,MACZ,WAAW,aAAa,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,GAAG,GAAG,GAAG,CAAC;AAAA,IAC7E,GAAG,MAAM,YAAY,QAAQ;AAAA,MAC3B,gBAAgB,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,GAAG,GAAG,GAAG;AAAA,IACpE,CAAC,CAAC;AAAA,EACJ,GAAG;AAAA,IACD,OAAO;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,OAAO,SAAS;AAAA,MACd,YAAY;AAAA,MACZ,cAAc,aAAa,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,GAAG,GAAG,GAAG,CAAC;AAAA,IAChF,GAAG,MAAM,YAAY,QAAQ;AAAA,MAC3B,mBAAmB,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,GAAG,GAAG,GAAG;AAAA,IACvE,CAAC,CAAC;AAAA,EACJ,GAAG;AAAA,IACD,OAAO;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,OAAO,SAAS;AAAA,MACd,YAAY;AAAA,MACZ,cAAc,aAAa,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,GAAG,GAAG,GAAG,CAAC;AAAA,IAChF,GAAG,MAAM,YAAY,QAAQ;AAAA,MAC3B,mBAAmB,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,GAAG,GAAG,GAAG;AAAA,IACvE,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,EAAE;AACF,SAAS,4BAA4B,OAAO;AAC1C,MAAI,MAAM,UAAU,MAAM;AACxB,WAAO;AAAA,EACT;AACA,aAAoB,oBAAAC,KAAK,iCAAiC,SAAS,CAAC,GAAG,KAAK,CAAC;AAC/E;;;ACnEA;AAEA,IAAM,sBAAsB,eAAO,SAAS;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,CAAC;AAAA,EACF;AACF,MAAM,SAAS,CAAC,GAAG,MAAM,WAAW,OAAO;AAAA,EACzC,OAAO;AAAA,EACP,iBAAiB,MAAM,QAAQ,WAAW;AAAA,EAC1C,cAAc,MAAM,MAAM;AAAA,EAC1B,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,IACT,SAAS,aAAa,MAAM,QAAQ,QAAQ,IAAI;AAAA,EAClD;AACF,CAAC,CAAC;;;AHRF,IAAAC,sBAA2C;AAR3C,IAAMC,aAAY,CAAC,WAAW,aAAa,eAAe,iBAAiB,QAAQ,SAAS,UAAU,WAAW,eAAe,2BAA2B,iBAAiB;AAY5K,IAAM,kBAAqC,mBAAW,SAASC,iBAAgB,OAAO,KAAK;AACzF,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,UAAS;AACxD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,MAAM;AAC3B,QAAM,OAAO,YAAY,iBAAiB;AAC1C,QAAM,cAAoB,eAAO,IAAI;AACrC,QAAM,kBAAkB,WAAS;AAC/B,qBAAiB,KAAK;AACtB,QAAI,aAAa;AACf,kBAAY,KAAK;AAAA,IACnB;AAAA,EACF;AACA,QAAM,cAAc,WAAS;AAtD/B;AAuDI,6DAAqB,OAAO;AAC5B,SAAI,iBAAY,YAAZ,mBAAqB,SAAS,MAAM,SAAS;AAC/C;AAAA,IACF;AACA,QAAI,qBAAqB,WAAW;AAClC,sBAAgB,KAAK;AAAA,IACvB;AACA,QAAI,CAAC,mBAAmB;AACtB,sBAAgB,KAAK;AAAA,IACvB;AACA,QAAI,SAAS;AACX,cAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACA,QAAM,yBAAyB,WAAS;AACtC,QAAI,MAAM,qBAAqB;AAC7B;AAAA,IACF;AACA,sBAAkB;AAAA,EACpB;AACA;AAAA;AAAA,QAGE,oBAAAE,MAAM,OAAO,SAAS,CAAC,GAAG,OAAO;AAAA,MAC/B,WAAW,aAAK,WAAW,QAAQ,MAAM,YAAY,QAAQ,UAAU,YAAY,QAAQ,UAAU,WAAW,QAAQ,SAAS,YAAY,QAAQ,UAAU,WAAW,QAAQ,SAAS,YAAY,QAAQ,QAAQ;AAAA,MACvN,SAAS;AAAA,MACT,aAAa;AAAA,MACb;AAAA,MACA,UAAU,KAAc,oBAAAC,KAAK,OAAO;AAAA,QAClC,WAAW,QAAQ;AAAA,QACnB,UAAU;AAAA,MACZ,CAAC,GAAG,yBAAkC,oBAAAA,KAAK,kBAAU;AAAA,QACnD,WAAW,QAAQ;AAAA,QACnB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,UAAU,YAAY;AAAA,QACtB,KAAK;AAAA,QACL,UAAU;AAAA,MACZ,CAAC,GAAG,cAAuB,oBAAAA,KAAK,qBAAqB,SAAS,CAAC,GAAG,iBAAiB;AAAA,QACjF,WAAW,QAAQ;AAAA,MACrB,CAAC,CAAC,QAAiB,oBAAAA,KAAK,OAAO,SAAS;AAAA,QACtC,WAAW,QAAQ;AAAA,MACrB,GAAG,YAAY;AAAA,QACb,eAAe;AAAA,MACjB,GAAG;AAAA,QACD,UAAU;AAAA,MACZ,CAAC,CAAC,GAAG,+BAAwC,oBAAAA,KAAK,6BAA6B,SAAS,CAAC,GAAG,uBAAuB,CAAC,CAAC;AAAA,IACvH,CAAC,CAAC;AAAA;AAEN,CAAC;AACD,OAAwC,gBAAgB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlE,SAAS,kBAAAC,QAAU,OAAO;AAAA,EAC1B,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,aAAa,kBAAAA,QAAU;AAAA,EACvB,yBAAyB,kBAAAA,QAAU,MAAM;AAAA,IACvC,QAAQ,kBAAAA,QAAU,MAAM,CAAC,cAAc,kBAAkB,iBAAiB,eAAe,CAAC;AAAA,IAC1F,OAAO,kBAAAA,QAAU;AAAA,EACnB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIzB,MAAM,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIhB,QAAQ,kBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAIzB,OAAO,kBAAAA,QAAU;AAAA,EACjB,iBAAiB,kBAAAA,QAAU,MAAM;AAAA,IAC/B,WAAW,kBAAAA,QAAU,MAAM,CAAC,IAAI,CAAC;AAAA,IACjC,gBAAgB,kBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC;AAAA,IAC9C,QAAQ,kBAAAA,QAAU;AAAA,IAClB,UAAU,kBAAAA,QAAU;AAAA,IACpB,WAAW,kBAAAA,QAAU;AAAA,IACrB,MAAM,kBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC;AAAA,IAC9B,OAAO,kBAAAA,QAAU;AAAA,EACnB,CAAC;AACH,IAAI;;;AIlJG,SAAS,wBAAwB,MAAM;AAC5C,SAAO,qBAAqB,eAAe,IAAI;AACjD;AACO,IAAM,kBAAkB,uBAAuB,eAAe,CAAC,QAAQ,mBAAmB,WAAW,YAAY,YAAY,WAAW,YAAY,iBAAiB,SAAS,YAAY,cAAc,YAAY,WAAW,oBAAoB,CAAC;;;ACJ3P,IAAAC,UAAuB;AACvB,IAAAC,sBAA4B;AACrB,IAAM,qBAAqB,kBAA2B,oBAAAC,KAAK,QAAQ;AAAA,EACxE,GAAG;AACL,CAAC,GAAG,oBAAoB;AACjB,IAAM,uBAAuB,kBAA2B,oBAAAA,KAAK,QAAQ;AAAA,EAC1E,GAAG;AACL,CAAC,GAAG,sBAAsB;;;ACR1B,IAAAC,qBAAsB;AAEtB,SAAS,kBAAkB,OAAO;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB;AACvB,SAAO,SAAS;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,kBAAkB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,UAAU,mBAAAC,QAAU;AAAA,EACpB,QAAQ,mBAAAA,QAAU,OAAO;AAC3B;;;APIA,IAAAC,sBAA2C;AA1B3C,IAAMC,aAAY,CAAC,YAAY,aAAa,SAAS,aAAa,oBAAoB,gBAAgB,UAAU,MAAM,SAAS,WAAW,eAAe,WAAW,UAAU,WAAW;AAAzL,IACEC,cAAa,CAAC,YAAY;AAD5B,IAEEC,cAAa,CAAC,YAAY;AAF5B,IAGEC,cAAa,CAAC,YAAY;AAwB5B,IAAMC,iBAAgB,oBAAoB,aAAa;AACvD,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,SAAS,CAAC,SAAS;AAAA,IACnB,UAAU,CAAC,UAAU;AAAA,IACrB,UAAU,CAAC,UAAU;AAAA,IACrB,SAAS,CAAC,SAAS;AAAA,IACnB,UAAU,CAAC,UAAU;AAAA,IACrB,eAAe,CAAC,eAAe;AAAA,IAC/B,UAAU,CAAC,UAAU;AAAA,IACrB,OAAO,CAAC,OAAO;AAAA,IACf,YAAY,CAAC,YAAY;AAAA,IACzB,SAAS,CAAC,SAAS;AAAA,IACnB,UAAU,CAAC,UAAU;AAAA,IACrB,iBAAiB,CAAC,iBAAiB;AAAA,EACrC;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACA,IAAM,eAAe,eAAO,MAAM;AAAA,EAChC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AACD,IAAM,wBAAwB,eAAO,iBAAiB;AAAA,EACpD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,WAAO,CAAC,OAAO,SAAS,OAAO,iBAAiB;AAAA,MAC9C,CAAC,MAAM,gBAAgB,aAAa,EAAE,GAAG,OAAO;AAAA,IAClD,GAAG,OAAO,SAAS;AAAA,MACjB,CAAC,MAAM,gBAAgB,KAAK,EAAE,GAAG,OAAO;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,cAAQ,uCAAkB,IAAI,KAAK,SAAS;AACjE,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,SAAS,MAAM,QAAQ,KAAK,CAAC;AAAA,EAC7B,cAAc,MAAM,MAAM;AAAA,EAC1B,OAAO;AAAA,EACP,WAAW;AAAA;AAAA,EAEX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,KAAK,MAAM,QAAQ,CAAC;AAAA,EACpB,QAAQ;AAAA,EACR,yBAAyB;AAAA,EACzB,WAAW;AAAA,IACT,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA;AAAA,IAEtD,wBAAwB;AAAA,MACtB,iBAAiB;AAAA,IACnB;AAAA,EACF;AAAA,EACA,CAAC,KAAK,gBAAgB,QAAQ,EAAE,GAAG;AAAA,IACjC,UAAU,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,IAC9C,iBAAiB;AAAA,EACnB;AAAA,EACA,CAAC,KAAK,gBAAgB,OAAO,EAAE,GAAG;AAAA,IAChC,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EACxD;AAAA,EACA,CAAC,KAAK,gBAAgB,QAAQ,EAAE,GAAG;AAAA,IACjC,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,eAAe;AAAA,IACvM,WAAW;AAAA,MACT,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,WAAW,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,OAAO,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,kBAAkB,MAAM,QAAQ,OAAO,YAAY;AAAA;AAAA,MAE7R,wBAAwB;AAAA,QACtB,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,eAAe;AAAA,MACzM;AAAA,IACF;AAAA,IACA,CAAC,KAAK,gBAAgB,OAAO,EAAE,GAAG;AAAA,MAChC,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,WAAW,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,OAAO,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,kBAAkB,MAAM,QAAQ,OAAO,YAAY;AAAA,IAC/R;AAAA,EACF;AAAA,EACA,CAAC,MAAM,gBAAgB,aAAa,EAAE,GAAG;AAAA,IACvC,OAAO;AAAA,IACP,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,SAAS;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,CAAC,MAAM,gBAAgB,KAAK,EAAE,GAAG,SAAS;AAAA,IACxC,OAAO;AAAA,IACP,WAAW;AAAA;AAAA;AAAA,IAGX,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,GAAG,MAAM,WAAW,KAAK;AAAA,EACzB,CAAC,MAAM,gBAAgB,QAAQ,EAAE,GAAG;AAAA,IAClC,SAAS;AAAA,EACX;AAAA,EACA,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,wBAAwB;AAAA,IAC1B;AAAA,IACA,OAAO;AAAA,MACL,aAAa,QAAQ,MAAM,QAAQ,CAAC,CAAC;AAAA,IACvC;AAAA,EACF,CAAC;AACH,EAAE;AACF,IAAM,gBAAgB,eAAO,kBAAU;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAAA,EAC7C,mBAAmB,cAAQ,uCAAkB,IAAI,KAAK,SAAS;AACjE,CAAC,EAAE;AAAA,EACD,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,aAAa;AAAA,EACb,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,wBAAwB;AAAA,IAC1B;AAAA,IACA,OAAO;AAAA,MACL,aAAa;AAAA,IACf;AAAA,EACF,CAAC;AACH,CAAC;AAYM,IAAM,WAA8B,mBAAW,SAASC,UAAS,SAAS,OAAO;AA3KxF;AA4KE,QAAM;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,IACA,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM,eAAqB,mBAAW,wBAAwB;AAC9D,QAAM,QAAQD,eAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,WAAW;AAAA,IACX,mBAAmB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOJ,UAAS;AACxD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,MAAM;AAC3B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,eAAe,KAAK;AACxB,QAAM,gBAAsB,eAAO,IAAI;AACvC,QAAM,mBAAyB,eAAO,IAAI;AAC1C,QAAM,gBAAgB,WAAW,OAAO,SAAS,aAAa;AAC9D,QAAM,mBAAmB,WAAW,6CAAc,KAAK,YAAY,gBAAgB;AACnF,QAAM,QAAQ;AAAA,IACZ,aAAY,mCAAS,eAAc,aAAa,MAAM,cAAc;AAAA,IACpE,eAAc,mCAAS,iBAAgB,aAAa,MAAM,gBAAgB;AAAA,IAC1E,UAAS,mCAAS,YAAW,aAAa,MAAM;AAAA,IAChD,MAAM,mCAAS;AAAA,IACf,iBAAiB,mCAAS;AAAA,EAC5B;AACA,QAAM,eAAe,mBAAiB;AACpC,QAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,aAAO,cAAc,SAAS,KAAK,cAAc,KAAK,YAAY;AAAA,IACpE;AACA,WAAO,QAAQ,aAAa;AAAA,EAC9B;AACA,QAAM,aAAa,aAAa,QAAQ;AACxC,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,kBAAkB,MAAM,mBAAmB;AACjD,QAAM,uBAAuB,qBAAa;AAAA,IACxC,aAAa;AAAA,IACb,YAAY,CAAC;AAAA,IACb,mBAAmB,2CAAa;AAAA,IAChC,iBAAiB,SAAS;AAAA,MACxB,eAAe;AAAA,MACf,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,MAAM;AAAA,IACR,GAAG,yBAAyB;AAAA,MAC1B,wBAAwB;AAAA,IAC1B,IAAI,CAAC,CAAC;AAAA,IACN,WAAW,QAAQ;AAAA,EACrB,CAAC;AACD,QAAM,2BAA2B,WAAS;AACxC,QAAI,qBAAqB,iBAAiB;AACxC,sBAAgB,KAAK;AAAA,IACvB;AAAA,EACF;AACA,QAAM,gBAAgB,WAAW,MAAM,eAAe,MAAM;AAC5D,QAAM,gBAAgB,qBAAa;AAAA,IAC/B,aAAa;AAAA,IACb,YAAY,CAAC;AAAA,IACb,mBAAmB,oBAAkB;AACnC,UAAI,UAAU;AACZ,eAAO,SAAS,CAAC,GAAG,8BAAsB,aAAa,UAAU,cAAc,cAAc,GAAG,8BAAsB,2CAAa,cAAc,cAAc,CAAC;AAAA,MAClK;AACA,aAAO,SAAS,CAAC,GAAG,8BAAsB,aAAa,UAAU,YAAY,cAAc,GAAG,8BAAsB,2CAAa,YAAY,cAAc,CAAC;AAAA,IAC9J;AAAA,IACA,iBAAiB;AAAA,MACf,SAAS;AAAA,IACX;AAAA,EACF,CAAC,GACD,qBAAqB,8BAA8B,eAAeC,WAAU;AAC9E,QAAM,gBAAgB,cAAc,CAAC,CAAC,oBAA6B,oBAAAK,KAAK,eAAe,SAAS,CAAC,GAAG,kBAAkB,CAAC,IAAI;AAC3H,QAAM,cAAc,aAAa,SAAY,MAAM;AACnD,QAAM,iBAAiB,qBAAa;AAAA,IAChC,aAAa;AAAA,IACb,YAAY,CAAC;AAAA,IACb,mBAAmB,oBAAkB;AACnC,UAAI,YAAY;AACd,eAAO,CAAC;AAAA,MACV;AACA,aAAO,SAAS,CAAC,GAAG,8BAAsB,aAAa,UAAU,SAAS,cAAc,GAAG,8BAAsB,2CAAa,SAAS,cAAc,CAAC;AAAA,IACxJ;AAAA,EACF,CAAC,GACD,mBAAmB,8BAA8B,gBAAgBJ,WAAU;AAC7E,QAAM,cAAc,kBAA2B,oBAAAI,KAAK,aAAa,SAAS,CAAC,GAAG,gBAAgB,CAAC,IAAI;AACnG,QAAM,OAAO,MAAM;AACnB,QAAM,iBAAiB,qBAAa;AAAA,IAChC,aAAa;AAAA,IACb,YAAY,CAAC;AAAA,IACb,mBAAmB,2CAAa;AAAA,EAClC,CAAC,GACD,YAAY,8BAA8B,gBAAgBH,WAAU;AACtE,QAAM,OAAO,WAAoB,oBAAAG,KAAK,MAAM,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI;AACvE,MAAI;AACJ,MAAI,aAAa;AACf,mBAAe;AAAA,EACjB,WAAW,UAAU;AAOnB,mBAAe;AAAA,EACjB;AACA,WAAS,YAAY,OAAO;AAC1B,UAAM,eAAe,CAAC,YAAY;AAClC,QAAI,CAAC,WAAW,gBAAgB,MAAM,kBAAkB,MAAM,QAAQ;AACpE,eAAS,UAAU,OAAO,MAAM;AAAA,IAClC;AAAA,EACF;AACA,WAAS,WAAW,OAAO;AApU7B,QAAAC,KAAAC,KAAAC,KAAAC;AAqUI,qCAAS;AACT,QAAI;AAAA;AAAA,IAGJ,MAAM,iBAAiB,sBAAsB,MAAM,eAAe,cAAc,OAAO,MAAM,MAAM,YAAUF,OAAAD,MAAA,MAAM,WAAN,gBAAAA,IAAc,YAAd,gBAAAC,IAAuB,aAAY,gBAAgB,sBAAsB,MAAM,QAAQ,cAAc,OAAO,OAAKE,OAAAD,MAAA,MAAM,kBAAN,gBAAAA,IAAqB,YAArB,gBAAAC,IAA8B,aAAY,eAAe;AACrR;AAAA,IACF;AACA,aAAS,kBAAkB;AAAA,EAC7B;AACA,QAAM,gBAAgB,WAAS;AA9UjC,QAAAH,KAAAC;AA+UI,2CAAY;AACZ,UAAIA,OAAAD,MAAA,MAAM,WAAN,gBAAAA,IAAc,YAAd,gBAAAC,IAAuB,aAAY,cAAc;AACnD;AAAA,IACF;AACA,aAAS,kBAAkB,OAAO,MAAM;AAAA,EAC1C;AACA,QAAM,cAAc,SAAS,uBAAuB,QAAQ,EAAE;AAC9D,QAAM,WAAW,SAAS,gBAAgB,MAAM,IAAI,IAAI;AACxD,QAAM,4BAA4B;AAAA,IAChC;AAAA,IACA;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,sBAAoB,oBAAe,SAAf,wCAAsB,SAAS,CAAC,GAAG,2BAA2B;AAAA,IACtF,uBAAuB,6BAAqB,KAAK;AAAA,EACnD,CAAC,OAAM,CAAC;AACR,QAAM,yBAAuB,oBAAe,YAAf,wCAAyB,SAAS,CAAC,GAAG,2BAA2B;AAAA,IAC5F,uBAAuB,6BAAqB,YAAY;AAAA,EAC1D,CAAC,OAAM,CAAC;AACR,QAAM,oCAAkC,oBAAe,uBAAf,wCAAoC,SAAS,CAAC,GAAG,2BAA2B;AAAA,IAClH,uBAAuB,CAAC;AAAA,EAC1B,CAAC,OAAM,CAAC;AACR,QAAM,4BAA0B,oBAAe,eAAf,wCAA4B,SAAS,CAAC,GAAG,2BAA2B;AAAA,IAClG,uBAAuB,CAAC;AAAA,EAC1B,CAAC,OAAM,CAAC;AACR,aAAoB,oBAAAF,KAAK,mBAAmB;AAAA,IAC1C;AAAA,IACA,cAAuB,oBAAAK,MAAM,cAAc,SAAS;AAAA,MAClD,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,MACvC,MAAM;AAAA,MACN,iBAAiB,aAAa,WAAW;AAAA,MACzC,iBAAiB;AAAA,MACjB,iBAAiB,YAAY;AAAA,MAC7B,IAAI;AAAA,MACJ;AAAA,IACF,GAAG,OAAO;AAAA,MACR;AAAA,MACA,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,KAAK;AAAA,MACL,OAAO,yBAAyB,SAAS,CAAC,GAAG,MAAM,OAAO;AAAA,QACxD,wBAAwB,OAAO,iBAAiB,aAAa,aAAa,MAAM,IAAI;AAAA,MACtF,CAAC,IAAI,MAAM;AAAA,IACb,GAAG,mBAAmB;AAAA,MACpB,UAAU,KAAc,oBAAAL,KAAK,uBAAuB,SAAS;AAAA,QAC3D,IAAI;AAAA,QACJ,SAAS;AAAA,UACP,MAAM,QAAQ;AAAA,UACd,UAAU,QAAQ;AAAA,UAClB,UAAU,QAAQ;AAAA,UAClB,SAAS,QAAQ;AAAA,UACjB,UAAU,QAAQ;AAAA,UAClB,UAAU,QAAQ;AAAA,UAClB,SAAS,QAAQ;AAAA,UACjB,eAAe,QAAQ;AAAA,UACvB,OAAO,QAAQ;AAAA,UACf,YAAY,QAAQ;AAAA,UACpB,UAAU,QAAQ;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,cAAc,sBAAsB,gCAAgC,UAAU,OAAO,CAAC,IAAI;AAAA,QAC3F,yBAAyB;AAAA,MAC3B,GAAG,wBAAwB,SAAS,OAAO,CAAC,IAAI;AAAA,QAC9C,iBAAiB;AAAA,MACnB,GAAG;AAAA,QACD,KAAK;AAAA,MACP,CAAC,CAAC,GAAG,gBAAyB,oBAAAA,KAAK,eAAe,SAAS;AAAA,QACzD,IAAI;AAAA,MACN,GAAG,sBAAsB;AAAA,QACvB;AAAA,MACF,CAAC,CAAC,CAAC;AAAA,IACL,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,OAAwC,SAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ3D,UAAU,mBAAAM,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,mBAAAA,QAAU;AAAA,EACnB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,kBAAkB;AAAA;AAAA;AAAA;AAAA,EAIlB,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,QAAQ,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAIzB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,SAAS;AAAA;AAAA;AAAA;AAAA,EAIT,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;;;A5BndJ,IAAAC,sBAA4B;AAC5B,IAAMC,iBAAgB,oBAAoB,iBAAiB;AAC3D,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,EACf;AACA,SAAO,eAAe,OAAO,6BAA6B,OAAO;AACnE;AACO,IAAM,mBAAmB,eAAO,MAAM;AAAA,EAC3C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU;AACZ,CAAC;AACD,SAAS,gBAAgB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,QAAO,+BAAO,SAAQ;AAC5B,QAAM,YAAY,qBAAa;AAAA,IAC7B,aAAa;AAAA,IACb,mBAAmB,uCAAW;AAAA,IAC9B,iBAAiB;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAoB,oBAAAC,KAAK,MAAM,SAAS,CAAC,GAAG,WAAW;AAAA,IACrD;AAAA,EACF,CAAC,CAAC;AACJ;AAYA,IAAM,eAAkC,mBAAW,SAASC,cAAa,SAAS,KAAK;AACrF,QAAM,QAAQH,eAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,MAAI,MAAuC;AACzC,QAAI,MAAM,YAAY,MAAM;AAC1B,eAAS,CAAC,sEAAsE,+DAA+D,oGAAoG,CAAC;AAAA,IACtP;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,YAAY;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT;AAAA,EACF,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAUC,mBAAkB,KAAK;AACvC,QAAM,QAAO,+BAAO,SAAQ;AAC5B,QAAM,YAAY,qBAAa;AAAA,IAC7B,aAAa;AAAA,IACb,mBAAmB,uCAAW;AAAA,IAC9B,WAAW,QAAQ;AAAA,IACnB,cAAc;AAAA,IACd,YAAY;AAAA,EACd,CAAC;AACD,QAAM,gBAAgB,SAAS,iBAAiB;AAChD,QAAM,aAAa,CAAC;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,eAAoB,oBAAAC,KAAK,iBAAiB;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,qCAAU,IAAI;AAAA,IAC1B,GAAG,MAAM;AAAA,EACX;AACA,aAAoB,oBAAAA,KAAK,kBAAkB;AAAA,IACzC,OAAO;AAAA,IACP,cAAuB,oBAAAA,KAAK,MAAM,SAAS,CAAC,GAAG,WAAW;AAAA,MACxD,UAAU,cAAc,IAAI,UAAU;AAAA,IACxC,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,OAAwC,aAAa,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ/D,QAAQ,mBAAAE,QAAU,MAAM;AAAA,IACtB,SAAS,mBAAAA,QAAU,MAAM;AAAA,MACvB,WAAW,mBAAAA,QAAU,KAAK;AAAA,MAC1B,SAAS,mBAAAA,QAAU,KAAK;AAAA,MACxB,mBAAmB,mBAAAA,QAAU,KAAK;AAAA,MAClC,2BAA2B,mBAAAA,QAAU,KAAK;AAAA,MAC1C,aAAa,mBAAAA,QAAU,KAAK;AAAA,MAC5B,YAAY,mBAAAA,QAAU,KAAK;AAAA,MAC3B,kBAAkB,mBAAAA,QAAU,KAAK;AAAA,MACjC,iBAAiB,mBAAAA,QAAU,KAAK;AAAA,IAClC,CAAC;AAAA,EACH,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI7B,SAAS,mBAAAA,QAAU;AAAA,EACnB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,sBAAsB,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxD,sBAAsB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,wBAAwB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,kBAAkB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,eAAe,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjD,kBAAkB,mBAAAA,QAAU,MAAM,CAAC,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9D,sBAAsB,mBAAAA,QAAU,MAAM;AAAA,IACpC,wBAAwB,mBAAAA,QAAU;AAAA,IAClC,cAAc,mBAAAA,QAAU;AAAA,EAC1B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,IAAI,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOd,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU1B,gBAAgB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpE,yBAAyB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACjF,OAAO,mBAAAA,QAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,uBAAuB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjC,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvB,uBAAuB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjC,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,mBAAmB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7B,uBAAuB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjC,uBAAuB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;", "names": ["React", "import_prop_types", "React", "React", "React", "React", "React", "_excluded", "_jsx", "React", "React", "React", "React", "registryContainer", "useInstanceEventHandler", "React", "React", "import_prop_types", "React", "React", "import_jsx_runtime", "_jsx", "import_jsx_runtime", "_excluded", "TreeItemContent", "_jsxs", "_jsx", "PropTypes", "React", "import_jsx_runtime", "_jsx", "import_prop_types", "PropTypes", "import_jsx_runtime", "_excluded", "_excluded2", "_excluded3", "_excluded4", "useThemeProps", "TreeItem", "_jsx", "_a", "_b", "_c", "_d", "_jsxs", "PropTypes", "import_jsx_runtime", "useThemeProps", "useUtilityClasses", "_jsx", "Rich<PERSON>reeView", "PropTypes"]}