{"version": 3, "sources": ["../../@mui/material/utils/useSlot.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"elementType\", \"ownerState\", \"externalForwardedProps\", \"getSlotOwnerState\", \"internalForwardedProps\"],\n  _excluded2 = [\"component\", \"slots\", \"slotProps\"],\n  _excluded3 = [\"component\"];\nimport useForkRef from '@mui/utils/useForkRef';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport mergeSlotProps from '@mui/utils/mergeSlotProps';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n      className,\n      elementType: initialElementType,\n      ownerState,\n      externalForwardedProps,\n      getSlotOwnerState,\n      internalForwardedProps\n    } = parameters,\n    useSlotPropsParams = _objectWithoutPropertiesLoose(parameters, _excluded);\n  const {\n      component: rootComponent,\n      slots = {\n        [name]: undefined\n      },\n      slotProps = {\n        [name]: undefined\n      }\n    } = externalForwardedProps,\n    other = _objectWithoutPropertiesLoose(externalForwardedProps, _excluded2);\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const _mergeSlotProps = mergeSlotProps(_extends({\n      className\n    }, useSlotPropsParams, {\n      externalForwardedProps: name === 'root' ? other : undefined,\n      externalSlotProps: resolvedComponentsProps\n    })),\n    {\n      props: {\n        component: slotComponent\n      },\n      internalRef\n    } = _mergeSlotProps,\n    mergedProps = _objectWithoutPropertiesLoose(_mergeSlotProps.props, _excluded3);\n  const ref = useForkRef(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, parameters.ref);\n  const slotOwnerState = getSlotOwnerState ? getSlotOwnerState(mergedProps) : {};\n  const finalOwnerState = _extends({}, ownerState, slotOwnerState);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, _extends({}, name === 'root' && !rootComponent && !slots[name] && internalForwardedProps, name !== 'root' && !slots[name] && internalForwardedProps, mergedProps, LeafComponent && {\n    as: LeafComponent\n  }, {\n    ref\n  }), finalOwnerState);\n  Object.keys(slotOwnerState).forEach(propName => {\n    delete props[propName];\n  });\n  return [elementType, props];\n}"], "mappings": ";;;;;;;;;;;;;;;AAEA;AAEA,IAAM,YAAY,CAAC,aAAa,eAAe,cAAc,0BAA0B,qBAAqB,wBAAwB;AAApI,IACE,aAAa,CAAC,aAAa,SAAS,WAAW;AADjD,IAEE,aAAa,CAAC,WAAW;AAmBZ,SAAR,QAOP,MAAM,YAAY;AAChB,QAAM;AAAA,IACF;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,YACJ,qBAAqB,8BAA8B,YAAY,SAAS;AAC1E,QAAM;AAAA,IACF,WAAW;AAAA,IACX,QAAQ;AAAA,MACN,CAAC,IAAI,GAAG;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,CAAC,IAAI,GAAG;AAAA,IACV;AAAA,EACF,IAAI,wBACJ,QAAQ,8BAA8B,wBAAwB,UAAU;AAC1E,QAAM,cAAc,MAAM,IAAI,KAAK;AAInC,QAAM,0BAA0B,8BAAsB,UAAU,IAAI,GAAG,UAAU;AACjF,QAAM,kBAAkB,uBAAe,SAAS;AAAA,IAC5C;AAAA,EACF,GAAG,oBAAoB;AAAA,IACrB,wBAAwB,SAAS,SAAS,QAAQ;AAAA,IAClD,mBAAmB;AAAA,EACrB,CAAC,CAAC,GACF;AAAA,IACE,OAAO;AAAA,MACL,WAAW;AAAA,IACb;AAAA,IACA;AAAA,EACF,IAAI,iBACJ,cAAc,8BAA8B,gBAAgB,OAAO,UAAU;AAC/E,QAAM,MAAM,WAAW,aAAa,2BAA2B,OAAO,SAAS,wBAAwB,KAAK,WAAW,GAAG;AAC1H,QAAM,iBAAiB,oBAAoB,kBAAkB,WAAW,IAAI,CAAC;AAC7E,QAAM,kBAAkB,SAAS,CAAC,GAAG,YAAY,cAAc;AAC/D,QAAM,gBAAgB,SAAS,SAAS,iBAAiB,gBAAgB;AACzE,QAAM,QAAQ,yBAAiB,aAAa,SAAS,CAAC,GAAG,SAAS,UAAU,CAAC,iBAAiB,CAAC,MAAM,IAAI,KAAK,wBAAwB,SAAS,UAAU,CAAC,MAAM,IAAI,KAAK,wBAAwB,aAAa,iBAAiB;AAAA,IAC7N,IAAI;AAAA,EACN,GAAG;AAAA,IACD;AAAA,EACF,CAAC,GAAG,eAAe;AACnB,SAAO,KAAK,cAAc,EAAE,QAAQ,cAAY;AAC9C,WAAO,MAAM,QAAQ;AAAA,EACvB,CAAC;AACD,SAAO,CAAC,aAAa,KAAK;AAC5B;", "names": []}