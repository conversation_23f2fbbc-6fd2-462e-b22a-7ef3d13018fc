import { useState, useEffect, useContext } from 'react';
import { useParams } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  TableContainer,
  Paper,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TextField,
  Chip,
} from '@mui/material';
import { useSettingsContext } from '@/components/settings';
import { LoadingScreen } from '@/components/loading-screen';
import { KpiModelContext } from '@/core/contexts/kpimodel.context';
import OverrideFetchMechanism from '../components/override-fetch-mechnism.component';
import { fetchMechanismOptions } from '@/shared/enum/common.enum';

export default function CurrentStateView() {
  const settings = useSettingsContext();
  const [filteredKpis, setFilteredKpis] = useState<any[]>([]);
  const [searchText, setSearchText] = useState('');

  const { modelScenarioData, kpiConfigs, loading, modelId, refreshData, modelData } = useContext(KpiModelContext);

  const handleFetchMechanismUpdate = () => {
    refreshData();
  };

  useEffect(() => {
    if (kpiConfigs) {
      var pull_kpis = kpiConfigs.filter(
        (x) =>
          x.kpi_type === 'pull_data' &&
          (!searchText ||
            x.title?.toLocaleLowerCase().includes(searchText?.toLocaleLowerCase()) ||
            x.system_code?.toLocaleLowerCase().includes(searchText?.toLocaleLowerCase())),
      );
      pull_kpis = pull_kpis.sort((a, b) => a.data_source.localeCompare(b.data_source));
      setFilteredKpis(pull_kpis);
    }
  }, [kpiConfigs, searchText]);

  const getKpiValue = (kpiCode: string, kpiValues: Record<string, any>) => {
    return kpiValues[kpiCode];
  };

  const handleSearchChange = (event: any) => {
    setSearchText(event.target.value);
  };

  if (loading) {
    return <LoadingScreen />;
  }

  if (!modelScenarioData || modelScenarioData.length === 0) {
    return (
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <Box sx={{ py: 5, textAlign: 'center' }}>
          <Typography variant="h6">Model not found or has no scenarios</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth={false} disableGutters sx={{ mt: 0 }}>
      <OverrideFetchMechanism modelId={Number(modelId)} modelData={modelData} onUpdate={handleFetchMechanismUpdate} />
      <Grid container spacing={3}>
        <Grid item xs={12}>
          {modelScenarioData && modelScenarioData.length > 0 && (
            <Box>
              <Card sx={{ mb: 2, pt: 2 }}>
                <CardContent sx={{ px: 2, py: 0 }}>
                  <TextField
                    label="Search"
                    variant="outlined"
                    fullWidth
                    value={searchText}
                    onChange={handleSearchChange}
                  />

                  {/* <Typography variant="body2" sx={{ mt: 1 }}>
                    Searching for: <strong>{searchText}</strong>
                  </Typography> */}
                </CardContent>
              </Card>
              <Card sx={{ mb: 2 }}>
                <CardContent sx={{ px: 2, py: 0 }}>
                  <TableContainer component={Paper}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell width="50%">Name</TableCell>
                          <TableCell width="25%" sx={{ textAlign: 'center' }}>
                            Source
                          </TableCell>
                          <TableCell width="25%" sx={{ textAlign: 'center' }}>
                            Current State
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {filteredKpis.map((kpi) => {
                          const parsedKpiValues =
                            typeof modelScenarioData[0]?.kpi_data === 'string'
                              ? JSON.parse(modelScenarioData[0].kpi_data)
                              : modelScenarioData[0]?.kpi_data || {};

                          const value = getKpiValue(kpi.system_code, parsedKpiValues);

                          const fetchMechanism =
                            !modelData?.override_fetch_mechanism_type ||
                            modelData?.override_fetch_mechanism_type === 'default'
                              ? kpi.kpi_config?.fetch_mechanism
                              : modelData?.override_fetch_mechanism_type;

                          var fetchMechanismLabel = fetchMechanismOptions.find(
                            (x) => x.value === fetchMechanism,
                          )?.label;

                          return (
                            <TableRow
                              key={kpi.system_code}
                              sx={{
                                backgroundColor: kpi.kpi_type === 'formula' ? '#eeeeee' : 'inherit',
                                '& td': {
                                  borderColor: '#d0d0d0',
                                },
                              }}
                            >
                              <TableCell>
                                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                  <Typography variant="body2">
                                    {kpi.title || 'N/A'}{' '}
                                    <Chip
                                      label={fetchMechanismLabel ? fetchMechanismLabel : 'Single Value'}
                                      color="warning"
                                      size="small"
                                      sx={{ mx: 2, backgroundColor: '#e4e4e4;' }}
                                    />
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell align="center">
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                  <Typography variant="body2">{kpi.data_source || 'N/A'}</Typography>
                                </Box>
                              </TableCell>
                              <TableCell align="center">
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                  <Typography variant="body2">{value}</Typography>
                                </Box>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                        {filteredKpis.length === 0 && (
                          <TableRow>
                            <TableCell colSpan={2} sx={{ textAlign: 'center', color: 'red' }}>
                              <Typography variant="body2" color="text.secondary">
                                No KPIs found
                              </Typography>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Box>
          )}
        </Grid>
      </Grid>
    </Container>
  );
}
