{"version": 3, "sources": ["../../react-router-dom/dom.ts", "../../react-router-dom/index.tsx"], "sourcesContent": ["import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\n/**\n * Submit options shared by both navigations and fetchers\n */\ninterface SharedSubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * Enable flushSync for this submission's state updates\n   */\n  unstable_flushSync?: boolean;\n}\n\n/**\n * Submit options available to fetchers\n */\nexport interface FetcherSubmitOptions extends SharedSubmitOptions {}\n\n/**\n * Submit options available to navigations\n */\nexport interface SubmitOptions extends FetcherSubmitOptions {\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Enable view transitions on this submission navigation\n   */\n  unstable_viewTransition?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport type {\n  DataRouteObject,\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  Navigator,\n  RelativeRoutingType,\n  RouteObject,\n  RouterProps,\n  RouterProviderProps,\n  To,\n  unstable_DataStrategyFunction,\n  unstable_PatchRoutesOnNavigationFunction,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n  UNSAFE_useRoutesImpl as useRoutesImpl,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n  RouterState,\n  RouterSubscriber,\n  BlockerFunction,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  UNSAFE_ErrorResponseImpl as ErrorResponseImpl,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n  matchPath,\n  IDLE_FETCHER,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n  FetcherSubmitOptions,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams, ErrorResponseImpl as UNSAFE_ErrorResponseImpl };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker,\n  BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  unstable_DataStrategyFunction,\n  unstable_DataStrategyFunctionArgs,\n  unstable_DataStrategyMatch,\n  unstable_DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  unstable_PatchRoutesOnNavigationFunction,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n  var __reactRouterVersion: string;\n  interface Document {\n    startViewTransition(cb: () => Promise<void> | void): ViewTransition;\n  }\n}\n\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"0\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  unstable_dataStrategy?: unstable_DataStrategyFunction;\n  unstable_patchRoutesOnNavigation?: unstable_PatchRoutesOnNavigationFunction;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    unstable_dataStrategy: opts?.unstable_dataStrategy,\n    unstable_patchRoutesOnNavigation: opts?.unstable_patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    unstable_dataStrategy: opts?.unstable_dataStrategy,\n    unstable_patchRoutesOnNavigation: opts?.unstable_patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Contexts\n////////////////////////////////////////////////////////////////////////////////\n\ntype ViewTransitionContextObject =\n  | {\n      isTransitioning: false;\n    }\n  | {\n      isTransitioning: true;\n      flushSync: boolean;\n      currentLocation: Location;\n      nextLocation: Location;\n    };\n\nconst ViewTransitionContext = React.createContext<ViewTransitionContextObject>({\n  isTransitioning: false,\n});\nif (__DEV__) {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\n\nexport { ViewTransitionContext as UNSAFE_ViewTransitionContext };\n\n// TODO: (v7) Change the useFetcher data from `any` to `unknown`\ntype FetchersContextObject = Map<string, any>;\n\nconst FetchersContext = React.createContext<FetchersContextObject>(new Map());\nif (__DEV__) {\n  FetchersContext.displayName = \"Fetchers\";\n}\n\nexport { FetchersContext as UNSAFE_FetchersContext };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\n\nfunction startTransitionSafe(cb: () => void) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\n\nfunction flushSyncSafe(cb: () => void) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\n\ninterface ViewTransition {\n  finished: Promise<void>;\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  skipTransition(): void;\n}\n\nclass Deferred<T> {\n  status: \"pending\" | \"resolved\" | \"rejected\" = \"pending\";\n  promise: Promise<T>;\n  // @ts-expect-error - no initializer\n  resolve: (value: T) => void;\n  // @ts-expect-error - no initializer\n  reject: (reason?: unknown) => void;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState<RouterState>();\n  let [vtContext, setVtContext] = React.useState<ViewTransitionContextObject>({\n    isTransitioning: false,\n  });\n  let [renderDfd, setRenderDfd] = React.useState<Deferred<void>>();\n  let [transition, setTransition] = React.useState<ViewTransition>();\n  let [interruption, setInterruption] = React.useState<{\n    state: RouterState;\n    currentLocation: Location;\n    nextLocation: Location;\n  }>();\n  let fetcherData = React.useRef<Map<string, any>>(new Map());\n  let { v7_startTransition } = future || {};\n\n  let optInStartTransition = React.useCallback(\n    (cb: () => void) => {\n      if (v7_startTransition) {\n        startTransitionSafe(cb);\n      } else {\n        cb();\n      }\n    },\n    [v7_startTransition]\n  );\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (\n      newState: RouterState,\n      {\n        deletedFetchers,\n        unstable_flushSync: flushSync,\n        unstable_viewTransitionOpts: viewTransitionOpts,\n      }\n    ) => {\n      deletedFetchers.forEach((key) => fetcherData.current.delete(key));\n      newState.fetchers.forEach((fetcher, key) => {\n        if (fetcher.data !== undefined) {\n          fetcherData.current.set(key, fetcher.data);\n        }\n      });\n\n      let isViewTransitionUnavailable =\n        router.window == null ||\n        router.window.document == null ||\n        typeof router.window.document.startViewTransition !== \"function\";\n\n      // If this isn't a view transition or it's not available in this browser,\n      // just update and be done with it\n      if (!viewTransitionOpts || isViewTransitionUnavailable) {\n        if (flushSync) {\n          flushSyncSafe(() => setStateImpl(newState));\n        } else {\n          optInStartTransition(() => setStateImpl(newState));\n        }\n        return;\n      }\n\n      // flushSync + startViewTransition\n      if (flushSync) {\n        // Flush through the context to mark DOM elements as transition=ing\n        flushSyncSafe(() => {\n          // Cancel any pending transitions\n          if (transition) {\n            renderDfd && renderDfd.resolve();\n            transition.skipTransition();\n          }\n          setVtContext({\n            isTransitioning: true,\n            flushSync: true,\n            currentLocation: viewTransitionOpts.currentLocation,\n            nextLocation: viewTransitionOpts.nextLocation,\n          });\n        });\n\n        // Update the DOM\n        let t = router.window!.document.startViewTransition(() => {\n          flushSyncSafe(() => setStateImpl(newState));\n        });\n\n        // Clean up after the animation completes\n        t.finished.finally(() => {\n          flushSyncSafe(() => {\n            setRenderDfd(undefined);\n            setTransition(undefined);\n            setPendingState(undefined);\n            setVtContext({ isTransitioning: false });\n          });\n        });\n\n        flushSyncSafe(() => setTransition(t));\n        return;\n      }\n\n      // startTransition + startViewTransition\n      if (transition) {\n        // Interrupting an in-progress transition, cancel and let everything flush\n        // out, and then kick off a new transition from the interruption state\n        renderDfd && renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      } else {\n        // Completed navigation update with opted-in view transitions, let 'er rip\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          flushSync: false,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      }\n    },\n    [router.window, transition, renderDfd, fetcherData, optInStartTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred<void>());\n    }\n  }, [vtContext]);\n\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (\n      renderDfd &&\n      pendingState &&\n      state.location.key === pendingState.location.key\n    ) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation,\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  let routerFuture = React.useMemo<RouterProps[\"future\"]>(\n    () => ({\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n    }),\n    [router.future.v7_relativeSplatPath]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <FetchersContext.Provider value={fetcherData.current}>\n            <ViewTransitionContext.Provider value={vtContext}>\n              <Router\n                basename={basename}\n                location={state.location}\n                navigationType={state.historyAction}\n                navigator={navigator}\n                future={routerFuture}\n              >\n                {state.initialized || router.future.v7_partialHydration ? (\n                  <MemoizedDataRoutes\n                    routes={router.routes}\n                    future={router.future}\n                    state={state}\n                  />\n                ) : (\n                  fallbackElement\n                )}\n              </Router>\n            </ViewTransitionContext.Provider>\n          </FetchersContext.Provider>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = React.memo(DataRoutes);\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n  unstable_viewTransition?: boolean;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      unstable_viewTransition,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      unstable_viewTransition,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport type NavLinkRenderProps = {\n  isActive: boolean;\n  isPending: boolean;\n  isTransitioning: boolean;\n};\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?: React.ReactNode | ((props: NavLinkRenderProps) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: NavLinkRenderProps) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: NavLinkRenderProps) => React.CSSProperties | undefined);\n}\n\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      unstable_viewTransition,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator, basename } = React.useContext(NavigationContext);\n    let isTransitioning =\n      routerState != null &&\n      // Conditional usage is OK here because the usage of a data router is static\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useViewTransitionState(path) &&\n      unstable_viewTransition === true;\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    if (nextLocationPathname && basename) {\n      nextLocationPathname =\n        stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n    }\n\n    // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n    // we're looking for a slash _after_ what's in `to`.  For example:\n    //\n    // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n    // both want to look for a / at index 6 to match URL `/users/matt`\n    const endSlashPosition =\n      toPathname !== \"/\" && toPathname.endsWith(\"/\")\n        ? toPathname.length - 1\n        : toPathname.length;\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(endSlashPosition) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning,\n    };\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n        unstable_viewTransition={unstable_viewTransition}\n      >\n        {typeof children === \"function\" ? children(renderProps) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n/**\n * Form props shared by navigations and fetchers\n */\ninterface SharedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * Form props available to fetchers\n */\nexport interface FetcherFormProps extends SharedFormProps {}\n\n/**\n * Form props available to navigations\n */\nexport interface FormProps extends SharedFormProps {\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Enable view transitions on this Form navigation\n   */\n  unstable_viewTransition?: boolean;\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (\n    {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      unstable_viewTransition,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmit();\n    let formAction = useFormAction(action, { relative });\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        fetcherKey,\n        method: submitMethod,\n        navigate,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n        unstable_viewTransition,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n  useViewTransitionState = \"useViewTransitionState\",\n}\n\nenum DataRouterStateHook {\n  UseFetcher = \"useFetcher\",\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\n// Internal hooks\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n// External hooks\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    unstable_viewTransition,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n    unstable_viewTransition?: boolean;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, {\n          replace,\n          state,\n          preventScrollReset,\n          relative,\n          unstable_viewTransition,\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      unstable_viewTransition,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: FetcherSubmitOptions\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => `__${String(++fetcherId)}__`;\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      if (options.navigate === false) {\n        let key = options.fetcherKey || getUniqueFetcherId();\n        router.fetch(key, currentRouteId, options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          unstable_flushSync: options.unstable_flushSync,\n        });\n      } else {\n        router.navigate(options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          replace: options.replace,\n          state: options.state,\n          fromRouteId: currentRouteId,\n          unstable_flushSync: options.unstable_flushSync,\n          unstable_viewTransition: options.unstable_viewTransition,\n        });\n      }\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    if (params.has(\"index\") && params.get(\"index\") === \"\") {\n      params.delete(\"index\");\n      path.search = params.toString() ? `?${params.toString()}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: React.ForwardRefExoticComponent<\n    FetcherFormProps & React.RefAttributes<HTMLFormElement>\n  >;\n  submit: FetcherSubmitFunction;\n  load: (href: string, opts?: { unstable_flushSync?: boolean }) => void;\n};\n\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>({\n  key,\n}: { key?: string } = {}): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(RouteContext);\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n\n  invariant(fetcherData, `useFetcher must be used inside a FetchersContext`);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState<string>(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  // Fetcher additions\n  let load = React.useCallback(\n    (href: string, opts?: { unstable_flushSync?: boolean }) => {\n      invariant(routeId, \"No routeId available for fetcher.load()\");\n      router.fetch(fetcherKey, routeId, href, opts);\n    },\n    [fetcherKey, routeId, router]\n  );\n\n  let submitImpl = useSubmit();\n  let submit = React.useCallback<FetcherSubmitFunction>(\n    (target, opts) => {\n      submitImpl(target, {\n        ...opts,\n        navigate: false,\n        fetcherKey,\n      });\n    },\n    [fetcherKey, submitImpl]\n  );\n\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n      (props, ref) => {\n        return (\n          <Form {...props} navigate={false} fetcherKey={fetcherKey} ref={ref} />\n        );\n      }\n    );\n    if (__DEV__) {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form: FetcherForm,\n      submit,\n      load,\n      ...fetcher,\n      data,\n    }),\n    [FetcherForm, submit, load, fetcher, data]\n  );\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): (Fetcher & { key: string })[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(([key, fetcher]) => ({\n    ...fetcher,\n    key,\n  }));\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({\n  when,\n  message,\n}: {\n  when: boolean | BlockerFunction;\n  message: string;\n}) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(\n  to: To,\n  opts: { relative?: RelativeRoutingType } = {}\n) {\n  let vtContext = React.useContext(ViewTransitionContext);\n\n  invariant(\n    vtContext != null,\n    \"`unstable_useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" +\n      \"Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n\n  let { basename } = useDataRouterContext(\n    DataRouterHook.useViewTransitionState\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n\n  let currentPath =\n    stripBasename(vtContext.currentLocation.pathname, basename) ||\n    vtContext.currentLocation.pathname;\n  let nextPath =\n    stripBasename(vtContext.nextLocation.pathname, basename) ||\n    vtContext.nextLocation.pathname;\n\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" unstable_viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" unstable_viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return (\n    matchPath(path.pathname, nextPath) != null ||\n    matchPath(path.pathname, currentPath) != null\n  );\n}\n\nexport { useViewTransitionState as unstable_useViewTransitionState };\n\n//#endregion\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAMA,gBAAgC;AAC7C,IAAMC,iBAA8B;AAE9B,SAAUC,cAAcC,QAAW;AACvC,SAAOA,UAAU,QAAQ,OAAOA,OAAOC,YAAY;AACrD;AAEM,SAAUC,gBAAgBF,QAAW;AACzC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAEM,SAAUC,cAAcJ,QAAW;AACvC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAEM,SAAUE,eAAeL,QAAW;AACxC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAOA,SAASG,gBAAgBC,OAAwB;AAC/C,SAAO,CAAC,EAAEA,MAAMC,WAAWD,MAAME,UAAUF,MAAMG,WAAWH,MAAMI;AACpE;AAEgB,SAAAC,uBACdL,OACAM,QAAe;AAEf,SACEN,MAAMO,WAAW;GAChB,CAACD,UAAUA,WAAW;EACvB,CAACP,gBAAgBC,KAAK;AAE1B;AA+BgB,SAAAQ,mBACdC,MAA8B;AAAA,MAA9BA,SAAA,QAAA;AAAAA,WAA4B;EAAE;AAE9B,SAAO,IAAIC,gBACT,OAAOD,SAAS,YAChBE,MAAMC,QAAQH,IAAI,KAClBA,gBAAgBC,kBACZD,OACAI,OAAOC,KAAKL,IAAI,EAAEM,OAAO,CAACC,OAAMC,QAAO;AACrC,QAAIC,QAAQT,KAAKQ,GAAG;AACpB,WAAOD,MAAKG,OACVR,MAAMC,QAAQM,KAAK,IAAIA,MAAME,IAAKC,OAAM,CAACJ,KAAKI,CAAC,CAAC,IAAI,CAAC,CAACJ,KAAKC,KAAK,CAAC,CAAC;KAEnE,CAAA,CAAyB,CAAC;AAErC;AAEgB,SAAAI,2BACdC,gBACAC,qBAA2C;AAE3C,MAAIC,eAAejB,mBAAmBe,cAAc;AAEpD,MAAIC,qBAAqB;AAMvBA,wBAAoBE,QAAQ,CAACC,GAAGV,QAAO;AACrC,UAAI,CAACQ,aAAaG,IAAIX,GAAG,GAAG;AAC1BO,4BAAoBK,OAAOZ,GAAG,EAAES,QAASR,WAAS;AAChDO,uBAAaK,OAAOb,KAAKC,KAAK;QAChC,CAAC;MACF;IACH,CAAC;EACF;AAED,SAAOO;AACT;AAoBA,IAAIM,6BAA6C;AAEjD,SAASC,+BAA4B;AACnC,MAAID,+BAA+B,MAAM;AACvC,QAAI;AACF,UAAIE;QACFC,SAASC,cAAc,MAAM;;QAE7B;MAAC;AAEHJ,mCAA6B;aACtBK,GAAG;AACVL,mCAA6B;IAC9B;EACF;AACD,SAAOA;AACT;AAgFA,IAAMM,wBAA0C,oBAAIC,IAAI,CACtD,qCACA,uBACA,YAAY,CACb;AAED,SAASC,eAAeC,SAAsB;AAC5C,MAAIA,WAAW,QAAQ,CAACH,sBAAsBT,IAAIY,OAAsB,GAAG;AACzEC,WAAAC,QACE,OACA,MAAIF,UACsBjD,+DAAAA,0BAAAA,iBAAc,IAAG,IAC5C;AAED,WAAO;EACR;AACD,SAAOiD;AACT;AAEgB,SAAAG,sBACdrC,QACAsC,UAAgB;AAQhB,MAAIC;AACJ,MAAIC;AACJ,MAAIN;AACJ,MAAIO;AACJ,MAAIC;AAEJ,MAAInD,cAAcS,MAAM,GAAG;AAIzB,QAAI2C,OAAO3C,OAAO4C,aAAa,QAAQ;AACvCJ,aAASG,OAAOE,cAAcF,MAAML,QAAQ,IAAI;AAChDC,aAASvC,OAAO4C,aAAa,QAAQ,KAAK5D;AAC1CkD,cAAUD,eAAejC,OAAO4C,aAAa,SAAS,CAAC,KAAK3D;AAE5DwD,eAAW,IAAId,SAAS3B,MAAM;aAE9BX,gBAAgBW,MAAM,KACrBR,eAAeQ,MAAM,MACnBA,OAAO8C,SAAS,YAAY9C,OAAO8C,SAAS,UAC/C;AACA,QAAIC,OAAO/C,OAAO+C;AAElB,QAAIA,QAAQ,MAAM;AAChB,YAAM,IAAIC,MAAK,oEACuD;IAEvE;AAOD,QAAIL,OAAO3C,OAAO4C,aAAa,YAAY,KAAKG,KAAKH,aAAa,QAAQ;AAC1EJ,aAASG,OAAOE,cAAcF,MAAML,QAAQ,IAAI;AAEhDC,aACEvC,OAAO4C,aAAa,YAAY,KAChCG,KAAKH,aAAa,QAAQ,KAC1B5D;AACFkD,cACED,eAAejC,OAAO4C,aAAa,aAAa,CAAC,KACjDX,eAAec,KAAKH,aAAa,SAAS,CAAC,KAC3C3D;AAGFwD,eAAW,IAAId,SAASoB,MAAM/C,MAAM;AAMpC,QAAI,CAAC0B,6BAA4B,GAAI;AACnC,UAAI;QAAEuB;QAAMH;QAAMlC;MAAK,IAAKZ;AAC5B,UAAI8C,SAAS,SAAS;AACpB,YAAII,SAASD,OAAUA,OAAI,MAAM;AACjCR,iBAASjB,OAAU0B,SAAM,KAAK,GAAG;AACjCT,iBAASjB,OAAU0B,SAAM,KAAK,GAAG;iBACxBD,MAAM;AACfR,iBAASjB,OAAOyB,MAAMrC,KAAK;MAC5B;IACF;EACF,WAAU1B,cAAcc,MAAM,GAAG;AAChC,UAAM,IAAIgD,MACR,oFAC+B;EAElC,OAAM;AACLT,aAASvD;AACTwD,aAAS;AACTN,cAAUjD;AACVyD,WAAO1C;EACR;AAGD,MAAIyC,YAAYP,YAAY,cAAc;AACxCQ,WAAOD;AACPA,eAAWU;EACZ;AAED,SAAO;IAAEX;IAAQD,QAAQA,OAAOjD,YAAW;IAAI4C;IAASO;IAAUC;;AACpE;;;;ACjGA,IAAAU,uBAAA;AAEA,IAAI;AACFC,SAAOC,uBAAuBF;AAC/B,SAAQtB,GAAG;AACV;AAgBc,SAAAyB,oBACdC,QACAC,MAAoB;AAEpB,SAAOC,aAAa;IAClBpB,UAAUmB,QAAAA,OAAAA,SAAAA,KAAMnB;IAChBqB,QAAMC,SAAA,CAAA,GACDH,QAAAA,OAAAA,SAAAA,KAAME,QAAM;MACfE,oBAAoB;KACrB;IACDC,SAASC,qBAAqB;MAAEV,QAAQI,QAAAA,OAAAA,SAAAA,KAAMJ;IAAM,CAAE;IACtDW,gBAAeP,QAAAA,OAAAA,SAAAA,KAAMO,kBAAiBC,mBAAkB;IACxDT;;IAEAU,uBAAuBT,QAAAA,OAAAA,SAAAA,KAAMS;IAC7BC,kCAAkCV,QAAAA,OAAAA,SAAAA,KAAMU;IACxCd,QAAQI,QAAAA,OAAAA,SAAAA,KAAMJ;GACf,EAAEe,WAAU;AACf;AAEgB,SAAAC,iBACdb,QACAC,MAAoB;AAEpB,SAAOC,aAAa;IAClBpB,UAAUmB,QAAAA,OAAAA,SAAAA,KAAMnB;IAChBqB,QAAMC,SAAA,CAAA,GACDH,QAAAA,OAAAA,SAAAA,KAAME,QAAM;MACfE,oBAAoB;KACrB;IACDC,SAASQ,kBAAkB;MAAEjB,QAAQI,QAAAA,OAAAA,SAAAA,KAAMJ;IAAM,CAAE;IACnDW,gBAAeP,QAAAA,OAAAA,SAAAA,KAAMO,kBAAiBC,mBAAkB;IACxDT;;IAEAU,uBAAuBT,QAAAA,OAAAA,SAAAA,KAAMS;IAC7BC,kCAAkCV,QAAAA,OAAAA,SAAAA,KAAMU;IACxCd,QAAQI,QAAAA,OAAAA,SAAAA,KAAMJ;GACf,EAAEe,WAAU;AACf;AAEA,SAASH,qBAAkB;AAAA,MAAAM;AACzB,MAAIC,SAAKD,UAAGlB,WAAAkB,OAAAA,SAAAA,QAAQE;AACpB,MAAID,SAASA,MAAME,QAAQ;AACzBF,YAAKZ,SAAA,CAAA,GACAY,OAAK;MACRE,QAAQC,kBAAkBH,MAAME,MAAM;KACvC;EACF;AACD,SAAOF;AACT;AAEA,SAASG,kBACPD,QAAsC;AAEtC,MAAI,CAACA,OAAQ,QAAO;AACpB,MAAIE,UAAUrE,OAAOqE,QAAQF,MAAM;AACnC,MAAIG,aAA6C,CAAA;AACjD,WAAS,CAAClE,KAAKmE,GAAG,KAAKF,SAAS;AAG9B,QAAIE,OAAOA,IAAIC,WAAW,sBAAsB;AAC9CF,iBAAWlE,GAAG,IAAI,IAAIqE,kBACpBF,IAAIG,QACJH,IAAII,YACJJ,IAAIK,MACJL,IAAIM,aAAa,IAAI;eAEdN,OAAOA,IAAIC,WAAW,SAAS;AAExC,UAAID,IAAIO,WAAW;AACjB,YAAIC,mBAAmBjC,OAAOyB,IAAIO,SAAS;AAC3C,YAAI,OAAOC,qBAAqB,YAAY;AAC1C,cAAI;AAEF,gBAAIC,QAAQ,IAAID,iBAAiBR,IAAIU,OAAO;AAG5CD,kBAAME,QAAQ;AACdZ,uBAAWlE,GAAG,IAAI4E;mBACXzD,GAAG;UACV;QAEH;MACF;AAED,UAAI+C,WAAWlE,GAAG,KAAK,MAAM;AAC3B,YAAI4E,QAAQ,IAAIvC,MAAM8B,IAAIU,OAAO;AAGjCD,cAAME,QAAQ;AACdZ,mBAAWlE,GAAG,IAAI4E;MACnB;IACF,OAAM;AACLV,iBAAWlE,GAAG,IAAImE;IACnB;EACF;AACD,SAAOD;AACT;AAmBA,IAAMa,wBAA8BC,oBAA2C;EAC7EC,iBAAiB;AAClB,CAAA;AACD,IAAAzD,MAAa;AACXuD,wBAAsBG,cAAc;AACrC;AAOKC,IAAAA,kBAAwBH,oBAAqC,oBAAII,IAAG,CAAE;AAC5E,IAAA5D,MAAa;AACX2D,kBAAgBD,cAAc;AAC/B;AA+BD,IAAMG,mBAAmB;AACzB,IAAMC,sBAAsBC,MAAMF,gBAAgB;AAClD,IAAMG,aAAa;AACnB,IAAMC,gBAAgBC,SAASF,UAAU;AACzC,IAAMG,SAAS;AACf,IAAMC,YAAYL,MAAMI,MAAM;AAE9B,SAASE,oBAAoBC,IAAc;AACzC,MAAIR,qBAAqB;AACvBA,wBAAoBQ,EAAE;EACvB,OAAM;AACLA,OAAE;EACH;AACH;AAEA,SAASC,cAAcD,IAAc;AACnC,MAAIL,eAAe;AACjBA,kBAAcK,EAAE;EACjB,OAAM;AACLA,OAAE;EACH;AACH;AASA,IAAME,WAAN,MAAc;EAOZC,cAAA;AANA,SAAM3B,SAAwC;AAO5C,SAAK4B,UAAU,IAAIC,QAAQ,CAACC,SAASC,WAAU;AAC7C,WAAKD,UAAWnG,WAAS;AACvB,YAAI,KAAKqE,WAAW,WAAW;AAC7B,eAAKA,SAAS;AACd8B,kBAAQnG,KAAK;QACd;;AAEH,WAAKoG,SAAUC,YAAU;AACvB,YAAI,KAAKhC,WAAW,WAAW;AAC7B,eAAKA,SAAS;AACd+B,iBAAOC,MAAM;QACd;;IAEL,CAAC;EACH;AACD;AAKK,SAAUC,eAAcC,MAIR;AAAA,MAJS;IAC7BC;IACAC;IACA1D;EACoB,IAAAwD;AACpB,MAAI,CAAC3C,OAAO8C,YAAY,IAAUC,eAASF,OAAO7C,KAAK;AACvD,MAAI,CAACgD,cAAcC,eAAe,IAAUF,eAAQ;AACpD,MAAI,CAACG,WAAWC,YAAY,IAAUJ,eAAsC;IAC1E3B,iBAAiB;EAClB,CAAA;AACD,MAAI,CAACgC,WAAWC,YAAY,IAAUN,eAAQ;AAC9C,MAAI,CAACO,YAAYC,aAAa,IAAUR,eAAQ;AAChD,MAAI,CAACS,cAAcC,eAAe,IAAUV,eAAQ;AAKpD,MAAIW,cAAoBC,aAAyB,oBAAIpC,IAAG,CAAE;AAC1D,MAAI;IAAEqC;EAAkB,IAAKzE,UAAU,CAAA;AAEvC,MAAI0E,uBAA6BC,kBAC9B7B,QAAkB;AACjB,QAAI2B,oBAAoB;AACtB5B,0BAAoBC,EAAE;IACvB,OAAM;AACLA,SAAE;IACH;EACH,GACA,CAAC2B,kBAAkB,CAAC;AAGtB,MAAIG,WAAiBD,kBACnB,CACEE,UAAqBC,UAMnB;AAAA,QALF;MACEC;MACAC,oBAAoBC;MACpBC,6BAA6BC;IAC9B,IAAAL;AAEDC,oBAAgBtH,QAAST,SAAQuH,YAAYa,QAAQC,OAAOrI,GAAG,CAAC;AAChE6H,aAASS,SAAS7H,QAAQ,CAAC8H,SAASvI,QAAO;AACzC,UAAIuI,QAAQ/D,SAAShC,QAAW;AAC9B+E,oBAAYa,QAAQI,IAAIxI,KAAKuI,QAAQ/D,IAAI;MAC1C;IACH,CAAC;AAED,QAAIiE,8BACF/B,OAAOhE,UAAU,QACjBgE,OAAOhE,OAAOzB,YAAY,QAC1B,OAAOyF,OAAOhE,OAAOzB,SAASyH,wBAAwB;AAIxD,QAAI,CAACP,sBAAsBM,6BAA6B;AACtD,UAAIR,WAAW;AACblC,sBAAc,MAAMY,aAAakB,QAAQ,CAAC;MAC3C,OAAM;AACLH,6BAAqB,MAAMf,aAAakB,QAAQ,CAAC;MAClD;AACD;IACD;AAGD,QAAII,WAAW;AAEblC,oBAAc,MAAK;AAEjB,YAAIoB,YAAY;AACdF,uBAAaA,UAAUb,QAAO;AAC9Be,qBAAWwB,eAAc;QAC1B;AACD3B,qBAAa;UACX/B,iBAAiB;UACjBgD,WAAW;UACXW,iBAAiBT,mBAAmBS;UACpCC,cAAcV,mBAAmBU;QAClC,CAAA;MACH,CAAC;AAGD,UAAIC,IAAIpC,OAAOhE,OAAQzB,SAASyH,oBAAoB,MAAK;AACvD3C,sBAAc,MAAMY,aAAakB,QAAQ,CAAC;MAC5C,CAAC;AAGDiB,QAAEC,SAASC,QAAQ,MAAK;AACtBjD,sBAAc,MAAK;AACjBmB,uBAAa1E,MAAS;AACtB4E,wBAAc5E,MAAS;AACvBsE,0BAAgBtE,MAAS;AACzBwE,uBAAa;YAAE/B,iBAAiB;UAAK,CAAE;QACzC,CAAC;MACH,CAAC;AAEDc,oBAAc,MAAMqB,cAAc0B,CAAC,CAAC;AACpC;IACD;AAGD,QAAI3B,YAAY;AAGdF,mBAAaA,UAAUb,QAAO;AAC9Be,iBAAWwB,eAAc;AACzBrB,sBAAgB;QACdzD,OAAOgE;QACPe,iBAAiBT,mBAAmBS;QACpCC,cAAcV,mBAAmBU;MAClC,CAAA;IACF,OAAM;AAEL/B,sBAAgBe,QAAQ;AACxBb,mBAAa;QACX/B,iBAAiB;QACjBgD,WAAW;QACXW,iBAAiBT,mBAAmBS;QACpCC,cAAcV,mBAAmBU;MAClC,CAAA;IACF;EACH,GACA,CAACnC,OAAOhE,QAAQyE,YAAYF,WAAWM,aAAaG,oBAAoB,CAAC;AAK3EnC,EAAM0D,sBAAgB,MAAMvC,OAAOwC,UAAUtB,QAAQ,GAAG,CAAClB,QAAQkB,QAAQ,CAAC;AAI1ErC,EAAM4D,gBAAU,MAAK;AACnB,QAAIpC,UAAU9B,mBAAmB,CAAC8B,UAAUkB,WAAW;AACrDf,mBAAa,IAAIlB,SAAQ,CAAQ;IAClC;EACH,GAAG,CAACe,SAAS,CAAC;AAKdxB,EAAM4D,gBAAU,MAAK;AACnB,QAAIlC,aAAaJ,gBAAgBH,OAAOhE,QAAQ;AAC9C,UAAImF,WAAWhB;AACf,UAAIuC,gBAAgBnC,UAAUf;AAC9B,UAAIiB,cAAaT,OAAOhE,OAAOzB,SAASyH,oBAAoB,YAAW;AACrEhB,6BAAqB,MAAMf,aAAakB,QAAQ,CAAC;AACjD,cAAMuB;MACR,CAAC;AACDjC,MAAAA,YAAW4B,SAASC,QAAQ,MAAK;AAC/B9B,qBAAa1E,MAAS;AACtB4E,sBAAc5E,MAAS;AACvBsE,wBAAgBtE,MAAS;AACzBwE,qBAAa;UAAE/B,iBAAiB;QAAK,CAAE;MACzC,CAAC;AACDmC,oBAAcD,WAAU;IACzB;EACH,GAAG,CAACO,sBAAsBb,cAAcI,WAAWP,OAAOhE,MAAM,CAAC;AAIjE6C,EAAM4D,gBAAU,MAAK;AACnB,QACElC,aACAJ,gBACAhD,MAAMwF,SAASrJ,QAAQ6G,aAAawC,SAASrJ,KAC7C;AACAiH,gBAAUb,QAAO;IAClB;EACH,GAAG,CAACa,WAAWE,YAAYtD,MAAMwF,UAAUxC,YAAY,CAAC;AAIxDtB,EAAM4D,gBAAU,MAAK;AACnB,QAAI,CAACpC,UAAU9B,mBAAmBoC,cAAc;AAC9CP,sBAAgBO,aAAaxD,KAAK;AAClCmD,mBAAa;QACX/B,iBAAiB;QACjBgD,WAAW;QACXW,iBAAiBvB,aAAauB;QAC9BC,cAAcxB,aAAawB;MAC5B,CAAA;AACDvB,sBAAgB9E,MAAS;IAC1B;KACA,CAACuE,UAAU9B,iBAAiBoC,YAAY,CAAC;AAE5C9B,EAAM4D,gBAAU,MAAK;AACnB3H,WAAAC,QACEgF,mBAAmB,QAAQ,CAACC,OAAO1D,OAAOsG,qBAC1C,8HACoE,IACrE;KAGA,CAAA,CAAE;AAEL,MAAIC,YAAkBC,cAAQ,MAAgB;AAC5C,WAAO;MACLC,YAAY/C,OAAO+C;MACnBC,gBAAgBhD,OAAOgD;MACvBC,IAAKC,OAAMlD,OAAOmD,SAASD,CAAC;MAC5BE,MAAMA,CAACC,IAAIlG,QAAOf,SAChB4D,OAAOmD,SAASE,IAAI;QAClBlG,OAAAA;QACAmG,oBAAoBlH,QAAAA,OAAAA,SAAAA,KAAMkH;OAC3B;MACHC,SAASA,CAACF,IAAIlG,QAAOf,SACnB4D,OAAOmD,SAASE,IAAI;QAClBE,SAAS;QACTpG,OAAAA;QACAmG,oBAAoBlH,QAAAA,OAAAA,SAAAA,KAAMkH;OAC3B;;EAEP,GAAG,CAACtD,MAAM,CAAC;AAEX,MAAI/E,WAAW+E,OAAO/E,YAAY;AAElC,MAAIuI,oBAA0BV,cAC5B,OAAO;IACL9C;IACA6C;IACAY,QAAQ;IACRxI;MAEF,CAAC+E,QAAQ6C,WAAW5H,QAAQ,CAAC;AAG/B,MAAIyI,eAAqBZ,cACvB,OAAO;IACLa,sBAAsB3D,OAAO1D,OAAOqH;MAEtC,CAAC3D,OAAO1D,OAAOqH,oBAAoB,CAAC;AAStC,SACEnJ,oBAAAoJ,gBAAA,MACEpJ,oBAACqJ,kBAAkBC,UAAS;IAAAvK,OAAOiK;KACjChJ,oBAACuJ,uBAAuBD,UAAS;IAAAvK,OAAO4D;KACrC3C,oBAAAiE,gBAAgBqF,UAAQ;IAACvK,OAAOsH,YAAYa;KAC3ClH,oBAAC6D,sBAAsByF,UAAS;IAAAvK,OAAO8G;EAAS,GAC9C7F,oBAACwJ,QAAM;IACL/I;IACA0H,UAAUxF,MAAMwF;IAChBsB,gBAAgB9G,MAAM+G;IACtBrB;IACAvG,QAAQoH;EAEP,GAAAvG,MAAMgH,eAAenE,OAAO1D,OAAOsG,sBAClCpI,oBAAC4J,oBACC;IAAAjI,QAAQ6D,OAAO7D;IACfG,QAAQ0D,OAAO1D;IACfa;GAAY,IAGd4C,eACD,CACM,CACsB,CACR,CACK,GAEnC,IAAI;AAGX;AAGA,IAAMqE,qBAA2B/K,WAAKgL,UAAU;AAEhD,SAASA,WAAUC,OAQlB;AAAA,MARmB;IAClBnI;IACAG;IACAa;EAKD,IAAAmH;AACC,SAAOC,cAAcpI,QAAQL,QAAWqB,OAAOb,MAAM;AACvD;AAYM,SAAUkI,cAAaC,OAKR;AAAA,MALS;IAC5BxJ;IACAyJ;IACApI;IACAN,QAAAA;EACmB,IAAAyI;AACnB,MAAIE,aAAmB7D,aAAM;AAC7B,MAAI6D,WAAWjD,WAAW,MAAM;AAC9BiD,eAAWjD,UAAUhF,qBAAqB;MAAEV,QAAAA;MAAQ4I,UAAU;IAAI,CAAE;EACrE;AAED,MAAInI,UAAUkI,WAAWjD;AACzB,MAAI,CAACvE,OAAO8C,YAAY,IAAUC,eAAS;IACzC/E,QAAQsB,QAAQtB;IAChBwH,UAAUlG,QAAQkG;EACnB,CAAA;AACD,MAAI;IAAE5B;EAAkB,IAAKzE,UAAU,CAAA;AACvC,MAAI4E,WAAiBD,kBAClBE,cAA4D;AAC3DJ,0BAAsBnC,sBAClBA,oBAAoB,MAAMqB,aAAakB,QAAQ,CAAC,IAChDlB,aAAakB,QAAQ;EAC3B,GACA,CAAClB,cAAcc,kBAAkB,CAAC;AAGpClC,EAAM0D,sBAAgB,MAAM9F,QAAQoI,OAAO3D,QAAQ,GAAG,CAACzE,SAASyE,QAAQ,CAAC;AAEzE,SACE1G,oBAACwJ,QAAM;IACL/I;IACAyJ;IACA/B,UAAUxF,MAAMwF;IAChBsB,gBAAgB9G,MAAMhC;IACtB0H,WAAWpG;IACXH;EAAc,CAAA;AAGpB;AAaM,SAAUwI,WAAUC,OAKR;AAAA,MALS;IACzB9J;IACAyJ;IACApI;IACAN,QAAAA;EACgB,IAAA+I;AAChB,MAAIJ,aAAmB7D,aAAM;AAC7B,MAAI6D,WAAWjD,WAAW,MAAM;AAC9BiD,eAAWjD,UAAUzE,kBAAkB;MAAEjB,QAAAA;MAAQ4I,UAAU;IAAI,CAAE;EAClE;AAED,MAAInI,UAAUkI,WAAWjD;AACzB,MAAI,CAACvE,OAAO8C,YAAY,IAAUC,eAAS;IACzC/E,QAAQsB,QAAQtB;IAChBwH,UAAUlG,QAAQkG;EACnB,CAAA;AACD,MAAI;IAAE5B;EAAkB,IAAKzE,UAAU,CAAA;AACvC,MAAI4E,WAAiBD,kBAClBE,cAA4D;AAC3DJ,0BAAsBnC,sBAClBA,oBAAoB,MAAMqB,aAAakB,QAAQ,CAAC,IAChDlB,aAAakB,QAAQ;EAC3B,GACA,CAAClB,cAAcc,kBAAkB,CAAC;AAGpClC,EAAM0D,sBAAgB,MAAM9F,QAAQoI,OAAO3D,QAAQ,GAAG,CAACzE,SAASyE,QAAQ,CAAC;AAEzE,SACE1G,oBAACwJ,QAAM;IACL/I;IACAyJ;IACA/B,UAAUxF,MAAMwF;IAChBsB,gBAAgB9G,MAAMhC;IACtB0H,WAAWpG;IACXH;EAAc,CAAA;AAGpB;AAeA,SAAS0I,cAAaC,OAKD;AAAA,MALE;IACrBhK;IACAyJ;IACApI;IACAG;EACmB,IAAAwI;AACnB,MAAI,CAAC9H,OAAO8C,YAAY,IAAUC,eAAS;IACzC/E,QAAQsB,QAAQtB;IAChBwH,UAAUlG,QAAQkG;EACnB,CAAA;AACD,MAAI;IAAE5B;EAAkB,IAAKzE,UAAU,CAAA;AACvC,MAAI4E,WAAiBD,kBAClBE,cAA4D;AAC3DJ,0BAAsBnC,sBAClBA,oBAAoB,MAAMqB,aAAakB,QAAQ,CAAC,IAChDlB,aAAakB,QAAQ;EAC3B,GACA,CAAClB,cAAcc,kBAAkB,CAAC;AAGpClC,EAAM0D,sBAAgB,MAAM9F,QAAQoI,OAAO3D,QAAQ,GAAG,CAACzE,SAASyE,QAAQ,CAAC;AAEzE,SACE1G,oBAACwJ,QAAM;IACL/I;IACAyJ;IACA/B,UAAUxF,MAAMwF;IAChBsB,gBAAgB9G,MAAMhC;IACtB0H,WAAWpG;IACXH;EAAc,CAAA;AAGpB;AAEA,IAAAxB,MAAa;AACXkK,gBAAcxG,cAAc;AAC7B;AAeD,IAAM0G,YACJ,OAAOlJ,WAAW,eAClB,OAAOA,OAAOzB,aAAa,eAC3B,OAAOyB,OAAOzB,SAASC,kBAAkB;AAE3C,IAAM2K,qBAAqB;AAKdC,IAAAA,OAAaC,iBACxB,SAASC,YAAWC,OAalBC,KAAG;AAAA,MAZH;IACEC;IACAC;IACAC;IACApC,SAAAA;IACApG;IACAxE;IACA0K;IACAC;IACAsC;EACO,IACRL,OADIM,OAAIC,8BAAAP,OAAAQ,SAAA;AAIT,MAAI;IAAE9K;EAAQ,IAAW+K,iBAAWC,iBAAiB;AAGrD,MAAIC;AACJ,MAAIC,aAAa;AAEjB,MAAI,OAAO9C,OAAO,YAAY8B,mBAAmBiB,KAAK/C,EAAE,GAAG;AAEzD6C,mBAAe7C;AAGf,QAAI6B,WAAW;AACb,UAAI;AACF,YAAImB,aAAa,IAAIC,IAAItK,OAAO2G,SAAS4D,IAAI;AAC7C,YAAIC,YAAYnD,GAAGoD,WAAW,IAAI,IAC9B,IAAIH,IAAID,WAAWK,WAAWrD,EAAE,IAChC,IAAIiD,IAAIjD,EAAE;AACd,YAAIsD,OAAOnL,cAAcgL,UAAUI,UAAU3L,QAAQ;AAErD,YAAIuL,UAAUK,WAAWR,WAAWQ,UAAUF,QAAQ,MAAM;AAE1DtD,eAAKsD,OAAOH,UAAUM,SAASN,UAAUO;QAC1C,OAAM;AACLZ,uBAAa;QACd;eACM1L,GAAG;AAEVK,eAAAC,QACE,OACA,eAAasI,KAAE,wGACsC,IACtD;MACF;IACF;EACF;AAGD,MAAIkD,OAAOS,QAAQ3D,IAAI;IAAEqC;EAAU,CAAA;AAEnC,MAAIuB,kBAAkBC,oBAAoB7D,IAAI;IAC5CE,SAAAA;IACApG;IACAxE;IACA2K;IACAoC;IACAE;EACD,CAAA;AACD,WAASuB,YACP9O,OAAsD;AAEtD,QAAIoN,QAASA,SAAQpN,KAAK;AAC1B,QAAI,CAACA,MAAM+O,kBAAkB;AAC3BH,sBAAgB5O,KAAK;IACtB;EACH;AAEA;;IAEEmC,oBAAA,KAAA+B,SAAA,CAAA,GACMsJ,MAAI;MACRU,MAAML,gBAAgBK;MACtBd,SAASU,cAAcR,iBAAiBF,UAAU0B;MAClD3B;MACA7M;KAAc,CAAA;;AAGpB,CAAC;AAGH,IAAAmC,MAAa;AACXsK,OAAK5G,cAAc;AACpB;AAsBY6I,IAAAA,UAAgBhC,iBAC3B,SAASiC,eAAcC,OAYrB/B,KAAG;AAAA,MAXH;IACE,gBAAgBgC,kBAAkB;IAClCC,gBAAgB;IAChBC,WAAWC,gBAAgB;IAC3BC,MAAM;IACNC,OAAOC;IACPzE;IACAuC;IACAlB;EAED,IAAA6C,OADI1B,OAAIC,8BAAAyB,OAAAQ,UAAA;AAIT,MAAIpB,OAAOqB,gBAAgB3E,IAAI;IAAEqC,UAAUG,KAAKH;EAAQ,CAAE;AAC1D,MAAI/C,WAAWsF,YAAW;AAC1B,MAAIC,cAAoBlC,iBAAWjC,sBAAsB;AACzD,MAAI;IAAElB;IAAW5H;EAAU,IAAS+K,iBAAWC,iBAAiB;AAChE,MAAI1H,kBACF2J,eAAe;;EAGfC,uBAAuBxB,IAAI,KAC3Bf,4BAA4B;AAE9B,MAAIwC,aAAavF,UAAUG,iBACvBH,UAAUG,eAAe2D,IAAI,EAAEC,WAC/BD,KAAKC;AACT,MAAIyB,mBAAmB1F,SAASiE;AAChC,MAAI0B,uBACFJ,eAAeA,YAAYK,cAAcL,YAAYK,WAAW5F,WAC5DuF,YAAYK,WAAW5F,SAASiE,WAChC;AAEN,MAAI,CAACa,eAAe;AAClBY,uBAAmBA,iBAAiBpQ,YAAW;AAC/CqQ,2BAAuBA,uBACnBA,qBAAqBrQ,YAAW,IAChC;AACJmQ,iBAAaA,WAAWnQ,YAAW;EACpC;AAED,MAAIqQ,wBAAwBrN,UAAU;AACpCqN,2BACE9M,cAAc8M,sBAAsBrN,QAAQ,KAAKqN;EACpD;AAOD,QAAME,mBACJJ,eAAe,OAAOA,WAAWK,SAAS,GAAG,IACzCL,WAAWM,SAAS,IACpBN,WAAWM;AACjB,MAAIC,WACFN,qBAAqBD,cACpB,CAACR,OACAS,iBAAiB5B,WAAW2B,UAAU,KACtCC,iBAAiBO,OAAOJ,gBAAgB,MAAM;AAElD,MAAIK,YACFP,wBAAwB,SACvBA,yBAAyBF,cACvB,CAACR,OACAU,qBAAqB7B,WAAW2B,UAAU,KAC1CE,qBAAqBM,OAAOR,WAAWM,MAAM,MAAM;AAEzD,MAAII,cAAc;IAChBH;IACAE;IACAtK;;AAGF,MAAIwK,cAAcJ,WAAWnB,kBAAkB1L;AAE/C,MAAI4L;AACJ,MAAI,OAAOC,kBAAkB,YAAY;AACvCD,gBAAYC,cAAcmB,WAAW;EACtC,OAAM;AAMLpB,gBAAY,CACVC,eACAgB,WAAW,WAAW,MACtBE,YAAY,YAAY,MACxBtK,kBAAkB,kBAAkB,IAAI,EAEvCyK,OAAOC,OAAO,EACdC,KAAK,GAAG;EACZ;AAED,MAAIrB,QACF,OAAOC,cAAc,aAAaA,UAAUgB,WAAW,IAAIhB;AAE7D,SACEjJ,oBAACuG,MAAI7I,SAAA,CAAA,GACCsJ,MAAI;IACM,gBAAAkD;IACdrB;IACAlC;IACAqC;IACAxE;IACAuC;GAEC,GAAA,OAAOlB,aAAa,aAAaA,SAASoE,WAAW,IAAIpE,QAAQ;AAGxE,CAAC;AAGH,IAAA5J,MAAa;AACXuM,UAAQ7I,cAAc;AACvB;AAsGM,IAAM2K,OAAa9D,iBACxB,CAAA+D,OAeEC,iBACE;AAAA,MAfF;IACEC;IACAnG;IACAwC;IACApC,SAAAA;IACApG;IACAjC,SAASvD;IACTwD;IACAoO;IACA7D;IACApC;IACAsC;MAEDwD,OADII,QAAK1D,8BAAAsD,OAAAK,UAAA;AAIV,MAAIC,SAASC,UAAS;AACtB,MAAIC,aAAaC,cAAc1O,QAAQ;IAAEuK;EAAU,CAAA;AACnD,MAAIoE,aACF5O,OAAOjD,YAAW,MAAO,QAAQ,QAAQ;AAE3C,MAAI8R,gBAA0D1R,WAAS;AACrEkR,gBAAYA,SAASlR,KAAK;AAC1B,QAAIA,MAAM+O,iBAAkB;AAC5B/O,UAAM2R,eAAc;AAEpB,QAAIC,YAAa5R,MAAqC6R,YACnDD;AAEH,QAAIE,gBACDF,aAAAA,OAAAA,SAAAA,UAAW1O,aAAa,YAAY,MACrCL;AAEFwO,WAAOO,aAAa5R,MAAM+R,eAAe;MACvCd;MACApO,QAAQiP;MACRhH;MACAI,SAAAA;MACApG;MACAuI;MACApC;MACAsC;IACD,CAAA;;AAGH,SACEpL,oBAAA,QAAA+B,SAAA;IACEiJ,KAAK6D;IACLnO,QAAQ4O;IACR3O,QAAQyO;IACRL,UAAU5D,iBAAiB4D,WAAWQ;KAClCP,KAAK,CAAA;AAGf,CAAC;AAGH,IAAA1O,MAAa;AACXqO,OAAK3K,cAAc;AACpB;SAWe6L,kBAAiBC,QAGR;AAAA,MAHS;IAChCC;IACAC;EACuB,IAAAF;AACvBG,uBAAqB;IAAEF;IAAQC;EAAU,CAAE;AAC3C,SAAO;AACT;AAEA,IAAA1P,MAAa;AACXuP,oBAAkB7L,cAAc;AACjC;AAOD,IAAKkM;CAAL,SAAKA,iBAAc;AACjBA,EAAAA,gBAAA,sBAAA,IAAA;AACAA,EAAAA,gBAAA,WAAA,IAAA;AACAA,EAAAA,gBAAA,kBAAA,IAAA;AACAA,EAAAA,gBAAA,YAAA,IAAA;AACAA,EAAAA,gBAAA,wBAAA,IAAA;AACF,GANKA,mBAAAA,iBAMJ,CAAA,EAAA;AAED,IAAKC;CAAL,SAAKA,sBAAmB;AACtBA,EAAAA,qBAAA,YAAA,IAAA;AACAA,EAAAA,qBAAA,aAAA,IAAA;AACAA,EAAAA,qBAAA,sBAAA,IAAA;AACF,GAJKA,wBAAAA,sBAIJ,CAAA,EAAA;AAID,SAASC,0BACPC,UAA8C;AAE9C,SAAUA,WAAQ;AACpB;AAEA,SAASC,qBAAqBD,UAAwB;AACpD,MAAIE,MAAY/E,iBAAWnC,iBAAiB;AAC5C,GAAUkH,MAAGjQ,OAAbkQ,UAAS,OAAMJ,0BAA0BC,QAAQ,CAAC,IAAlDG,UAAS,KAAA,IAAA;AACT,SAAOD;AACT;AAEA,SAASE,mBAAmBJ,UAA6B;AACvD,MAAI1N,QAAc6I,iBAAWjC,sBAAsB;AACnD,GAAU5G,QAAKrC,OAAfkQ,UAAS,OAAQJ,0BAA0BC,QAAQ,CAAC,IAApDG,UAAS,KAAA,IAAA;AACT,SAAO7N;AACT;AASM,SAAU+J,oBACd7D,IAAM6H,OAeA;AAAA,MAdN;IACEvS;IACA4K,SAAS4H;IACThO;IACAmG;IACAoC;IACAE;yBAQE,CAAA,IAAEsF;AAEN,MAAI/H,WAAWiI,YAAW;AAC1B,MAAIzI,WAAWsF,YAAW;AAC1B,MAAItB,OAAOqB,gBAAgB3E,IAAI;IAAEqC;EAAU,CAAA;AAE3C,SAAazE,kBACV5I,WAA0C;AACzC,QAAIK,uBAAuBL,OAAOM,MAAM,GAAG;AACzCN,YAAM2R,eAAc;AAIpB,UAAIzG,WACF4H,gBAAgBrP,SACZqP,cACAE,WAAW1I,QAAQ,MAAM0I,WAAW1E,IAAI;AAE9CxD,eAASE,IAAI;QACXE,SAAAA;QACApG;QACAmG;QACAoC;QACAE;MACD,CAAA;IACF;KAEH,CACEjD,UACAQ,UACAwD,MACAwE,aACAhO,OACAxE,QACA0K,IACAC,oBACAoC,UACAE,uBAAuB,CACxB;AAEL;AAMM,SAAU0F,gBACdC,aAAiC;AAEjCzQ,SAAAC,QACE,OAAOhC,oBAAoB,aAC3B,yOAG+C,IAChD;AAED,MAAIyS,yBAA+B1K,aAAOjI,mBAAmB0S,WAAW,CAAC;AACzE,MAAIE,wBAA8B3K,aAAO,KAAK;AAE9C,MAAI6B,WAAWsF,YAAW;AAC1B,MAAInO,eAAqBgJ,cACvB;;;;IAIEnJ,2BACEgJ,SAASmE,QACT2E,sBAAsB/J,UAAU,OAAO8J,uBAAuB9J,OAAO;KAEzE,CAACiB,SAASmE,MAAM,CAAC;AAGnB,MAAI3D,WAAWiI,YAAW;AAC1B,MAAIM,kBAAwBzK,kBAC1B,CAAC0K,UAAUC,oBAAmB;AAC5B,UAAMC,kBAAkBhT,mBACtB,OAAO8S,aAAa,aAAaA,SAAS7R,YAAY,IAAI6R,QAAQ;AAEpEF,0BAAsB/J,UAAU;AAChCyB,aAAS,MAAM0I,iBAAiBD,eAAe;EACjD,GACA,CAACzI,UAAUrJ,YAAY,CAAC;AAG1B,SAAO,CAACA,cAAc4R,eAAe;AACvC;AA2CA,SAASI,+BAA4B;AACnC,MAAI,OAAOvR,aAAa,aAAa;AACnC,UAAM,IAAIoB,MACR,+GACgE;EAEnE;AACH;AAEA,IAAIoQ,YAAY;AAChB,IAAIC,qBAAqBA,MAAA,OAAWC,OAAO,EAAEF,SAAS,IAAK;SAM3CpC,YAAS;AACvB,MAAI;IAAE3J;EAAM,IAAK8K,qBAAqBJ,eAAewB,SAAS;AAC9D,MAAI;IAAEjR;EAAQ,IAAW+K,iBAAWC,iBAAiB;AACrD,MAAIkG,iBAAiBC,WAAU;AAE/B,SAAanL,kBACX,SAACtI,QAAQ0T,SAAgB;AAAA,QAAhBA,YAAO,QAAA;AAAPA,gBAAU,CAAA;IAAE;AACnBP,iCAA4B;AAE5B,QAAI;MAAE3Q;MAAQD;MAAQL;MAASO;MAAUC;IAAI,IAAKL,sBAChDrC,QACAsC,QAAQ;AAGV,QAAIoR,QAAQlJ,aAAa,OAAO;AAC9B,UAAI7J,MAAM+S,QAAQ/C,cAAc0C,mBAAkB;AAClDhM,aAAOsM,MAAMhT,KAAK6S,gBAAgBE,QAAQlR,UAAUA,QAAQ;QAC1DmI,oBAAoB+I,QAAQ/I;QAC5BlI;QACAC;QACAyO,YAAYuC,QAAQnR,UAAWA;QAC/BqR,aAAaF,QAAQxR,WAAYA;QACjCyG,oBAAoB+K,QAAQ/K;MAC7B,CAAA;IACF,OAAM;AACLtB,aAAOmD,SAASkJ,QAAQlR,UAAUA,QAAQ;QACxCmI,oBAAoB+I,QAAQ/I;QAC5BlI;QACAC;QACAyO,YAAYuC,QAAQnR,UAAWA;QAC/BqR,aAAaF,QAAQxR,WAAYA;QACjC0I,SAAS8I,QAAQ9I;QACjBpG,OAAOkP,QAAQlP;QACfqP,aAAaL;QACb7K,oBAAoB+K,QAAQ/K;QAC5BsE,yBAAyByG,QAAQzG;MAClC,CAAA;IACF;KAEH,CAAC5F,QAAQ/E,UAAUkR,cAAc,CAAC;AAEtC;AAIM,SAAUtC,cACd1O,QAAesR,QACsC;AAAA,MAArD;IAAE/G;0BAAiD,CAAA,IAAE+G;AAErD,MAAI;IAAExR;EAAQ,IAAW+K,iBAAWC,iBAAiB;AACrD,MAAIyG,eAAqB1G,iBAAW2G,YAAY;AAChD,GAAUD,eAAY5R,OAAtBkQ,UAAS,OAAe,kDAAkD,IAA1EA,UAAS,KAAA,IAAA;AAET,MAAI,CAAC4B,KAAK,IAAIF,aAAaG,QAAQC,MAAM,EAAE;AAG3C,MAAInG,OAAIpK,SAAQyL,CAAAA,GAAAA,gBAAgB7M,SAASA,SAAS,KAAK;IAAEuK;EAAQ,CAAE,CAAC;AAKpE,MAAI/C,WAAWsF,YAAW;AAC1B,MAAI9M,UAAU,MAAM;AAGlBwL,SAAKG,SAASnE,SAASmE;AAKvB,QAAIiG,SAAS,IAAIhU,gBAAgB4N,KAAKG,MAAM;AAC5C,QAAIiG,OAAO9S,IAAI,OAAO,KAAK8S,OAAOC,IAAI,OAAO,MAAM,IAAI;AACrDD,aAAOpL,OAAO,OAAO;AACrBgF,WAAKG,SAASiG,OAAOE,SAAQ,IAAE,MAAOF,OAAOE,SAAQ,IAAO;IAC7D;EACF;AAED,OAAK,CAAC9R,UAAUA,WAAW,QAAQyR,MAAMM,MAAMC,OAAO;AACpDxG,SAAKG,SAASH,KAAKG,SACfH,KAAKG,OAAOvD,QAAQ,OAAO,SAAS,IACpC;EACL;AAMD,MAAItI,aAAa,KAAK;AACpB0L,SAAKC,WACHD,KAAKC,aAAa,MAAM3L,WAAWmS,UAAU,CAACnS,UAAU0L,KAAKC,QAAQ,CAAC;EACzE;AAED,SAAOyE,WAAW1E,IAAI;AACxB;SAgBgB0G,WAAUC,QAEF;AAAA,MAAAC;AAAA,MAFgB;IACtCjU;0BACoB,CAAA,IAAEgU;AACtB,MAAI;IAAEtN;EAAM,IAAK8K,qBAAqBJ,eAAe8C,UAAU;AAC/D,MAAIrQ,QAAQ8N,mBAAmBN,oBAAoB6C,UAAU;AAC7D,MAAI3M,cAAoBmF,iBAAWvH,eAAe;AAClD,MAAIyO,QAAclH,iBAAW2G,YAAY;AACzC,MAAIc,WAAOF,iBAAGL,MAAML,QAAQK,MAAML,QAAQnE,SAAS,CAAC,MAAC,OAAA,SAAvC6E,eAAyCL,MAAMQ;AAE7D,GAAU7M,cAAW/F,OAArBkQ,UAAS,OAAA,kDAAA,IAATA,UAAS,KAAA,IAAA;AACT,GAAUkC,QAAKpS,OAAfkQ,UAAS,OAAA,+CAAA,IAATA,UAAS,KAAA,IAAA;AACT,IACEyC,WAAW,QAAI3S,OADjBkQ,UAAS,OAAA,kEAAA,IAATA,UAAS,KAAA,IAAA;AAQT,MAAI2C,aAAazO,YAAYA,UAAS,IAAK;AAC3C,MAAI,CAACoK,YAAYsE,aAAa,IAAU1N,eAAiB5G,OAAOqU,UAAU;AAC1E,MAAIrU,OAAOA,QAAQgQ,YAAY;AAC7BsE,kBAActU,GAAG;EAClB,WAAU,CAACgQ,YAAY;AAEtBsE,kBAAc5B,mBAAkB,CAAE;EACnC;AAGDnN,EAAM4D,gBAAU,MAAK;AACnBzC,WAAO6N,WAAWvE,UAAU;AAC5B,WAAO,MAAK;AAIVtJ,aAAO8N,cAAcxE,UAAU;;EAEnC,GAAG,CAACtJ,QAAQsJ,UAAU,CAAC;AAGvB,MAAIyE,OAAa9M,kBACf,CAACsF,MAAcnK,SAA2C;AACxD,KAAUqR,UAAO3S,OAAjBkQ,UAAS,OAAU,yCAAyC,IAA5DA,UAAS,KAAA,IAAA;AACThL,WAAOsM,MAAMhD,YAAYmE,SAASlH,MAAMnK,IAAI;KAE9C,CAACkN,YAAYmE,SAASzN,MAAM,CAAC;AAG/B,MAAIgO,aAAarE,UAAS;AAC1B,MAAID,SAAezI,kBACjB,CAACtI,QAAQyD,SAAQ;AACf4R,eAAWrV,QAAM4D,SAAA,CAAA,GACZH,MAAI;MACP+G,UAAU;MACVmG;IAAU,CAAA,CACX;EACH,GACA,CAACA,YAAY0E,UAAU,CAAC;AAG1B,MAAIC,cAAoBnL,cAAQ,MAAK;AACnC,QAAImL,eAAoB5I,iBACtB,CAACmE,OAAOhE,QAAO;AACb,aACGhL,oBAAA2O,MAAI5M,SAAA,CAAA,GAAKiN,OAAK;QAAErG,UAAU;QAAOmG;QAAwB9D;MAAQ,CAAA,CAAA;IAEtE,CAAC;AAEH,QAAA1K,MAAa;AACXmT,MAAAA,aAAYzP,cAAc;IAC3B;AACD,WAAOyP;EACT,GAAG,CAAC3E,UAAU,CAAC;AAGf,MAAIzH,UAAU1E,MAAMyE,SAASoL,IAAI1D,UAAU,KAAK4E;AAChD,MAAIpQ,OAAO+C,YAAYmM,IAAI1D,UAAU;AACrC,MAAI6E,wBAA8BrL,cAChC,MAAAvG,SAAA;IACE4M,MAAM8E;IACNvE;IACAqE;EAAI,GACDlM,SAAO;IACV/D;EAAI,CAAA,GAEN,CAACmQ,aAAavE,QAAQqE,MAAMlM,SAAS/D,IAAI,CAAC;AAG5C,SAAOqQ;AACT;SAMgBC,cAAW;AACzB,MAAIjR,QAAQ8N,mBAAmBN,oBAAoB0D,WAAW;AAC9D,SAAOrV,MAAMsV,KAAKnR,MAAMyE,SAASrE,QAAO,CAAE,EAAE9D,IAAI8U,YAAA;AAAA,QAAC,CAACjV,KAAKuI,OAAO,IAAC0M;AAAA,WAAAhS,SAAA,CAAA,GAC1DsF,SAAO;MACVvI;IAAG,CAAA;EAAA,CACH;AACJ;AAEA,IAAMkV,iCAAiC;AACvC,IAAIC,uBAA+C,CAAA;AAKnD,SAAShE,qBAAoBiE,QAMvB;AAAA,MANwB;IAC5BnE;IACAC;0BAIE,CAAA,IAAEkE;AACJ,MAAI;IAAE1O;EAAM,IAAK8K,qBAAqBJ,eAAeiE,oBAAoB;AACzE,MAAI;IAAEC;IAAuBtL;EAAoB,IAAG2H,mBAClDN,oBAAoBgE,oBAAoB;AAE1C,MAAI;IAAE1T;EAAQ,IAAW+K,iBAAWC,iBAAiB;AACrD,MAAItD,WAAWsF,YAAW;AAC1B,MAAI4E,UAAUgC,WAAU;AACxB,MAAItG,aAAauG,cAAa;AAG9BjQ,EAAM4D,gBAAU,MAAK;AACnBzG,WAAOS,QAAQsS,oBAAoB;AACnC,WAAO,MAAK;AACV/S,aAAOS,QAAQsS,oBAAoB;;KAEpC,CAAA,CAAE;AAGLC,cACQ/N,kBAAY,MAAK;AACrB,QAAIsH,WAAWpL,UAAU,QAAQ;AAC/B,UAAI7D,OAAOiR,SAASA,OAAO5H,UAAUkK,OAAO,IAAI,SAASlK,SAASrJ;AAClEmV,2BAAqBnV,GAAG,IAAI0C,OAAOiT;IACpC;AACD,QAAI;AACFC,qBAAeC,QACb3E,cAAcgE,gCACdY,KAAKC,UAAUZ,oBAAoB,CAAC;aAE/BvQ,OAAO;AACdpD,aAAAC,QACE,OAAK,sGAC+FmD,QAAK,IAAI,IAC9G;IACF;AACDlC,WAAOS,QAAQsS,oBAAoB;EACrC,GAAG,CAACvE,YAAYD,QAAQhC,WAAWpL,OAAOwF,UAAUkK,OAAO,CAAC,CAAC;AAI/D,MAAI,OAAOtS,aAAa,aAAa;AAEnCsE,IAAM0D,sBAAgB,MAAK;AACzB,UAAI;AACF,YAAI+M,mBAAmBJ,eAAeK,QACpC/E,cAAcgE,8BAA8B;AAE9C,YAAIc,kBAAkB;AACpBb,iCAAuBW,KAAKI,MAAMF,gBAAgB;QACnD;eACM7U,GAAG;MACV;IAEJ,GAAG,CAAC+P,UAAU,CAAC;AAIf3L,IAAM0D,sBAAgB,MAAK;AACzB,UAAIkN,wBACFlF,UAAUtP,aAAa,MACnB,CAAC0H,WAAUkK,aACTtC;;QACEhO,SAAA,CAAA,GAEKoG,WAAQ;UACXiE,UACEpL,cAAcmH,UAASiE,UAAU3L,QAAQ,KACzC0H,UAASiE;SAEbiG;QAAAA;MAAO,IAEXtC;AACN,UAAImF,2BAA2B1P,UAAAA,OAAAA,SAAAA,OAAQ2P,wBACrClB,sBACA,MAAMzS,OAAOiT,SACbQ,qBAAqB;AAEvB,aAAO,MAAMC,4BAA4BA,yBAAwB;OAChE,CAAC1P,QAAQ/E,UAAUsP,MAAM,CAAC;AAI7B1L,IAAM0D,sBAAgB,MAAK;AAEzB,UAAIqM,0BAA0B,OAAO;AACnC;MACD;AAGD,UAAI,OAAOA,0BAA0B,UAAU;AAC7C5S,eAAO4T,SAAS,GAAGhB,qBAAqB;AACxC;MACD;AAGD,UAAIjM,SAASoE,MAAM;AACjB,YAAI8I,KAAKtV,SAASuV,eAChBC,mBAAmBpN,SAASoE,KAAK+F,MAAM,CAAC,CAAC,CAAC;AAE5C,YAAI+C,IAAI;AACNA,aAAGG,eAAc;AACjB;QACD;MACF;AAGD,UAAI1M,uBAAuB,MAAM;AAC/B;MACD;AAGDtH,aAAO4T,SAAS,GAAG,CAAC;OACnB,CAACjN,UAAUiM,uBAAuBtL,kBAAkB,CAAC;EACzD;AACH;AAYgB,SAAA2M,gBACdC,UACA7D,SAA+B;AAE/B,MAAI;IAAE8D;EAAO,IAAK9D,WAAW,CAAA;AAC7BxN,EAAM4D,gBAAU,MAAK;AACnB,QAAIrG,OAAO+T,WAAW,OAAO;MAAEA;IAAS,IAAGrU;AAC3CE,WAAOoU,iBAAiB,gBAAgBF,UAAU9T,IAAI;AACtD,WAAO,MAAK;AACVJ,aAAOqU,oBAAoB,gBAAgBH,UAAU9T,IAAI;;EAE7D,GAAG,CAAC8T,UAAUC,OAAO,CAAC;AACxB;AAUA,SAASnB,YACPkB,UACA7D,SAA+B;AAE/B,MAAI;IAAE8D;EAAO,IAAK9D,WAAW,CAAA;AAC7BxN,EAAM4D,gBAAU,MAAK;AACnB,QAAIrG,OAAO+T,WAAW,OAAO;MAAEA;IAAS,IAAGrU;AAC3CE,WAAOoU,iBAAiB,YAAYF,UAAU9T,IAAI;AAClD,WAAO,MAAK;AACVJ,aAAOqU,oBAAoB,YAAYH,UAAU9T,IAAI;;EAEzD,GAAG,CAAC8T,UAAUC,OAAO,CAAC;AACxB;AAUA,SAASG,UAASC,QAMjB;AAAA,MANkB;IACjBC;IACArS;EAID,IAAAoS;AACC,MAAIE,UAAUC,WAAWF,IAAI;AAE7B3R,EAAM4D,gBAAU,MAAK;AACnB,QAAIgO,QAAQtT,UAAU,WAAW;AAC/B,UAAIwT,UAAU3U,OAAO4U,QAAQzS,OAAO;AACpC,UAAIwS,SAAS;AAIXE,mBAAWJ,QAAQE,SAAS,CAAC;MAC9B,OAAM;AACLF,gBAAQK,MAAK;MACd;IACF;EACH,GAAG,CAACL,SAAStS,OAAO,CAAC;AAErBU,EAAM4D,gBAAU,MAAK;AACnB,QAAIgO,QAAQtT,UAAU,aAAa,CAACqT,MAAM;AACxCC,cAAQK,MAAK;IACd;EACH,GAAG,CAACL,SAASD,IAAI,CAAC;AACpB;AAYA,SAASrI,uBACP9E,IACAjH,MAA6C;AAAA,MAA7CA,SAAAA,QAAAA;AAAAA,WAA2C,CAAA;EAAE;AAE7C,MAAIiE,YAAkB2F,iBAAW3H,qBAAqB;AAEtD,IACEgC,aAAa,QAAIvF,OADnBkQ,UAEE,OAAA,iKACqE,IAHvEA,UAAS,KAAA,IAAA;AAMT,MAAI;IAAE/P;EAAQ,IAAK6P,qBACjBJ,eAAevC,sBAAsB;AAEvC,MAAIxB,OAAOqB,gBAAgB3E,IAAI;IAAEqC,UAAUtJ,KAAKsJ;EAAQ,CAAE;AAC1D,MAAI,CAACrF,UAAU9B,iBAAiB;AAC9B,WAAO;EACR;AAED,MAAIwS,cACFvV,cAAc6E,UAAU6B,gBAAgB0E,UAAU3L,QAAQ,KAC1DoF,UAAU6B,gBAAgB0E;AAC5B,MAAIoK,WACFxV,cAAc6E,UAAU8B,aAAayE,UAAU3L,QAAQ,KACvDoF,UAAU8B,aAAayE;AAezB,SACEqK,UAAUtK,KAAKC,UAAUoK,QAAQ,KAAK,QACtCC,UAAUtK,KAAKC,UAAUmK,WAAW,KAAK;AAE7C;", "names": ["defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "for<PERSON>ach", "_", "has", "getAll", "append", "_formDataSupportsSubmitter", "isFormDataSubmitterSupported", "FormData", "document", "createElement", "e", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "process", "warning", "getFormSubmissionInfo", "basename", "method", "action", "formData", "body", "attr", "getAttribute", "stripBasename", "type", "form", "Error", "name", "prefix", "undefined", "REACT_ROUTER_VERSION", "window", "__reactRouterVersion", "createBrowserRouter", "routes", "opts", "createRouter", "future", "_extends", "v7_prependBasename", "history", "createBrowserHistory", "hydrationData", "parseHydrationData", "unstable_dataStrategy", "unstable_patchRoutesOnNavigation", "initialize", "createHashRouter", "createHashHistory", "_window", "state", "__staticRouterHydrationData", "errors", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponseImpl", "status", "statusText", "data", "internal", "__subType", "ErrorConstructor", "error", "message", "stack", "ViewTransitionContext", "createContext", "isTransitioning", "displayName", "FetchersContext", "Map", "START_TRANSITION", "startTransitionImpl", "React", "FLUSH_SYNC", "flushSyncImpl", "ReactDOM", "USE_ID", "useIdImpl", "startTransitionSafe", "cb", "flushSyncSafe", "Deferred", "constructor", "promise", "Promise", "resolve", "reject", "reason", "RouterProvider", "_ref", "fallbackElement", "router", "setStateImpl", "useState", "pendingState", "setPendingState", "vtContext", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "fetcherData", "useRef", "v7_startTransition", "optInStartTransition", "useCallback", "setState", "newState", "_ref2", "deletedFetchers", "unstable_flushSync", "flushSync", "unstable_viewTransitionOpts", "viewTransitionOpts", "current", "delete", "fetchers", "fetcher", "set", "isViewTransitionUnavailable", "startViewTransition", "skipTransition", "currentLocation", "nextLocation", "t", "finished", "finally", "useLayoutEffect", "subscribe", "useEffect", "renderPromise", "location", "v7_partialHydration", "navigator", "useMemo", "createHref", "encodeLocation", "go", "n", "navigate", "push", "to", "preventScrollReset", "replace", "dataRouterContext", "static", "routerFuture", "v7_relativeSplatPath", "Fragment", "DataRouterContext", "Provider", "DataRouterStateContext", "Router", "navigationType", "historyAction", "initialized", "MemoizedDataRoutes", "DataRoutes", "_ref3", "useRoutesImpl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref4", "children", "historyRef", "v5Compat", "listen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref5", "HistoryRouter", "_ref6", "<PERSON><PERSON><PERSON><PERSON>", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "LinkWithRef", "_ref7", "ref", "onClick", "relative", "reloadDocument", "unstable_viewTransition", "rest", "_objectWithoutPropertiesLoose", "_excluded", "useContext", "NavigationContext", "absoluteHref", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "NavLink", "NavLinkWithRef", "_ref8", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "_excluded2", "useResolvedPath", "useLocation", "routerState", "useViewTransitionState", "toPathname", "locationPathname", "nextLocationPathname", "navigation", "endSlashPosition", "endsWith", "length", "isActive", "char<PERSON>t", "isPending", "renderProps", "aria<PERSON>urrent", "filter", "Boolean", "join", "Form", "_ref9", "forwardedRef", "fetcher<PERSON>ey", "onSubmit", "props", "_excluded3", "submit", "useSubmit", "formAction", "useFormAction", "formMethod", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "_ref10", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "invariant", "useDataRouterState", "_temp", "replaceProp", "useNavigate", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "validateClientSideSubmission", "fetcherId", "getUniqueFetcherId", "String", "UseSubmit", "currentRouteId", "useRouteId", "options", "fetch", "formEncType", "fromRouteId", "_temp2", "routeContext", "RouteContext", "match", "matches", "slice", "params", "get", "toString", "route", "index", "joinPaths", "useFetcher", "_temp3", "_route$matches", "UseFetcher", "routeId", "id", "defaultKey", "setFetcher<PERSON>ey", "getFetcher", "deleteFetcher", "load", "submitImpl", "FetcherForm", "IDLE_FETCHER", "fetcherWithComponents", "useFetchers", "UseFetchers", "from", "_ref11", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "_temp4", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "_ref12", "when", "blocker", "useBlocker", "proceed", "confirm", "setTimeout", "reset", "currentPath", "nextPath", "matchPath"]}