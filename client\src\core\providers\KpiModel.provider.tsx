import { useState, useEffect } from 'react';
import { getModelScenariosById, getKpiConfigurations, getModelById } from '@/shared/services/kpi.service';
import { KpiModelContext } from '../contexts/kpimodel.context';
import { KpiModelResponse } from '@/shared/models/model-response.model';
import { WORKFLOW_STATUS } from '@/shared/enum';

interface KpiModelProviderProps {
  modelId: any;
  children: any;
}

const KpiModelProvider = ({ modelId, children }: KpiModelProviderProps) => {
  const [modelData, setModelData] = useState<KpiModelResponse | null>(null);
  const [modelScenarioData, setModelScenarioData] = useState<any>(null);
  const [kpiConfigs, setKpiConfigs] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [isEditable, setEditable] = useState(false);
  const [error, setError] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const modelResponse = await getModelById(modelId);
        setModelData(modelResponse);
        setEditable(modelResponse?.workflow_status === WORKFLOW_STATUS.DRAFT);
        const modelScenarioResponse = await getModelScenariosById(modelId);
        setModelScenarioData(modelScenarioResponse);
        const kpiConfigsResponse = await getKpiConfigurations(modelId);
        setKpiConfigs(kpiConfigsResponse);
      } catch (error) {
        setError(error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [modelId]);

  const refreshData = async () => {
    setLoading(true);
    try {
      // Refresh both model data and scenario data
      const modelResponse = await getModelById(modelId);
      setModelData(modelResponse);
      const modelScenarioResponse = await getModelScenariosById(modelId);
      setModelScenarioData(modelScenarioResponse);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  const refreshModelData = async () => {
    setLoading(true);
    try {
      const modelResponse = await getModelById(modelId);
      setModelData(modelResponse);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KpiModelContext.Provider
      value={{
        modelData,
        modelScenarioData,
        kpiConfigs,
        loading,
        modelId,
        isEditable,
        error,
        refreshData,
        refreshModelData,
      }}
    >
      {children}
    </KpiModelContext.Provider>
  );
};

export { KpiModelProvider, KpiModelContext };
