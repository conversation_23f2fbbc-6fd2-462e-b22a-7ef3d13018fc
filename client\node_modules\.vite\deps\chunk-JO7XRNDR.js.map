{"version": 3, "sources": ["../../@remix-run/router/history.ts", "../../@remix-run/router/utils.ts", "../../@remix-run/router/router.ts", "../../react-router/lib/context.ts", "../../react-router/lib/hooks.tsx", "../../react-router/lib/components.tsx", "../../react-router/index.ts"], "sourcesContent": ["////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n// TODO: (v7) Change the Location generic default from `any` to `unknown` and\n// remove Remix `useLocation` wrapper.\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location<State = any> extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: State;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n\n  /**\n   * The delta between this location and the former location in the history stack\n   */\n  delta: number | null;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. This may be either a URL or the pieces\n * of a URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Returns a URL for the given `to` value\n   *\n   * @param to - The destination URL\n   */\n  createURL(to: To): URL;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLACE navigations for data routers\n   * behave the same as POP\n   *\n   * @param to Unencoded path\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n  idx: number;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  function createHref(to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to: To) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\",\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n\n    // Hash URL should always have a leading / just like window.location.pathname\n    // does, so if an app ends up at a route like /#something then we add a\n    // leading slash so all of our path-matching behaves the same as if it would\n    // in a browser router.  This is particularly important when there exists a\n    // root splat route (<Route path=\"*\">) since that matches internally against\n    // \"/*\" and we'd expect /#something to 404 in a hash router app.\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport function warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience, so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location, index: number): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  let index = getIndex()!;\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n\n  function getIndex(): number {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n\n  function createURL(to: To): URL {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base =\n      window.location.origin !== \"null\"\n        ? window.location.origin\n        : window.location.href;\n\n    let href = typeof to === \"string\" ? to : createPath(to);\n    // Treating this as a full URL will strip any trailing spaces so we need to\n    // pre-encode them since they might be part of a matching splat param from\n    // an ancestor route\n    href = href.replace(/ $/, \"%20\");\n    invariant(\n      base,\n      `No window.location.(origin|href) available to create URL for href: ${href}`\n    );\n    return new URL(href, base);\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { invariant, parsePath, warning } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  // We keep the raw Response for redirects so we can return it verbatim\n  response: Response;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\ntype LowerCaseFormMethod = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\ntype UpperCaseFormMethod = Uppercase<LowerCaseFormMethod>;\n\n/**\n * Users can specify either lowercase or uppercase form methods on `<Form>`,\n * useSubmit(), `<fetcher.Form>`, etc.\n */\nexport type HTMLFormMethod = LowerCaseFormMethod | UpperCaseFormMethod;\n\n/**\n * Active navigation/fetcher form methods are exposed in lowercase on the\n * RouterState\n */\nexport type FormMethod = LowerCaseFormMethod;\nexport type MutationFormMethod = Exclude<FormMethod, \"get\">;\n\n/**\n * In v7, active navigation/fetcher form methods are exposed in uppercase on the\n * RouterState.  This is to align with the normalization done via fetch().\n */\nexport type V7_FormMethod = UpperCaseFormMethod;\nexport type V7_MutationFormMethod = Exclude<V7_FormMethod, \"GET\">;\n\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\"\n  | \"application/json\"\n  | \"text/plain\";\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport type Submission =\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: FormData;\n      json: undefined;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: JsonValue;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: undefined;\n      text: string;\n    };\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs<Context> {\n  request: Request;\n  params: Params;\n  context?: Context;\n}\n\n// TODO: (v7) Change the defaults from any to unknown in and remove Remix wrappers:\n//   ActionFunction, ActionFunctionArgs, LoaderFunction, LoaderFunctionArgs\n//   Also, make them a type alias instead of an interface\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Loaders and actions can return anything except `undefined` (`null` is a\n * valid return value if there is no data to return).  Responses are preferred\n * and will ease any future migration to Remix\n */\ntype DataFunctionValue = Response | NonNullable<unknown> | null;\n\ntype DataFunctionReturnValue = Promise<DataFunctionValue> | DataFunctionValue;\n\n/**\n * Route loader function signature\n */\nexport type LoaderFunction<Context = any> = {\n  (\n    args: LoaderFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n} & { hydrate?: boolean };\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction<Context = any> {\n  (\n    args: ActionFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n}\n\n/**\n * Arguments passed to shouldRevalidate function\n */\nexport interface ShouldRevalidateFunctionArgs {\n  currentUrl: URL;\n  currentParams: AgnosticDataRouteMatch[\"params\"];\n  nextUrl: URL;\n  nextParams: AgnosticDataRouteMatch[\"params\"];\n  formMethod?: Submission[\"formMethod\"];\n  formAction?: Submission[\"formAction\"];\n  formEncType?: Submission[\"formEncType\"];\n  text?: Submission[\"text\"];\n  formData?: Submission[\"formData\"];\n  json?: Submission[\"json\"];\n  actionStatus?: number;\n  actionResult?: any;\n  defaultShouldRevalidate: boolean;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: ShouldRevalidateFunctionArgs): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set `hasErrorBoundary`\n * from the framework-aware `errorElement` prop\n *\n * @deprecated Use `mapRouteProperties` instead\n */\nexport interface DetectErrorBoundaryFunction {\n  (route: AgnosticRouteObject): boolean;\n}\n\nexport interface DataStrategyMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {\n  shouldLoad: boolean;\n  resolve: (\n    handlerOverride?: (\n      handler: (ctx?: unknown) => DataFunctionReturnValue\n    ) => DataFunctionReturnValue\n  ) => Promise<DataStrategyResult>;\n}\n\nexport interface DataStrategyFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {\n  matches: DataStrategyMatch[];\n  fetcherKey: string | null;\n}\n\n/**\n * Result from a loader or action called via dataStrategy\n */\nexport interface DataStrategyResult {\n  type: \"data\" | \"error\";\n  result: unknown; // data, Error, Response, DeferredData, DataWithResponseInit\n}\n\nexport interface DataStrategyFunction {\n  (args: DataStrategyFunctionArgs): Promise<Record<string, DataStrategyResult>>;\n}\n\nexport interface AgnosticPatchRoutesOnNavigationFunction<\n  M extends AgnosticRouteMatch = AgnosticRouteMatch\n> {\n  (opts: {\n    path: string;\n    matches: M[];\n    patch: (routeId: string | null, children: AgnosticRouteObject[]) => void;\n  }): void | Promise<void>;\n}\n\n/**\n * Function provided by the framework-aware layers to set any framework-specific\n * properties from framework-agnostic properties\n */\nexport interface MapRoutePropertiesFunction {\n  (route: AgnosticRouteObject): {\n    hasErrorBoundary: boolean;\n  } & Record<string, any>;\n}\n\n/**\n * Keys we cannot change from within a lazy() function. We spread all other keys\n * onto the route. Either they're meaningful to the router, or they'll get\n * ignored.\n */\nexport type ImmutableRouteKey =\n  | \"lazy\"\n  | \"caseSensitive\"\n  | \"path\"\n  | \"id\"\n  | \"index\"\n  | \"children\";\n\nexport const immutableRouteKeys = new Set<ImmutableRouteKey>([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"children\",\n]);\n\ntype RequireOne<T, Key = keyof T> = Exclude<\n  {\n    [K in keyof T]: K extends Key ? Omit<T, K> & Required<Pick<T, K>> : never;\n  }[keyof T],\n  undefined\n>;\n\n/**\n * lazy() function to load a route definition, which can add non-matching\n * related properties to a route\n */\nexport interface LazyRouteFunction<R extends AgnosticRouteObject> {\n  (): Promise<RequireOne<Omit<R, ImmutableRouteKey>>>;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction | boolean;\n  action?: ActionFunction | boolean;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n  lazy?: LazyRouteFunction<AgnosticBaseRouteObject>;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\nexport type RouteManifest = Record<string, AgnosticDataRouteObject | undefined>;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `:${infer Param}`\n    ? Param extends `${infer Optional}?`\n      ? Optional\n      : Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\nexport type PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\" | \"/*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise, return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if you could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary, so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  mapRouteProperties: MapRoutePropertiesFunction,\n  parentPath: string[] = [],\n  manifest: RouteManifest = {}\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, String(index)];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !manifest[id],\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n      };\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n        children: undefined,\n      };\n      manifest[id] = pathOrLayoutRoute;\n\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(\n          route.children,\n          mapRouteProperties,\n          treePath,\n          manifest\n        );\n      }\n\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  return matchRoutesImpl(routes, locationArg, basename, false);\n}\n\nexport function matchRoutesImpl<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename: string,\n  allowPartial: boolean\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch<string, RouteObjectType>(\n      branches[i],\n      decoded,\n      allowPartial\n    );\n  }\n\n  return matches;\n}\n\nexport interface UIMatch<Data = unknown, Handle = unknown> {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: Data;\n  handle: Handle;\n}\n\nexport function convertRouteMatchToUiMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UIMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle,\n  };\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  let flattenRoute = (\n    route: RouteObjectType,\n    index: number,\n    relativePath?: string\n  ) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath:\n        relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array, so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta,\n    });\n  };\n  routes.forEach((route, index) => {\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n\n  return branches;\n}\n\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path: string): string[] {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n\n  let [first, ...rest] = segments;\n\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n\n  let result: string[] = [];\n\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children, so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explode _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(\n    ...restExploded.map((subpath) =>\n      subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n\n  // Then, if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map((exploded) =>\n    path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:[\\w-]+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string,\n  allowPartial = false\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    let route = meta.route;\n\n    if (\n      !match &&\n      end &&\n      allowPartial &&\n      !routesMeta[routesMeta.length - 1].route.index\n    ) {\n      match = matchPath(\n        {\n          path: meta.relativePath,\n          caseSensitive: meta.caseSensitive,\n          end: false,\n        },\n        remainingPathname\n      );\n    }\n\n    if (!match) {\n      return null;\n    }\n\n    Object.assign(matchedParams, match.params);\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  originalPath: Path,\n  params: {\n    [key in PathParam<Path>]: string | null;\n  } = {} as any\n): string {\n  let path: string = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were ` +\n        `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n        `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n        `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\") as Path;\n  }\n\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n\n  const stringify = (p: any) =>\n    p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n\n  const segments = path\n    .split(/\\/+/)\n    .map((segment, index, array) => {\n      const isLastSegment = index === array.length - 1;\n\n      // only apply the splat if it's the last segment\n      if (isLastSegment && segment === \"*\") {\n        const star = \"*\" as PathParam<Path>;\n        // Apply the splat\n        return stringify(params[star]);\n      }\n\n      const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n      if (keyMatch) {\n        const [, key, optional] = keyMatch;\n        let param = params[key as PathParam<Path>];\n        invariant(optional === \"?\" || param != null, `Missing \":${key}\" param`);\n        return stringify(param);\n      }\n\n      // Remove any optional markers from optional static segments\n      return segment.replace(/\\?$/g, \"\");\n    })\n    // Remove empty segments\n    .filter((segment) => !!segment);\n\n  return prefix + segments.join(\"/\");\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, compiledParams] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = compiledParams.reduce<Mutable<Params>>(\n    (memo, { paramName, isOptional }, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      const value = captureGroups[index];\n      if (isOptional && !value) {\n        memo[paramName] = undefined;\n      } else {\n        memo[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n      }\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\ntype CompiledPathParam = { paramName: string; isOptional?: boolean };\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, CompiledPathParam[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let params: CompiledPathParam[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(\n        /\\/:([\\w-]+)(\\?)?/g,\n        (_: string, paramName: string, isOptional) => {\n          params.push({ paramName, isOptional: isOptional != null });\n          return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n        }\n      );\n\n  if (path.endsWith(\"*\")) {\n    params.push({ paramName: \"*\" });\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex, so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, params];\n}\n\nexport function decodePath(value: string) {\n  try {\n    return value\n      .split(\"/\")\n      .map((v) => decodeURIComponent(v).replace(/\\//g, \"%2F\"))\n      .join(\"/\");\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n// Return the array of pathnames for the current route matches - used to\n// generate the routePathnames input for resolveTo()\nexport function getResolveToMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[], v7_relativeSplatPath: boolean) {\n  let pathMatches = getPathContributingMatches(matches);\n\n  // When v7_relativeSplatPath is enabled, use the full pathname for the leaf\n  // match so we include splat values for \".\" links.  See:\n  // https://github.com/remix-run/react-router/issues/11052#issuecomment-**********\n  if (v7_relativeSplatPath) {\n    return pathMatches.map((match, idx) =>\n      idx === pathMatches.length - 1 ? match.pathname : match.pathnameBase\n    );\n  }\n\n  return pathMatches.map((match) => match.pathnameBase);\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    // With relative=\"route\" (the default), each leading .. segment means\n    // \"go up one route\" instead of \"go up one URL segment\".  This is a key\n    // difference from how <a href> works and a major reason we call this a\n    // \"to\" value instead of a \"href\".\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport class DataWithResponseInit<D> {\n  type: string = \"DataWithResponseInit\";\n  data: D;\n  init: ResponseInit | null;\n\n  constructor(data: D, init?: ResponseInit) {\n    this.data = data;\n    this.init = init || null;\n  }\n}\n\n/**\n * Create \"responses\" that contain `status`/`headers` without forcing\n * serialization into an actual `Response` - used by Remix single fetch\n */\nexport function data<D>(data: D, init?: number | ResponseInit) {\n  return new DataWithResponseInit(\n    data,\n    typeof init === \"number\" ? { status: init } : init\n  );\n}\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeysSet: Set<string> = new Set<string>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscribers: Set<(aborted: boolean, settledKey?: string) => void> =\n    new Set();\n  data: Record<string, unknown>;\n  init?: ResponseInit;\n  deferredKeys: string[] = [];\n\n  constructor(data: Record<string, unknown>, responseInit?: ResponseInit) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n\n    this.init = responseInit;\n  }\n\n  private trackPromise(\n    key: string,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, undefined, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeysSet.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\n        `Deferred data for key \"${key}\" resolved/rejected with \\`undefined\\`, ` +\n          `you must resolve/reject with a value or \\`null\\`.`\n      );\n      Object.defineProperty(promise, \"_error\", { get: () => undefinedError });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    this.emit(false, key);\n    return data;\n  }\n\n  private emit(aborted: boolean, settledKey?: string) {\n    this.subscribers.forEach((subscriber) => subscriber(aborted, settledKey));\n  }\n\n  subscribe(fn: (aborted: boolean, settledKey?: string) => void) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport type DeferFunction = (\n  data: Record<string, unknown>,\n  init?: number | ResponseInit\n) => DeferredData;\n\nexport const defer: DeferFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  return new DeferredData(data, responseInit);\n};\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirectDocument: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\n\n/**\n * A redirect response that will perform a `history.replaceState` instead of a\n * `history.pushState` for client-side navigation redirects.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const replace: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Replace\", \"true\");\n  return response;\n};\n\nexport type ErrorResponse = {\n  status: number;\n  statusText: string;\n  data: any;\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n *\n * We don't export the class for public use since it's an implementation\n * detail, but we export the interface above so folks can build their own\n * abstractions around instances via isRouteErrorResponse()\n */\nexport class ErrorResponseImpl implements ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n  private error?: Error;\n  private internal: boolean;\n\n  constructor(\n    status: number,\n    statusText: string | undefined,\n    data: any,\n    internal = false\n  ) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nexport function isRouteErrorResponse(error: any): error is ErrorResponse {\n  return (\n    error != null &&\n    typeof error.status === \"number\" &&\n    typeof error.statusText === \"string\" &&\n    typeof error.internal === \"boolean\" &&\n    \"data\" in error\n  );\n}\n", "import type { History, Location, Path, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  invariant,\n  parsePath,\n  warning,\n} from \"./history\";\nimport type {\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  DataStrategyMatch,\n  AgnosticRouteObject,\n  DataResult,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DeferredData,\n  DeferredResult,\n  DetectErrorBoundaryFunction,\n  ErrorResult,\n  FormEncType,\n  FormMethod,\n  HTMLFormMethod,\n  DataStrategyResult,\n  ImmutableRouteKey,\n  MapRoutePropertiesFunction,\n  MutationFormMethod,\n  RedirectResult,\n  RouteData,\n  RouteManifest,\n  ShouldRevalidateFunctionArgs,\n  Submission,\n  SuccessResult,\n  UIMatch,\n  V7_FormMethod,\n  V7_MutationFormMethod,\n  AgnosticPatchRoutesOnNavigationFunction,\n  DataWithResponseInit,\n} from \"./utils\";\nimport {\n  ErrorResponseImpl,\n  ResultType,\n  convertRouteMatchToUiMatch,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  getResolveToMatches,\n  immutableRouteKeys,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  matchRoutesImpl,\n  resolveTo,\n  stripBasename,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the future config for the router\n   */\n  get future(): FutureConfig;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the window associated with the router\n   */\n  get window(): RouterInit[\"window\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): Promise<void>;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To | null, opts?: RouterNavigateOptions): Promise<void>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to URL encode a destination path according to the internal\n   * history implementation\n   * @param to\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get a navigation blocker\n   * @param key The identifier for the blocker\n   * @param fn The blocker function implementation\n   */\n  getBlocker(key: string, fn: BlockerFunction): Blocker;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete a navigation blocker\n   * @param key The identifier for the blocker\n   */\n  deleteBlocker(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE DO NOT USE\n   *\n   * Patch additional children routes into an existing parent route\n   * @param routeId The parent route id or a callback function accepting `patch`\n   *                to perform batch patching\n   * @param children The additional children routes\n   */\n  patchRoutes(routeId: string | null, children: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * HMR needs to pass in-flight route updates to React Router\n   * TODO: Replace this with granular route update APIs (addRoute, updateRoute, deleteRoute)\n   */\n  _internalSetRoutes(routes: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n\n  /**\n   * Map of current blockers\n   */\n  blockers: Map<string, Blocker>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface FutureConfig {\n  v7_fetcherPersist: boolean;\n  v7_normalizeFormMethod: boolean;\n  v7_partialHydration: boolean;\n  v7_prependBasename: boolean;\n  v7_relativeSplatPath: boolean;\n  v7_skipActionErrorRevalidation: boolean;\n}\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  routes: AgnosticRouteObject[];\n  history: History;\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<FutureConfig>;\n  hydrationData?: HydrationState;\n  window?: Window;\n  unstable_patchRoutesOnNavigation?: AgnosticPatchRoutesOnNavigationFunction;\n  unstable_dataStrategy?: DataStrategyFunction;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  basename: Router[\"basename\"];\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  activeDeferreds: Record<string, DeferredData> | null;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(\n    request: Request,\n    opts?: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      unstable_dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<StaticHandlerContext | Response>;\n  queryRoute(\n    request: Request,\n    opts?: {\n      routeId?: string;\n      requestContext?: unknown;\n      unstable_dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<any>;\n}\n\ntype ViewTransitionOpts = {\n  currentLocation: Location;\n  nextLocation: Location;\n};\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (\n    state: RouterState,\n    opts: {\n      deletedFetchers: string[];\n      unstable_viewTransitionOpts?: ViewTransitionOpts;\n      unstable_flushSync: boolean;\n    }\n  ): void;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UIMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\n// Allowed for any navigation or fetch\ntype BaseNavigateOrFetchOptions = {\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  unstable_flushSync?: boolean;\n};\n\n// Only allowed for navigations\ntype BaseNavigateOptions = BaseNavigateOrFetchOptions & {\n  replace?: boolean;\n  state?: any;\n  fromRouteId?: string;\n  unstable_viewTransition?: boolean;\n};\n\n// Only allowed for submission navigations\ntype BaseSubmissionOptions = {\n  formMethod?: HTMLFormMethod;\n  formEncType?: FormEncType;\n} & (\n  | { formData: FormData; body?: undefined }\n  | { formData?: undefined; body: any }\n);\n\n/**\n * Options for a navigate() call for a normal (non-submission) navigation\n */\ntype LinkNavigateOptions = BaseNavigateOptions;\n\n/**\n * Options for a navigate() call for a submission navigation\n */\ntype SubmissionNavigateOptions = BaseNavigateOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to navigate() for a navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options for a fetch() load\n */\ntype LoadFetchOptions = BaseNavigateOrFetchOptions;\n\n/**\n * Options for a fetch() submission\n */\ntype SubmitFetchOptions = BaseNavigateOrFetchOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions = LoadFetchOptions | SubmitFetchOptions;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    json: undefined;\n    text: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    text: Submission[\"text\"];\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    text: undefined;\n    formData: undefined;\n    json: undefined;\n    data: TData | undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    data: TData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    text: Submission[\"text\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    data: TData | undefined;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface BlockerBlocked {\n  state: \"blocked\";\n  reset(): void;\n  proceed(): void;\n  location: Location;\n}\n\ninterface BlockerUnblocked {\n  state: \"unblocked\";\n  reset: undefined;\n  proceed: undefined;\n  location: undefined;\n}\n\ninterface BlockerProceeding {\n  state: \"proceeding\";\n  reset: undefined;\n  proceed: undefined;\n  location: Location;\n}\n\nexport type Blocker = BlockerUnblocked | BlockerBlocked | BlockerProceeding;\n\nexport type BlockerFunction = (args: {\n  currentLocation: Location;\n  nextLocation: Location;\n  historyAction: HistoryAction;\n}) => boolean;\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ntype PendingActionResult = [string, SuccessResult | ErrorResult];\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * Tuple for the returned or thrown value from the current action.  The routeId\n   * is the action route for success and the bubbled boundary route for errors.\n   */\n  pendingActionResult?: PendingActionResult;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Cached info for active fetcher.load() instances so they can participate\n * in revalidation\n */\ninterface FetchLoadMatch {\n  routeId: string;\n  path: string;\n}\n\n/**\n * Identified fetcher.load() calls that need to be revalidated\n */\ninterface RevalidatingFetcher extends FetchLoadMatch {\n  key: string;\n  match: AgnosticDataRouteMatch | null;\n  matches: AgnosticDataRouteMatch[] | null;\n  controller: AbortController | null;\n}\n\nconst validMutationMethodsArr: MutationFormMethod[] = [\n  \"post\",\n  \"put\",\n  \"patch\",\n  \"delete\",\n];\nconst validMutationMethods = new Set<MutationFormMethod>(\n  validMutationMethodsArr\n);\n\nconst validRequestMethodsArr: FormMethod[] = [\n  \"get\",\n  ...validMutationMethodsArr,\n];\nconst validRequestMethods = new Set<FormMethod>(validRequestMethodsArr);\n\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_BLOCKER: BlockerUnblocked = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined,\n};\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\nconst defaultMapRouteProperties: MapRoutePropertiesFunction = (route) => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary),\n});\n\nconst TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  const routerWindow = init.window\n    ? init.window\n    : typeof window !== \"undefined\"\n    ? window\n    : undefined;\n  const isBrowser =\n    typeof routerWindow !== \"undefined\" &&\n    typeof routerWindow.document !== \"undefined\" &&\n    typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  // Routes keyed by ID\n  let manifest: RouteManifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(\n    init.routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n  let inFlightDataRoutes: AgnosticDataRouteObject[] | undefined;\n  let basename = init.basename || \"/\";\n  let dataStrategyImpl = init.unstable_dataStrategy || defaultDataStrategy;\n  let patchRoutesOnNavigationImpl = init.unstable_patchRoutesOnNavigation;\n\n  // Config driven behavior flags\n  let future: FutureConfig = {\n    v7_fetcherPersist: false,\n    v7_normalizeFormMethod: false,\n    v7_partialHydration: false,\n    v7_prependBasename: false,\n    v7_relativeSplatPath: false,\n    v7_skipActionErrorRevalidation: false,\n    ...init.future,\n  };\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // FIFO queue of previously discovered routes to prevent re-calling on\n  // subsequent navigations to the same path\n  let discoveredRoutesMaxSize = 1000;\n  let discoveredRoutes = new Set<string>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null && !patchRoutesOnNavigationImpl) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname,\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  // In SPA apps, if the user provided a patchRoutesOnNavigation implementation and\n  // our initial match is a splat route, clear them out so we run through lazy\n  // discovery on hydration in case there's a more accurate lazy route match.\n  // In SSR apps (with `hydrationData`), we expect that the server will send\n  // up the proper matched routes so we don't want to run lazy discovery on\n  // initial hydration and want to hydrate into the splat route.\n  if (initialMatches && !init.hydrationData) {\n    let fogOfWar = checkFogOfWar(\n      initialMatches,\n      dataRoutes,\n      init.history.location.pathname\n    );\n    if (fogOfWar.active) {\n      initialMatches = null;\n    }\n  }\n\n  let initialized: boolean;\n  if (!initialMatches) {\n    initialized = false;\n    initialMatches = [];\n\n    // If partial hydration and fog of war is enabled, we will be running\n    // `patchRoutesOnNavigation` during hydration so include any partial matches as\n    // the initial matches so we can properly render `HydrateFallback`'s\n    if (future.v7_partialHydration) {\n      let fogOfWar = checkFogOfWar(\n        null,\n        dataRoutes,\n        init.history.location.pathname\n      );\n      if (fogOfWar.active && fogOfWar.matches) {\n        initialMatches = fogOfWar.matches;\n      }\n    }\n  } else if (initialMatches.some((m) => m.route.lazy)) {\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    initialized = false;\n  } else if (!initialMatches.some((m) => m.route.loader)) {\n    // If we've got no loaders to run, then we're good to go\n    initialized = true;\n  } else if (future.v7_partialHydration) {\n    // If partial hydration is enabled, we're initialized so long as we were\n    // provided with hydrationData for every route with a loader, and no loaders\n    // were marked for explicit hydration\n    let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n    let errors = init.hydrationData ? init.hydrationData.errors : null;\n    let isRouteInitialized = (m: AgnosticDataRouteMatch) => {\n      // No loader, nothing to initialize\n      if (!m.route.loader) {\n        return true;\n      }\n      // Explicitly opting-in to running on hydration\n      if (\n        typeof m.route.loader === \"function\" &&\n        m.route.loader.hydrate === true\n      ) {\n        return false;\n      }\n      // Otherwise, initialized if hydrated with data or an error\n      return (\n        (loaderData && loaderData[m.route.id] !== undefined) ||\n        (errors && errors[m.route.id] !== undefined)\n      );\n    };\n\n    // If errors exist, don't consider routes below the boundary\n    if (errors) {\n      let idx = initialMatches.findIndex(\n        (m) => errors![m.route.id] !== undefined\n      );\n      initialized = initialMatches.slice(0, idx + 1).every(isRouteInitialized);\n    } else {\n      initialized = initialMatches.every(isRouteInitialized);\n    }\n  } else {\n    // Without partial hydration - we're initialized if we were provided any\n    // hydrationData - which is expected to be complete\n    initialized = init.hydrationData != null;\n  }\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n\n  // Should the current navigation enable document.startViewTransition?\n  let pendingViewTransitionEnabled = false;\n\n  // Store applied view transitions so we can apply them on POP\n  let appliedViewTransitions: Map<string, Set<string>> = new Map<\n    string,\n    Set<string>\n  >();\n\n  // Cleanup function for persisting applied transitions to sessionStorage\n  let removePageHideEventListener: (() => void) | null = null;\n\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: Set<string> = new Set();\n\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set<string>();\n\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n\n  // Ref-count mounted fetchers so we know when it's ok to clean them up\n  let activeFetchers = new Map<string, number>();\n\n  // Fetchers that have requested a delete when using v7_fetcherPersist,\n  // they'll be officially removed after they return to idle\n  let deletedFetchers = new Set<string>();\n\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map<string, BlockerFunction>();\n\n  // Map of pending patchRoutesOnNavigation() promises (keyed by path/matches) so\n  // that we only kick them off once for a given combo\n  let pendingPatchRoutes = new Map<\n    string,\n    ReturnType<AgnosticPatchRoutesOnNavigationFunction>\n  >();\n\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let unblockBlockerHistoryUpdate: (() => void) | undefined = undefined;\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        // Ignore this event if it was just us resetting the URL from a\n        // blocked POP navigation\n        if (unblockBlockerHistoryUpdate) {\n          unblockBlockerHistoryUpdate();\n          unblockBlockerHistoryUpdate = undefined;\n          return;\n        }\n\n        warning(\n          blockerFunctions.size === 0 || delta != null,\n          \"You are trying to use a blocker on a POP navigation to a location \" +\n            \"that was not created by @remix-run/router. This will fail silently in \" +\n            \"production. This can happen if you are navigating outside the router \" +\n            \"via `window.history.pushState`/`window.location.hash` instead of using \" +\n            \"router navigation APIs.  This can also happen if you are using \" +\n            \"createHashRouter and the user manually changes the URL.\"\n        );\n\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction,\n        });\n\n        if (blockerKey && delta != null) {\n          // Restore the URL to match the current UI, but don't update router state\n          let nextHistoryUpdatePromise = new Promise<void>((resolve) => {\n            unblockBlockerHistoryUpdate = resolve;\n          });\n          init.history.go(delta * -1);\n\n          // Put the blocker into a blocked state\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey!, {\n                state: \"proceeding\",\n                proceed: undefined,\n                reset: undefined,\n                location,\n              });\n              // Re-do the same POP navigation we just blocked, after the url\n              // restoration is also complete.  See:\n              // https://github.com/remix-run/react-router/issues/11613\n              nextHistoryUpdatePromise.then(() => init.history.go(delta));\n            },\n            reset() {\n              let blockers = new Map(state.blockers);\n              blockers.set(blockerKey!, IDLE_BLOCKER);\n              updateState({ blockers });\n            },\n          });\n          return;\n        }\n\n        return startNavigation(historyAction, location);\n      }\n    );\n\n    if (isBrowser) {\n      // FIXME: This feels gross.  How can we cleanup the lines between\n      // scrollRestoration/appliedTransitions persistance?\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () =>\n        persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () =>\n        routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location, {\n        initialHydration: true,\n      });\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(\n    newState: Partial<RouterState>,\n    opts: {\n      flushSync?: boolean;\n      viewTransitionOpts?: ViewTransitionOpts;\n    } = {}\n  ): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n\n    // Prep fetcher cleanup so we can tell the UI which fetcher data entries\n    // can be removed\n    let completedFetchers: string[] = [];\n    let deletedFetchersKeys: string[] = [];\n\n    if (future.v7_fetcherPersist) {\n      state.fetchers.forEach((fetcher, key) => {\n        if (fetcher.state === \"idle\") {\n          if (deletedFetchers.has(key)) {\n            // Unmounted from the UI and can be totally removed\n            deletedFetchersKeys.push(key);\n          } else {\n            // Returned to idle but still mounted in the UI, so semi-remains for\n            // revalidations and such\n            completedFetchers.push(key);\n          }\n        }\n      });\n    }\n\n    // Iterate over a local copy so that if flushSync is used and we end up\n    // removing and adding a new subscriber due to the useCallback dependencies,\n    // we don't get ourselves into a loop calling the new subscriber immediately\n    [...subscribers].forEach((subscriber) =>\n      subscriber(state, {\n        deletedFetchers: deletedFetchersKeys,\n        unstable_viewTransitionOpts: opts.viewTransitionOpts,\n        unstable_flushSync: opts.flushSync === true,\n      })\n    );\n\n    // Remove idle fetchers from state since we only care about in-flight fetchers.\n    if (future.v7_fetcherPersist) {\n      completedFetchers.forEach((key) => state.fetchers.delete(key));\n      deletedFetchersKeys.forEach((key) => deleteFetcher(key));\n    }\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>,\n    { flushSync }: { flushSync?: boolean } = {}\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      isMutationMethod(state.navigation.formMethod) &&\n      state.navigation.state === \"loading\" &&\n      location.state?._isRedirect !== true;\n\n    let actionData: RouteData | null;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData\n      ? mergeLoaderData(\n          state.loaderData,\n          newState.loaderData,\n          newState.matches || [],\n          newState.errors\n        )\n      : state.loaderData;\n\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset =\n      pendingPreventScrollReset === true ||\n      (state.navigation.formMethod != null &&\n        isMutationMethod(state.navigation.formMethod) &&\n        location.state?._isRedirect !== true);\n\n    // Commit any in-flight routes at the end of the HMR revalidation \"navigation\"\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    let viewTransitionOpts: ViewTransitionOpts | undefined;\n\n    // On POP, enable transitions if they were enabled on the original navigation\n    if (pendingAction === HistoryAction.Pop) {\n      // Forward takes precedence so they behave like the original navigation\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location,\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        // If we don't have a previous forward nav, assume we're popping back to\n        // the new location and enable if that location previously enabled\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location,\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      // Store the applied transition on PUSH/REPLACE\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = new Set<string>([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location,\n      };\n    }\n\n    updateState(\n      {\n        ...newState, // matches, errors, fetchers go through as-is\n        actionData,\n        loaderData,\n        historyAction: pendingAction,\n        location,\n        initialized: true,\n        navigation: IDLE_NAVIGATION,\n        revalidation: \"idle\",\n        restoreScrollPosition: getSavedScrollPosition(\n          location,\n          newState.matches || state.matches\n        ),\n        preventScrollReset,\n        blockers,\n      },\n      {\n        viewTransitionOpts,\n        flushSync: flushSync === true,\n      }\n    );\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To | null,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      to,\n      future.v7_relativeSplatPath,\n      opts?.fromRouteId,\n      opts?.relative\n    );\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      false,\n      normalizedPath,\n      opts\n    );\n\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation),\n    };\n\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n\n    let historyAction = HistoryAction.Push;\n\n    if (userReplace === true) {\n      historyAction = HistoryAction.Replace;\n    } else if (userReplace === false) {\n      // no-op\n    } else if (\n      submission != null &&\n      isMutationMethod(submission.formMethod) &&\n      submission.formAction === state.location.pathname + state.location.search\n    ) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = HistoryAction.Replace;\n    }\n\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    let flushSync = (opts && opts.unstable_flushSync) === true;\n\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction,\n    });\n\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey!, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation,\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey!, IDLE_BLOCKER);\n          updateState({ blockers });\n        },\n      });\n      return;\n    }\n\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.unstable_viewTransition,\n      flushSync,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      {\n        overrideNavigation: state.navigation,\n        // Proxy through any rending view transition\n        enableViewTransition: pendingViewTransitionEnabled === true,\n      }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      initialHydration?: boolean;\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponseImpl;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n      enableViewTransition?: boolean;\n      flushSync?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, location.pathname);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let { error, notFoundMatches, route } = handleNavigational404(\n        location.pathname\n      );\n      completeNavigation(\n        location,\n        {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        },\n        { flushSync }\n      );\n      return;\n    }\n\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial load will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (\n      state.initialized &&\n      !isRevalidationRequired &&\n      isHashChangeOnly(state.location, location) &&\n      !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))\n    ) {\n      completeNavigation(location, { matches }, { flushSync });\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionResult: PendingActionResult | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingActionResult = [\n        findNearestBoundary(matches).route.id,\n        { type: ResultType.error, error: opts.pendingError },\n      ];\n    } else if (\n      opts &&\n      opts.submission &&\n      isMutationMethod(opts.submission.formMethod)\n    ) {\n      // Call action if we received an action submission\n      let actionResult = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        fogOfWar.active,\n        { replace: opts.replace, flushSync }\n      );\n\n      if (actionResult.shortCircuited) {\n        return;\n      }\n\n      // If we received a 404 from handleAction, it's because we couldn't lazily\n      // discover the destination route so we don't want to call loaders\n      if (actionResult.pendingActionResult) {\n        let [routeId, result] = actionResult.pendingActionResult;\n        if (\n          isErrorResult(result) &&\n          isRouteErrorResponse(result.error) &&\n          result.error.status === 404\n        ) {\n          pendingNavigationController = null;\n\n          completeNavigation(location, {\n            matches: actionResult.matches,\n            loaderData: {},\n            errors: {\n              [routeId]: result.error,\n            },\n          });\n          return;\n        }\n      }\n\n      matches = actionResult.matches || matches;\n      pendingActionResult = actionResult.pendingActionResult;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n      // No need to do fog of war matching again on loader execution\n      fogOfWar.active = false;\n\n      // Create a GET request for the loaders\n      request = createClientSideRequest(\n        init.history,\n        request.url,\n        request.signal\n      );\n    }\n\n    // Call loaders\n    let {\n      shortCircuited,\n      matches: updatedMatches,\n      loaderData,\n      errors,\n    } = await handleLoaders(\n      request,\n      location,\n      matches,\n      fogOfWar.active,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.fetcherSubmission,\n      opts && opts.replace,\n      opts && opts.initialHydration === true,\n      flushSync,\n      pendingActionResult\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches: updatedMatches || matches,\n      ...getActionDataForCommit(pendingActionResult),\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    opts: { replace?: boolean; flushSync?: boolean } = {}\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({ navigation }, { flushSync: opts.flushSync === true });\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let { boundaryId, error } = handleDiscoverRouteError(\n          location.pathname,\n          discoverResult\n        );\n        return {\n          matches: discoverResult.partialMatches,\n          pendingActionResult: [\n            boundaryId,\n            {\n              type: ResultType.error,\n              error,\n            },\n          ],\n        };\n      } else if (!discoverResult.matches) {\n        let { notFoundMatches, error, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [\n            route.id,\n            {\n              type: ResultType.error,\n              error,\n            },\n          ],\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id,\n        }),\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        state,\n        request,\n        [actionMatch],\n        matches,\n        null\n      );\n      result = results[actionMatch.route.id];\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace: boolean;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        let location = normalizeRedirectLocation(\n          result.response.headers.get(\"Location\")!,\n          new URL(request.url),\n          basename\n        );\n        replace = location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(request, result, true, {\n        submission,\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions to the current location are REPLACE\n      // navigations, but if the action threw an error that'll be rendered in\n      // an errorElement, we fall back to PUSH so that the user can use the\n      // back button to get back to the pre-submission form location to try\n      // again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        matches,\n        pendingActionResult: [boundaryMatch.route.id, result],\n      };\n    }\n\n    return {\n      matches,\n      pendingActionResult: [actionMatch.route.id, result],\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    fetcherSubmission?: Submission,\n    replace?: boolean,\n    initialHydration?: boolean,\n    flushSync?: boolean,\n    pendingActionResult?: PendingActionResult\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation =\n      overrideNavigation || getLoadingNavigation(location, submission);\n\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission =\n      submission ||\n      fetcherSubmission ||\n      getSubmissionFromNavigation(loadingNavigation);\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    // If we have partialHydration enabled, then don't update the state for the\n    // initial data load since it's not a \"navigation\"\n    let shouldUpdateNavigationState =\n      !isUninterruptedRevalidation &&\n      (!future.v7_partialHydration || !initialHydration);\n\n    // When fog of war is enabled, we enter our `loading` state earlier so we\n    // can discover new routes during the `loading` state.  We skip this if\n    // we've already run actions since we would have done our matching already.\n    // If the children() function threw then, we want to proceed with the\n    // partial matches it discovered.\n    if (isFogOfWar) {\n      if (shouldUpdateNavigationState) {\n        let actionData = getUpdatedActionData(pendingActionResult);\n        updateState(\n          {\n            navigation: loadingNavigation,\n            ...(actionData !== undefined ? { actionData } : {}),\n          },\n          {\n            flushSync,\n          }\n        );\n      }\n\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let { boundaryId, error } = handleDiscoverRouteError(\n          location.pathname,\n          discoverResult\n        );\n        return {\n          matches: discoverResult.partialMatches,\n          loaderData: {},\n          errors: {\n            [boundaryId]: error,\n          },\n        };\n      } else if (!discoverResult.matches) {\n        let { error, notFoundMatches, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      future.v7_partialHydration && initialHydration === true,\n      future.v7_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      pendingActionResult\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(\n        location,\n        {\n          matches,\n          loaderData: {},\n          // Commit pending error if we're short circuiting\n          errors:\n            pendingActionResult && isErrorResult(pendingActionResult[1])\n              ? { [pendingActionResult[0]]: pendingActionResult[1].error }\n              : null,\n          ...getActionDataForCommit(pendingActionResult),\n          ...(updatedFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n        },\n        { flushSync }\n      );\n      return { shortCircuited: true };\n    }\n\n    if (shouldUpdateNavigationState) {\n      let updates: Partial<RouterState> = {};\n      if (!isFogOfWar) {\n        // Only update navigation/actionNData if we didn't already do it above\n        updates.navigation = loadingNavigation;\n        let actionData = getUpdatedActionData(pendingActionResult);\n        if (actionData !== undefined) {\n          updates.actionData = actionData;\n        }\n      }\n      if (revalidatingFetchers.length > 0) {\n        updates.fetchers = getUpdatedRevalidatingFetchers(revalidatingFetchers);\n      }\n      updateState(updates, { flushSync });\n    }\n\n    revalidatingFetchers.forEach((rf) => {\n      if (fetchControllers.has(rf.key)) {\n        abortFetcher(rf.key);\n      }\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((f) => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      matchesToLoad,\n      loaderResults,\n      pendingActionResult,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    // During partial hydration, preserve SSR errors for routes that don't re-run\n    if (future.v7_partialHydration && initialHydration && state.errors) {\n      Object.entries(state.errors)\n        .filter(([id]) => !matchesToLoad.some((m) => m.route.id === id))\n        .forEach(([routeId, error]) => {\n          errors = Object.assign(errors || {}, { [routeId]: error });\n        });\n    }\n\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers =\n      updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n\n    return {\n      matches,\n      loaderData,\n      errors,\n      ...(shouldUpdateFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n    };\n  }\n\n  function getUpdatedActionData(\n    pendingActionResult: PendingActionResult | undefined\n  ): Record<string, RouteData> | null | undefined {\n    if (pendingActionResult && !isErrorResult(pendingActionResult[1])) {\n      // This is cast to `any` currently because `RouteData`uses any and it\n      // would be a breaking change to use any.\n      // TODO: v7 - change `RouteData` to use `unknown` instead of `any`\n      return {\n        [pendingActionResult[0]]: pendingActionResult[1].data as any,\n      };\n    } else if (state.actionData) {\n      if (Object.keys(state.actionData).length === 0) {\n        return null;\n      } else {\n        return state.actionData;\n      }\n    }\n  }\n\n  function getUpdatedRevalidatingFetchers(\n    revalidatingFetchers: RevalidatingFetcher[]\n  ) {\n    revalidatingFetchers.forEach((rf) => {\n      let fetcher = state.fetchers.get(rf.key);\n      let revalidatingFetcher = getLoadingFetcher(\n        undefined,\n        fetcher ? fetcher.data : undefined\n      );\n      state.fetchers.set(rf.key, revalidatingFetcher);\n    });\n    return new Map(state.fetchers);\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    if (fetchControllers.has(key)) abortFetcher(key);\n    let flushSync = (opts && opts.unstable_flushSync) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      href,\n      future.v7_relativeSplatPath,\n      routeId,\n      opts?.relative\n    );\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, normalizedPath);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: normalizedPath }),\n        { flushSync }\n      );\n      return;\n    }\n\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      true,\n      normalizedPath,\n      opts\n    );\n\n    if (error) {\n      setFetcherError(key, routeId, error, { flushSync });\n      return;\n    }\n\n    let match = getTargetMatch(matches, path);\n\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(\n        key,\n        routeId,\n        path,\n        match,\n        matches,\n        fogOfWar.active,\n        flushSync,\n        submission\n      );\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, { routeId, path });\n    handleFetcherLoader(\n      key,\n      routeId,\n      path,\n      match,\n      matches,\n      fogOfWar.active,\n      flushSync,\n      submission\n    );\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    function detectAndHandle405Error(m: AgnosticDataRouteMatch) {\n      if (!m.route.action && !m.route.lazy) {\n        let error = getInternalRouterError(405, {\n          method: submission.formMethod,\n          pathname: path,\n          routeId: routeId,\n        });\n        setFetcherError(key, routeId, error, { flushSync });\n        return true;\n      }\n      return false;\n    }\n\n    if (!isFogOfWar && detectAndHandle405Error(match)) {\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync,\n    });\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        requestMatches,\n        path,\n        fetchRequest.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        let { error } = handleDiscoverRouteError(path, discoverResult);\n        setFetcherError(key, routeId, error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        requestMatches = discoverResult.matches;\n        match = getTargetMatch(requestMatches, path);\n\n        if (detectAndHandle405Error(match)) {\n          return;\n        }\n      }\n    }\n\n    // Call the action for the fetcher\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let actionResults = await callDataStrategy(\n      \"action\",\n      state,\n      fetchRequest,\n      [match],\n      requestMatches,\n      key\n    );\n    let actionResult = actionResults[match.route.id];\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    // When using v7_fetcherPersist, we don't want errors bubbling up to the UI\n    // or redirects processed for unmounted fetchers so we just revert them to\n    // idle\n    if (future.v7_fetcherPersist && deletedFetchers.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      }\n      // Let SuccessResult's fall through for revalidation\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          // A new navigation was kicked off after our action started, so that\n          // should take precedence over this redirect navigation.  We already\n          // set isRevalidationRequired so all loaders for the new route should\n          // fire unless opted out via shouldRevalidate\n          updateFetcherState(key, getDoneFetcher(undefined));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(fetchRequest, actionResult, false, {\n            fetcherSubmission: submission,\n          });\n        }\n      }\n\n      // Process any non-redirect errors thrown\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n      nextLocation,\n      abortController.signal\n    );\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(routesToUse, state.navigation.location, basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      false,\n      future.v7_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      [match.route.id, actionResult]\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter((rf) => rf.key !== key)\n      .forEach((rf) => {\n        let staleKey = rf.key;\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher = getLoadingFetcher(\n          undefined,\n          existingFetcher ? existingFetcher.data : undefined\n        );\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        if (fetchControllers.has(staleKey)) {\n          abortFetcher(staleKey);\n        }\n        if (rf.controller) {\n          fetchControllers.set(staleKey, rf.controller);\n        }\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((rf) => abortFetcher(rf.key));\n\n    abortController.signal.addEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    abortController.signal.removeEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect.result,\n        false\n      );\n    }\n\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect.result,\n        false\n      );\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      matchesToLoad,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n\n    abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        fetchers: new Map(state.fetchers),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    submission?: Submission\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(\n      key,\n      getLoadingFetcher(\n        submission,\n        existingFetcher ? existingFetcher.data : undefined\n      ),\n      { flushSync }\n    );\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        path,\n        fetchRequest.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        let { error } = handleDiscoverRouteError(path, discoverResult);\n        setFetcherError(key, routeId, error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        matches = discoverResult.matches;\n        match = getTargetMatch(matches, path);\n      }\n    }\n\n    // Call the loader for this fetcher route match\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let results = await callDataStrategy(\n      \"loader\",\n      state,\n      fetchRequest,\n      [match],\n      matches,\n      key\n    );\n    let result = results[match.route.id];\n\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // We don't want errors bubbling up or redirects followed for unmounted\n    // fetchers, so short circuit here if it was removed from the UI\n    if (deletedFetchers.has(key)) {\n      updateFetcherState(key, getDoneFetcher(undefined));\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(fetchRequest, result, false);\n        return;\n      }\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    request: Request,\n    redirect: RedirectResult,\n    isNavigation: boolean,\n    {\n      submission,\n      fetcherSubmission,\n      replace,\n    }: {\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      replace?: boolean;\n    } = {}\n  ) {\n    if (redirect.response.headers.has(\"X-Remix-Revalidate\")) {\n      isRevalidationRequired = true;\n    }\n\n    let location = redirect.response.headers.get(\"Location\");\n    invariant(location, \"Expected a Location header on the redirect Response\");\n    location = normalizeRedirectLocation(\n      location,\n      new URL(request.url),\n      basename\n    );\n    let redirectLocation = createLocation(state.location, location, {\n      _isRedirect: true,\n    });\n\n    if (isBrowser) {\n      let isDocumentReload = false;\n\n      if (redirect.response.headers.has(\"X-Remix-Reload-Document\")) {\n        // Hard reload if the response contained X-Remix-Reload-Document\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(location)) {\n        const url = init.history.createURL(location);\n        isDocumentReload =\n          // Hard reload if it's an absolute URL to a new origin\n          url.origin !== routerWindow.location.origin ||\n          // Hard reload if it's an absolute URL that does not match our basename\n          stripBasename(url.pathname, basename) == null;\n      }\n\n      if (isDocumentReload) {\n        if (replace) {\n          routerWindow.location.replace(location);\n        } else {\n          routerWindow.location.assign(location);\n        }\n        return;\n      }\n    }\n\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true || redirect.response.headers.has(\"X-Remix-Replace\")\n        ? HistoryAction.Replace\n        : HistoryAction.Push;\n\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let { formMethod, formAction, formEncType } = state.navigation;\n    if (\n      !submission &&\n      !fetcherSubmission &&\n      formMethod &&\n      formAction &&\n      formEncType\n    ) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    let activeSubmission = submission || fetcherSubmission;\n    if (\n      redirectPreserveMethodStatusCodes.has(redirect.response.status) &&\n      activeSubmission &&\n      isMutationMethod(activeSubmission.formMethod)\n    ) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: {\n          ...activeSubmission,\n          formAction: location,\n        },\n        // Preserve these flags across redirects\n        preventScrollReset: pendingPreventScrollReset,\n        enableViewTransition: isNavigation\n          ? pendingViewTransitionEnabled\n          : undefined,\n      });\n    } else {\n      // If we have a navigation submission, we will preserve it through the\n      // redirect navigation\n      let overrideNavigation = getLoadingNavigation(\n        redirectLocation,\n        submission\n      );\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve these flags across redirects\n        preventScrollReset: pendingPreventScrollReset,\n        enableViewTransition: isNavigation\n          ? pendingViewTransitionEnabled\n          : undefined,\n      });\n    }\n  }\n\n  // Utility wrapper for calling dataStrategy client-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    state: RouterState,\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    fetcherKey: string | null\n  ): Promise<Record<string, DataResult>> {\n    let results: Record<string, DataStrategyResult>;\n    let dataResults: Record<string, DataResult> = {};\n    try {\n      results = await callDataStrategyImpl(\n        dataStrategyImpl,\n        type,\n        state,\n        request,\n        matchesToLoad,\n        matches,\n        fetcherKey,\n        manifest,\n        mapRouteProperties\n      );\n    } catch (e) {\n      // If the outer dataStrategy method throws, just return the error for all\n      // matches - and it'll naturally bubble to the root\n      matchesToLoad.forEach((m) => {\n        dataResults[m.route.id] = {\n          type: ResultType.error,\n          error: e,\n        };\n      });\n      return dataResults;\n    }\n\n    for (let [routeId, result] of Object.entries(results)) {\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result as Response;\n        dataResults[routeId] = {\n          type: ResultType.redirect,\n          response: normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            routeId,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          ),\n        };\n      } else {\n        dataResults[routeId] = await convertDataStrategyResultToDataResult(\n          result\n        );\n      }\n    }\n\n    return dataResults;\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    state: RouterState,\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    let currentMatches = state.matches;\n\n    // Kick off loaders and fetchers in parallel\n    let loaderResultsPromise = callDataStrategy(\n      \"loader\",\n      state,\n      request,\n      matchesToLoad,\n      matches,\n      null\n    );\n\n    let fetcherResultsPromise = Promise.all(\n      fetchersToLoad.map(async (f) => {\n        if (f.matches && f.match && f.controller) {\n          let results = await callDataStrategy(\n            \"loader\",\n            state,\n            createClientSideRequest(init.history, f.path, f.controller.signal),\n            [f.match],\n            f.matches,\n            f.key\n          );\n          let result = results[f.match.route.id];\n          // Fetcher results are keyed by fetcher key from here on out, not routeId\n          return { [f.key]: result };\n        } else {\n          return Promise.resolve({\n            [f.key]: {\n              type: ResultType.error,\n              error: getInternalRouterError(404, {\n                pathname: f.path,\n              }),\n            } as ErrorResult,\n          });\n        }\n      })\n    );\n\n    let loaderResults = await loaderResultsPromise;\n    let fetcherResults = (await fetcherResultsPromise).reduce(\n      (acc, r) => Object.assign(acc, r),\n      {}\n    );\n\n    await Promise.all([\n      resolveNavigationDeferredResults(\n        matches,\n        loaderResults,\n        request.signal,\n        currentMatches,\n        state.loaderData\n      ),\n      resolveFetcherDeferredResults(matches, fetcherResults, fetchersToLoad),\n    ]);\n\n    return {\n      loaderResults,\n      fetcherResults,\n    };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.add(key);\n        abortFetcher(key);\n      }\n    });\n  }\n\n  function updateFetcherState(\n    key: string,\n    fetcher: Fetcher,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    state.fetchers.set(key, fetcher);\n    updateState(\n      { fetchers: new Map(state.fetchers) },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function setFetcherError(\n    key: string,\n    routeId: string,\n    error: any,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState(\n      {\n        errors: {\n          [boundaryMatch.route.id]: error,\n        },\n        fetchers: new Map(state.fetchers),\n      },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    if (future.v7_fetcherPersist) {\n      activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n      // If this fetcher was previously marked for deletion, unmark it since we\n      // have a new instance\n      if (deletedFetchers.has(key)) {\n        deletedFetchers.delete(key);\n      }\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  function deleteFetcher(key: string): void {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (\n      fetchControllers.has(key) &&\n      !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))\n    ) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    deletedFetchers.delete(key);\n    cancelledFetcherLoads.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function deleteFetcherAndUpdateState(key: string): void {\n    if (future.v7_fetcherPersist) {\n      let count = (activeFetchers.get(key) || 0) - 1;\n      if (count <= 0) {\n        activeFetchers.delete(key);\n        deletedFetchers.add(key);\n      } else {\n        activeFetchers.set(key, count);\n      }\n    } else {\n      deleteFetcher(key);\n    }\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, `Expected fetch controller: ${key}`);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): boolean {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function getBlocker(key: string, fn: BlockerFunction) {\n    let blocker: Blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n\n    return blocker;\n  }\n\n  function deleteBlocker(key: string) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key: string, newBlocker: Blocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(\n      (blocker.state === \"unblocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"proceeding\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"unblocked\") ||\n        (blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\"),\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({ blockers });\n  }\n\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction,\n  }: {\n    currentLocation: Location;\n    nextLocation: Location;\n    historyAction: HistoryAction;\n  }): string | undefined {\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return blockerKey;\n    }\n  }\n\n  function handleNavigational404(pathname: string) {\n    let error = getInternalRouterError(404, { pathname });\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let { matches, route } = getShortCircuitMatches(routesToUse);\n\n    // Cancel all pending deferred on 404s since we don't keep any routes\n    cancelActiveDeferreds();\n\n    return { notFoundMatches: matches, route, error };\n  }\n\n  function handleDiscoverRouteError(\n    pathname: string,\n    discoverResult: DiscoverRoutesErrorResult\n  ) {\n    return {\n      boundaryId: findNearestBoundary(discoverResult.partialMatches).route.id,\n      error: getInternalRouterError(400, {\n        type: \"route-discovery\",\n        pathname,\n        message:\n          discoverResult.error != null && \"message\" in discoverResult.error\n            ? discoverResult.error\n            : String(discoverResult.error),\n      }),\n    };\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function getScrollKey(location: Location, matches: AgnosticDataRouteMatch[]) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(\n        location,\n        matches.map((m) => convertRouteMatchToUiMatch(m, state.loaderData))\n      );\n      return key || location.key;\n    }\n    return location.key;\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  function checkFogOfWar(\n    matches: AgnosticDataRouteMatch[] | null,\n    routesToUse: AgnosticDataRouteObject[],\n    pathname: string\n  ): { active: boolean; matches: AgnosticDataRouteMatch[] | null } {\n    if (patchRoutesOnNavigationImpl) {\n      // Don't bother re-calling patchRouteOnMiss for a path we've already\n      // processed.  the last execution would have patched the route tree\n      // accordingly so `matches` here are already accurate.\n      if (discoveredRoutes.has(pathname)) {\n        return { active: false, matches };\n      }\n\n      if (!matches) {\n        let fogMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n          routesToUse,\n          pathname,\n          basename,\n          true\n        );\n\n        return { active: true, matches: fogMatches || [] };\n      } else {\n        if (Object.keys(matches[0].params).length > 0) {\n          // If we matched a dynamic param or a splat, it might only be because\n          // we haven't yet discovered other routes that would match with a\n          // higher score.  Call patchRoutesOnNavigation just to be sure\n          let partialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n            routesToUse,\n            pathname,\n            basename,\n            true\n          );\n          return { active: true, matches: partialMatches };\n        }\n      }\n    }\n\n    return { active: false, matches: null };\n  }\n\n  type DiscoverRoutesSuccessResult = {\n    type: \"success\";\n    matches: AgnosticDataRouteMatch[] | null;\n  };\n  type DiscoverRoutesErrorResult = {\n    type: \"error\";\n    error: any;\n    partialMatches: AgnosticDataRouteMatch[];\n  };\n  type DiscoverRoutesAbortedResult = { type: \"aborted\" };\n  type DiscoverRoutesResult =\n    | DiscoverRoutesSuccessResult\n    | DiscoverRoutesErrorResult\n    | DiscoverRoutesAbortedResult;\n\n  async function discoverRoutes(\n    matches: AgnosticDataRouteMatch[],\n    pathname: string,\n    signal: AbortSignal\n  ): Promise<DiscoverRoutesResult> {\n    let partialMatches: AgnosticDataRouteMatch[] | null = matches;\n    while (true) {\n      let isNonHMR = inFlightDataRoutes == null;\n      let routesToUse = inFlightDataRoutes || dataRoutes;\n      try {\n        await loadLazyRouteChildren(\n          patchRoutesOnNavigationImpl!,\n          pathname,\n          partialMatches,\n          routesToUse,\n          manifest,\n          mapRouteProperties,\n          pendingPatchRoutes,\n          signal\n        );\n      } catch (e) {\n        return { type: \"error\", error: e, partialMatches };\n      } finally {\n        // If we are not in the middle of an HMR revalidation and we changed the\n        // routes, provide a new identity so when we `updateState` at the end of\n        // this navigation/fetch `router.routes` will be a new identity and\n        // trigger a re-run of memoized `router.routes` dependencies.\n        // HMR will already update the identity and reflow when it lands\n        // `inFlightDataRoutes` in `completeNavigation`\n        if (isNonHMR) {\n          dataRoutes = [...dataRoutes];\n        }\n      }\n\n      if (signal.aborted) {\n        return { type: \"aborted\" };\n      }\n\n      let newMatches = matchRoutes(routesToUse, pathname, basename);\n      if (newMatches) {\n        addToFifoQueue(pathname, discoveredRoutes);\n        return { type: \"success\", matches: newMatches };\n      }\n\n      let newPartialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n        routesToUse,\n        pathname,\n        basename,\n        true\n      );\n\n      // Avoid loops if the second pass results in the same partial matches\n      if (\n        !newPartialMatches ||\n        (partialMatches.length === newPartialMatches.length &&\n          partialMatches.every(\n            (m, i) => m.route.id === newPartialMatches![i].route.id\n          ))\n      ) {\n        addToFifoQueue(pathname, discoveredRoutes);\n        return { type: \"success\", matches: null };\n      }\n\n      partialMatches = newPartialMatches;\n    }\n  }\n\n  function addToFifoQueue(path: string, queue: Set<string>) {\n    if (queue.size >= discoveredRoutesMaxSize) {\n      let first = queue.values().next().value;\n      queue.delete(first);\n    }\n    queue.add(path);\n  }\n\n  function _internalSetRoutes(newRoutes: AgnosticDataRouteObject[]) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(\n      newRoutes,\n      mapRouteProperties,\n      undefined,\n      manifest\n    );\n  }\n\n  function patchRoutes(\n    routeId: string | null,\n    children: AgnosticRouteObject[]\n  ): void {\n    let isNonHMR = inFlightDataRoutes == null;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    patchRoutesImpl(\n      routeId,\n      children,\n      routesToUse,\n      manifest,\n      mapRouteProperties\n    );\n\n    // If we are not in the middle of an HMR revalidation and we changed the\n    // routes, provide a new identity and trigger a reflow via `updateState`\n    // to re-run memoized `router.routes` dependencies.\n    // HMR will already update the identity and reflow when it lands\n    // `inFlightDataRoutes` in `completeNavigation`\n    if (isNonHMR) {\n      dataRoutes = [...dataRoutes];\n      updateState({});\n    }\n  }\n\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    encodeLocation: (to: To) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: deleteFetcherAndUpdateState,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    patchRoutes,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nexport const UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface StaticHandlerFutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_throwAbortReason: boolean;\n}\n\nexport interface CreateStaticHandlerOptions {\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<StaticHandlerFutureConfig>;\n}\n\nexport function createStaticHandler(\n  routes: AgnosticRouteObject[],\n  opts?: CreateStaticHandlerOptions\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n\n  let manifest: RouteManifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (opts?.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts?.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Config driven behavior flags\n  let future: StaticHandlerFutureConfig = {\n    v7_relativeSplatPath: false,\n    v7_throwAbortReason: false,\n    ...(opts ? opts.future : null),\n  };\n\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   *\n   * - `opts.requestContext` is an optional server context that will be passed\n   *   to actions/loaders in the `context` parameter\n   * - `opts.skipLoaderErrorBubbling` is an optional parameter that will prevent\n   *   the bubbling of errors which allows single-fetch-type implementations\n   *   where the client will handle the bubbling and we may need to return data\n   *   for the handling route\n   */\n  async function query(\n    request: Request,\n    {\n      requestContext,\n      skipLoaderErrorBubbling,\n      unstable_dataStrategy,\n    }: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      unstable_dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      unstable_dataStrategy || null,\n      skipLoaderErrorBubbling === true,\n      null\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, basename, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   *\n   * - `opts.routeId` allows you to specify the specific route handler to call.\n   *   If not provided the handler will determine the proper route by matching\n   *   against `request.url`\n   * - `opts.requestContext` is an optional server context that will be passed\n   *    to actions/loaders in the `context` parameter\n   */\n  async function queryRoute(\n    request: Request,\n    {\n      routeId,\n      requestContext,\n      unstable_dataStrategy,\n    }: {\n      requestContext?: unknown;\n      routeId?: string;\n      unstable_dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<any> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId,\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      unstable_dataStrategy || null,\n      false,\n      match\n    );\n\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n\n    if (result.loaderData) {\n      let data = Object.values(result.loaderData)[0];\n      if (result.activeDeferreds?.[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n\n    return undefined;\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    unstable_dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          unstable_dataStrategy,\n          skipLoaderErrorBubbling,\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        unstable_dataStrategy,\n        skipLoaderErrorBubbling,\n        routeMatch\n      );\n      return isResponse(result)\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction for a\n      // `queryRoute` call, we throw the `DataStrategyResult` to bail out early\n      // and then return or throw the raw Response here accordingly\n      if (isDataStrategyResult(e) && isResponse(e.result)) {\n        if (e.type === ResultType.error) {\n          throw e.result;\n        }\n        return e.result;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    requestContext: unknown,\n    unstable_dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    let result: DataResult;\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id,\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        request,\n        [actionMatch],\n        matches,\n        isRouteRequest,\n        requestContext,\n        unstable_dataStrategy\n      );\n      result = results[actionMatch.route.id];\n\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest, future);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.response.status,\n        headers: {\n          Location: result.response.headers.get(\"Location\")!,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, { type: \"defer-action\" });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal,\n    });\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = skipLoaderErrorBubbling\n        ? actionMatch\n        : findNearestBoundary(matches, actionMatch.route.id);\n\n      let context = await loadRouteData(\n        loaderRequest,\n        matches,\n        requestContext,\n        unstable_dataStrategy,\n        skipLoaderErrorBubbling,\n        null,\n        [boundaryMatch.route.id, result]\n      );\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : result.statusCode != null\n          ? result.statusCode\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    let context = await loadRouteData(\n      loaderRequest,\n      matches,\n      requestContext,\n      unstable_dataStrategy,\n      skipLoaderErrorBubbling,\n      null\n    );\n\n    return {\n      ...context,\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionHeaders: result.headers\n        ? { [actionMatch.route.id]: result.headers }\n        : {},\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    unstable_dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null,\n    pendingActionResult?: PendingActionResult\n  ): Promise<\n    | Omit<\n        StaticHandlerContext,\n        \"location\" | \"basename\" | \"actionData\" | \"actionHeaders\"\n      >\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (\n      isRouteRequest &&\n      !routeMatch?.route.loader &&\n      !routeMatch?.route.lazy\n    ) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id,\n      });\n    }\n\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? getLoaderMatchesUntilBoundary(matches, pendingActionResult[0])\n      : matches;\n    let matchesToLoad = requestMatches.filter(\n      (m) => m.route.loader || m.route.lazy\n    );\n\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce(\n          (acc, m) => Object.assign(acc, { [m.route.id]: null }),\n          {}\n        ),\n        errors:\n          pendingActionResult && isErrorResult(pendingActionResult[1])\n            ? {\n                [pendingActionResult[0]]: pendingActionResult[1].error,\n              }\n            : null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let results = await callDataStrategy(\n      \"loader\",\n      request,\n      matchesToLoad,\n      matches,\n      isRouteRequest,\n      requestContext,\n      unstable_dataStrategy\n    );\n\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest, future);\n    }\n\n    // Process and commit output from loaders\n    let activeDeferreds = new Map<string, DeferredData>();\n    let context = processRouteLoaderData(\n      matches,\n      results,\n      pendingActionResult,\n      activeDeferreds,\n      skipLoaderErrorBubbling\n    );\n\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set<string>(\n      matchesToLoad.map((match) => match.route.id)\n    );\n    matches.forEach((match) => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n\n    return {\n      ...context,\n      matches,\n      activeDeferreds:\n        activeDeferreds.size > 0\n          ? Object.fromEntries(activeDeferreds.entries())\n          : null,\n    };\n  }\n\n  // Utility wrapper for calling dataStrategy server-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    isRouteRequest: boolean,\n    requestContext: unknown,\n    unstable_dataStrategy: DataStrategyFunction | null\n  ): Promise<Record<string, DataResult>> {\n    let results = await callDataStrategyImpl(\n      unstable_dataStrategy || defaultDataStrategy,\n      type,\n      null,\n      request,\n      matchesToLoad,\n      matches,\n      null,\n      manifest,\n      mapRouteProperties,\n      requestContext\n    );\n\n    let dataResults: Record<string, DataResult> = {};\n    await Promise.all(\n      matches.map(async (match) => {\n        if (!(match.route.id in results)) {\n          return;\n        }\n        let result = results[match.route.id];\n        if (isRedirectDataStrategyResultResult(result)) {\n          let response = result.result as Response;\n          // Throw redirects and let the server handle them with an HTTP redirect\n          throw normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            match.route.id,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          );\n        }\n        if (isResponse(result.result) && isRouteRequest) {\n          // For SSR single-route requests, we want to hand Responses back\n          // directly without unwrapping\n          throw result;\n        }\n\n        dataResults[match.route.id] =\n          await convertDataStrategyResultToDataResult(result);\n      })\n    );\n    return dataResults;\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\nfunction throwStaticHandlerAbortedError(\n  request: Request,\n  isRouteRequest: boolean,\n  future: StaticHandlerFutureConfig\n) {\n  if (future.v7_throwAbortReason && request.signal.reason !== undefined) {\n    throw request.signal.reason;\n  }\n\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(`${method}() call aborted: ${request.method} ${request.url}`);\n}\n\nfunction isSubmissionNavigation(\n  opts: BaseNavigateOrFetchOptions\n): opts is SubmissionNavigateOptions {\n  return (\n    opts != null &&\n    ((\"formData\" in opts && opts.formData != null) ||\n      (\"body\" in opts && opts.body !== undefined))\n  );\n}\n\nfunction normalizeTo(\n  location: Path,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  prependBasename: boolean,\n  to: To | null,\n  v7_relativeSplatPath: boolean,\n  fromRouteId?: string,\n  relative?: RelativeRoutingType\n) {\n  let contextualMatches: AgnosticDataRouteMatch[];\n  let activeRouteMatch: AgnosticDataRouteMatch | undefined;\n  if (fromRouteId) {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n\n  // Resolve the relative path\n  let path = resolveTo(\n    to ? to : \".\",\n    getResolveToMatches(contextualMatches, v7_relativeSplatPath),\n    stripBasename(location.pathname, basename) || location.pathname,\n    relative === \"path\"\n  );\n\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n\n  // Add an ?index param for matched index routes if we don't already have one\n  if (\n    (to == null || to === \"\" || to === \".\") &&\n    activeRouteMatch &&\n    activeRouteMatch.route.index &&\n    !hasNakedIndexQuery(path.search)\n  ) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  normalizeFormMethod: boolean,\n  isFetcher: boolean,\n  path: string,\n  opts?: BaseNavigateOrFetchOptions\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponseImpl;\n} {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod }),\n    };\n  }\n\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, { type: \"invalid-body\" }),\n  });\n\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod\n    ? (rawFormMethod.toUpperCase() as V7_FormMethod)\n    : (rawFormMethod.toLowerCase() as FormMethod);\n  let formAction = stripHashFromPath(path);\n\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      let text =\n        typeof opts.body === \"string\"\n          ? opts.body\n          : opts.body instanceof FormData ||\n            opts.body instanceof URLSearchParams\n          ? // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n            Array.from(opts.body.entries()).reduce(\n              (acc, [name, value]) => `${acc}${name}=${value}\\n`,\n              \"\"\n            )\n          : String(opts.body);\n\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text,\n        },\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      try {\n        let json =\n          typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined,\n          },\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n\n  invariant(\n    typeof FormData === \"function\",\n    \"FormData is not available in this environment\"\n  );\n\n  let searchParams: URLSearchParams;\n  let formData: FormData;\n\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n\n  let submission: Submission = {\n    formMethod,\n    formAction,\n    formEncType:\n      (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined,\n  };\n\n  if (isMutationMethod(submission.formMethod)) {\n    return { path, submission };\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n\n  return { path: createPath(parsedPath), submission };\n}\n\n// Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId: string\n) {\n  let boundaryMatches = matches;\n  if (boundaryId) {\n    let index = matches.findIndex((m) => m.route.id === boundaryId);\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n  return boundaryMatches;\n}\n\nfunction getMatchesToLoad(\n  history: History,\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  isInitialLoad: boolean,\n  skipActionErrorRevalidation: boolean,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: Set<string>,\n  deletedFetchers: Set<string>,\n  fetchLoadMatches: Map<string, FetchLoadMatch>,\n  fetchRedirectIds: Set<string>,\n  routesToUse: AgnosticDataRouteObject[],\n  basename: string | undefined,\n  pendingActionResult?: PendingActionResult\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingActionResult\n    ? isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : pendingActionResult[1].data\n    : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryId =\n    pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[0]\n      : undefined;\n  let boundaryMatches = boundaryId\n    ? getLoaderMatchesUntilBoundary(matches, boundaryId)\n    : matches;\n\n  // Don't revalidate loaders by default after action 4xx/5xx responses\n  // when the flag is enabled.  They can still opt-into revalidation via\n  // `shouldRevalidate` via `actionResult`\n  let actionStatus = pendingActionResult\n    ? pendingActionResult[1].statusCode\n    : undefined;\n  let shouldSkipRevalidation =\n    skipActionErrorRevalidation && actionStatus && actionStatus >= 400;\n\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    let { route } = match;\n    if (route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n\n    if (route.loader == null) {\n      return false;\n    }\n\n    if (isInitialLoad) {\n      if (typeof route.loader !== \"function\" || route.loader.hydrate) {\n        return true;\n      }\n      return (\n        state.loaderData[route.id] === undefined &&\n        // Don't re-run if the loader ran and threw an error\n        (!state.errors || state.errors[route.id] === undefined)\n      );\n    }\n\n    // Always call the loader on new route instances and pending defer cancellations\n    if (\n      isNewLoader(state.loaderData, state.matches[index], match) ||\n      cancelledDeferredRoutes.some((id) => id === match.route.id)\n    ) {\n      return true;\n    }\n\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n\n    return shouldRevalidateLoader(match, {\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params,\n      ...submission,\n      actionResult,\n      actionStatus,\n      defaultShouldRevalidate: shouldSkipRevalidation\n        ? false\n        : // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n          isRevalidationRequired ||\n          currentUrl.pathname + currentUrl.search ===\n            nextUrl.pathname + nextUrl.search ||\n          // Search params affect all loaders\n          currentUrl.search !== nextUrl.search ||\n          isNewRouteInstance(currentRouteMatch, nextRouteMatch),\n    });\n  });\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate:\n    //  - on initial load (shouldn't be any fetchers then anyway)\n    //  - if fetcher won't be present in the subsequent render\n    //    - no longer matches the URL (v7_fetcherPersist=false)\n    //    - was unmounted but persisted due to v7_fetcherPersist=true\n    if (\n      isInitialLoad ||\n      !matches.some((m) => m.route.id === f.routeId) ||\n      deletedFetchers.has(key)\n    ) {\n      return;\n    }\n\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null,\n      });\n      return;\n    }\n\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.has(key)) {\n      // Always mark for revalidation if the fetcher was cancelled\n      cancelledFetcherLoads.delete(key);\n      shouldRevalidate = true;\n    } else if (\n      fetcher &&\n      fetcher.state !== \"idle\" &&\n      fetcher.data === undefined\n    ) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, {\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params,\n        ...submission,\n        actionResult,\n        actionStatus,\n        defaultShouldRevalidate: shouldSkipRevalidation\n          ? false\n          : isRevalidationRequired,\n      });\n    }\n\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController(),\n      });\n    }\n  });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath != null &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  loaderMatch: AgnosticDataRouteMatch,\n  arg: ShouldRevalidateFunctionArgs\n) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return arg.defaultShouldRevalidate;\n}\n\n/**\n * Idempotent utility to execute patchRoutesOnNavigation() to lazily load route\n * definitions and update the routes/routeManifest\n */\nasync function loadLazyRouteChildren(\n  patchRoutesOnNavigationImpl: AgnosticPatchRoutesOnNavigationFunction,\n  path: string,\n  matches: AgnosticDataRouteMatch[],\n  routes: AgnosticDataRouteObject[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  pendingRouteChildren: Map<\n    string,\n    ReturnType<typeof patchRoutesOnNavigationImpl>\n  >,\n  signal: AbortSignal\n) {\n  let key = [path, ...matches.map((m) => m.route.id)].join(\"-\");\n  try {\n    let pending = pendingRouteChildren.get(key);\n    if (!pending) {\n      pending = patchRoutesOnNavigationImpl({\n        path,\n        matches,\n        patch: (routeId, children) => {\n          if (!signal.aborted) {\n            patchRoutesImpl(\n              routeId,\n              children,\n              routes,\n              manifest,\n              mapRouteProperties\n            );\n          }\n        },\n      });\n      pendingRouteChildren.set(key, pending);\n    }\n\n    if (pending && isPromise<AgnosticRouteObject[]>(pending)) {\n      await pending;\n    }\n  } finally {\n    pendingRouteChildren.delete(key);\n  }\n}\n\nfunction patchRoutesImpl(\n  routeId: string | null,\n  children: AgnosticRouteObject[],\n  routesToUse: AgnosticDataRouteObject[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction\n) {\n  if (routeId) {\n    let route = manifest[routeId];\n    invariant(\n      route,\n      `No route found to patch children into: routeId = ${routeId}`\n    );\n    let dataChildren = convertRoutesToDataRoutes(\n      children,\n      mapRouteProperties,\n      [routeId, \"patch\", String(route.children?.length || \"0\")],\n      manifest\n    );\n    if (route.children) {\n      route.children.push(...dataChildren);\n    } else {\n      route.children = dataChildren;\n    }\n  } else {\n    let dataChildren = convertRoutesToDataRoutes(\n      children,\n      mapRouteProperties,\n      [\"patch\", String(routesToUse.length || \"0\")],\n      manifest\n    );\n    routesToUse.push(...dataChildren);\n  }\n}\n\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(\n  route: AgnosticDataRouteObject,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  manifest: RouteManifest\n) {\n  if (!route.lazy) {\n    return;\n  }\n\n  let lazyRoute = await route.lazy();\n\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates: Record<string, any> = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue =\n      routeToUpdate[lazyRouteProperty as keyof typeof routeToUpdate];\n\n    let isPropertyStaticallyDefined =\n      staticRouteValue !== undefined &&\n      // This property isn't static since it should always be updated based\n      // on the route updates\n      lazyRouteProperty !== \"hasErrorBoundary\";\n\n    warning(\n      !isPropertyStaticallyDefined,\n      `Route \"${routeToUpdate.id}\" has a static property \"${lazyRouteProperty}\" ` +\n        `defined but its lazy function is also returning a value for this property. ` +\n        `The lazy route property \"${lazyRouteProperty}\" will be ignored.`\n    );\n\n    if (\n      !isPropertyStaticallyDefined &&\n      !immutableRouteKeys.has(lazyRouteProperty as ImmutableRouteKey)\n    ) {\n      routeUpdates[lazyRouteProperty] =\n        lazyRoute[lazyRouteProperty as keyof typeof lazyRoute];\n    }\n  }\n\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, {\n    // To keep things framework agnostic, we use the provided\n    // `mapRouteProperties` (or wrapped `detectErrorBoundary`) function to\n    // set the framework-aware properties (`element`/`hasErrorBoundary`) since\n    // the logic will differ between frameworks.\n    ...mapRouteProperties(routeToUpdate),\n    lazy: undefined,\n  });\n}\n\n// Default implementation of `dataStrategy` which fetches all loaders in parallel\nasync function defaultDataStrategy({\n  matches,\n}: DataStrategyFunctionArgs): ReturnType<DataStrategyFunction> {\n  let matchesToLoad = matches.filter((m) => m.shouldLoad);\n  let results = await Promise.all(matchesToLoad.map((m) => m.resolve()));\n  return results.reduce(\n    (acc, result, i) =>\n      Object.assign(acc, { [matchesToLoad[i].route.id]: result }),\n    {}\n  );\n}\n\nasync function callDataStrategyImpl(\n  dataStrategyImpl: DataStrategyFunction,\n  type: \"loader\" | \"action\",\n  state: RouterState | null,\n  request: Request,\n  matchesToLoad: AgnosticDataRouteMatch[],\n  matches: AgnosticDataRouteMatch[],\n  fetcherKey: string | null,\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  requestContext?: unknown\n): Promise<Record<string, DataStrategyResult>> {\n  let loadRouteDefinitionsPromises = matches.map((m) =>\n    m.route.lazy\n      ? loadLazyRouteModule(m.route, mapRouteProperties, manifest)\n      : undefined\n  );\n\n  let dsMatches = matches.map((match, i) => {\n    let loadRoutePromise = loadRouteDefinitionsPromises[i];\n    let shouldLoad = matchesToLoad.some((m) => m.route.id === match.route.id);\n    // `resolve` encapsulates route.lazy(), executing the loader/action,\n    // and mapping return values/thrown errors to a `DataStrategyResult`.  Users\n    // can pass a callback to take fine-grained control over the execution\n    // of the loader/action\n    let resolve: DataStrategyMatch[\"resolve\"] = async (handlerOverride) => {\n      if (\n        handlerOverride &&\n        request.method === \"GET\" &&\n        (match.route.lazy || match.route.loader)\n      ) {\n        shouldLoad = true;\n      }\n      return shouldLoad\n        ? callLoaderOrAction(\n            type,\n            request,\n            match,\n            loadRoutePromise,\n            handlerOverride,\n            requestContext\n          )\n        : Promise.resolve({ type: ResultType.data, result: undefined });\n    };\n\n    return {\n      ...match,\n      shouldLoad,\n      resolve,\n    };\n  });\n\n  // Send all matches here to allow for a middleware-type implementation.\n  // handler will be a no-op for unneeded routes and we filter those results\n  // back out below.\n  let results = await dataStrategyImpl({\n    matches: dsMatches,\n    request,\n    params: matches[0].params,\n    fetcherKey,\n    context: requestContext,\n  });\n\n  // Wait for all routes to load here but 'swallow the error since we want\n  // it to bubble up from the `await loadRoutePromise` in `callLoaderOrAction` -\n  // called from `match.resolve()`\n  try {\n    await Promise.all(loadRouteDefinitionsPromises);\n  } catch (e) {\n    // No-op\n  }\n\n  return results;\n}\n\n// Default logic for calling a loader/action is the user has no specified a dataStrategy\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  loadRoutePromise: Promise<void> | undefined,\n  handlerOverride: Parameters<DataStrategyMatch[\"resolve\"]>[0],\n  staticContext?: unknown\n): Promise<DataStrategyResult> {\n  let result: DataStrategyResult;\n  let onReject: (() => void) | undefined;\n\n  let runHandler = (\n    handler: AgnosticRouteObject[\"loader\"] | AgnosticRouteObject[\"action\"]\n  ): Promise<DataStrategyResult> => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject: () => void;\n    // This will never resolve so safe to type it as Promise<DataStrategyResult> to\n    // satisfy the function return value\n    let abortPromise = new Promise<DataStrategyResult>((_, r) => (reject = r));\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n\n    let actualHandler = (ctx?: unknown) => {\n      if (typeof handler !== \"function\") {\n        return Promise.reject(\n          new Error(\n            `You cannot call the handler for a route which defines a boolean ` +\n              `\"${type}\" [routeId: ${match.route.id}]`\n          )\n        );\n      }\n      return handler(\n        {\n          request,\n          params: match.params,\n          context: staticContext,\n        },\n        ...(ctx !== undefined ? [ctx] : [])\n      );\n    };\n\n    let handlerPromise: Promise<DataStrategyResult> = (async () => {\n      try {\n        let val = await (handlerOverride\n          ? handlerOverride((ctx: unknown) => actualHandler(ctx))\n          : actualHandler());\n        return { type: \"data\", result: val };\n      } catch (e) {\n        return { type: \"error\", result: e };\n      }\n    })();\n\n    return Promise.race([handlerPromise, abortPromise]);\n  };\n\n  try {\n    let handler = match.route[type];\n\n    // If we have a route.lazy promise, await that first\n    if (loadRoutePromise) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let handlerError;\n        let [value] = await Promise.all([\n          // If the handler throws, don't let it immediately bubble out,\n          // since we need to let the lazy() execution finish so we know if this\n          // route has a boundary that can handle the error\n          runHandler(handler).catch((e) => {\n            handlerError = e;\n          }),\n          loadRoutePromise,\n        ]);\n        if (handlerError !== undefined) {\n          throw handlerError;\n        }\n        result = value!;\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadRoutePromise;\n\n        handler = match.route[type];\n        if (handler) {\n          // Handler still runs even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id,\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return { type: ResultType.data, result: undefined };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname,\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n\n    invariant(\n      result.result !== undefined,\n      `You defined ${type === \"action\" ? \"an action\" : \"a loader\"} for route ` +\n        `\"${match.route.id}\" but didn't return anything from your \\`${type}\\` ` +\n        `function. Please return a value or \\`null\\`.`\n    );\n  } catch (e) {\n    // We should already be catching and converting normal handler executions to\n    // DataStrategyResults and returning them, so anything that throws here is an\n    // unexpected error we still need to wrap\n    return { type: ResultType.error, result: e };\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n\n  return result;\n}\n\nasync function convertDataStrategyResultToDataResult(\n  dataStrategyResult: DataStrategyResult\n): Promise<DataResult> {\n  let { result, type } = dataStrategyResult;\n\n  if (isResponse(result)) {\n    let data: any;\n\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      // Check between word boundaries instead of startsWith() due to the last\n      // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data = null;\n        } else {\n          data = await result.json();\n        }\n      } else {\n        data = await result.text();\n      }\n    } catch (e) {\n      return { type: ResultType.error, error: e };\n    }\n\n    if (type === ResultType.error) {\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(result.status, result.statusText, data),\n        statusCode: result.status,\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (type === ResultType.error) {\n    if (isDataWithResponseInit(result)) {\n      if (result.data instanceof Error) {\n        return {\n          type: ResultType.error,\n          error: result.data,\n          statusCode: result.init?.status,\n        };\n      }\n\n      // Convert thrown unstable_data() to ErrorResponse instances\n      result = new ErrorResponseImpl(\n        result.init?.status || 500,\n        undefined,\n        result.data\n      );\n    }\n    return {\n      type: ResultType.error,\n      error: result,\n      statusCode: isRouteErrorResponse(result) ? result.status : undefined,\n    };\n  }\n\n  if (isDeferredData(result)) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: result.init?.status,\n      headers: result.init?.headers && new Headers(result.init.headers),\n    };\n  }\n\n  if (isDataWithResponseInit(result)) {\n    return {\n      type: ResultType.data,\n      data: result.data,\n      statusCode: result.init?.status,\n      headers: result.init?.headers\n        ? new Headers(result.init.headers)\n        : undefined,\n    };\n  }\n\n  return { type: ResultType.data, data: result };\n}\n\n// Support relative routing in internal redirects\nfunction normalizeRelativeRoutingRedirectResponse(\n  response: Response,\n  request: Request,\n  routeId: string,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  v7_relativeSplatPath: boolean\n) {\n  let location = response.headers.get(\"Location\");\n  invariant(\n    location,\n    \"Redirects returned/thrown from loaders/actions must have a Location header\"\n  );\n\n  if (!ABSOLUTE_URL_REGEX.test(location)) {\n    let trimmedMatches = matches.slice(\n      0,\n      matches.findIndex((m) => m.route.id === routeId) + 1\n    );\n    location = normalizeTo(\n      new URL(request.url),\n      trimmedMatches,\n      basename,\n      true,\n      location,\n      v7_relativeSplatPath\n    );\n    response.headers.set(\"Location\", location);\n  }\n\n  return response;\n}\n\nfunction normalizeRedirectLocation(\n  location: string,\n  currentUrl: URL,\n  basename: string\n): string {\n  if (ABSOLUTE_URL_REGEX.test(location)) {\n    // Strip off the protocol+origin for same-origin + same-basename absolute redirects\n    let normalizedLocation = location;\n    let url = normalizedLocation.startsWith(\"//\")\n      ? new URL(currentUrl.protocol + normalizedLocation)\n      : new URL(normalizedLocation);\n    let isSameBasename = stripBasename(url.pathname, basename) != null;\n    if (url.origin === currentUrl.origin && isSameBasename) {\n      return url.pathname + url.search + url.hash;\n    }\n  }\n  return location;\n}\n\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(\n  history: History,\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({ \"Content-Type\": formEncType });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (\n      formEncType === \"application/x-www-form-urlencoded\" &&\n      submission.formData\n    ) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n\n  return searchParams;\n}\n\nfunction convertSearchParamsToFormData(\n  searchParams: URLSearchParams\n): FormData {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  results: Record<string, DataResult>,\n  pendingActionResult: PendingActionResult | undefined,\n  activeDeferreds: Map<string, DeferredData>,\n  skipLoaderErrorBubbling: boolean\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n  let pendingError =\n    pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : undefined;\n\n  // Process loader results into state.loaderData/state.errors\n  matches.forEach((match) => {\n    if (!(match.route.id in results)) {\n      return;\n    }\n    let id = match.route.id;\n    let result = results[id];\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError !== undefined) {\n        error = pendingError;\n        pendingError = undefined;\n      }\n\n      errors = errors || {};\n\n      if (skipLoaderErrorBubbling) {\n        errors[id] = error;\n      } else {\n        // Look upwards from the matched route for the closest ancestor error\n        // boundary, defaulting to the root match.  Prefer higher error values\n        // if lower errors bubble to the same boundary\n        let boundaryMatch = findNearestBoundary(matches, id);\n        if (errors[boundaryMatch.route.id] == null) {\n          errors[boundaryMatch.route.id] = error;\n        }\n      }\n\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (\n          result.statusCode != null &&\n          result.statusCode !== 200 &&\n          !foundError\n        ) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      } else {\n        loaderData[id] = result.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError !== undefined && pendingActionResult) {\n    errors = { [pendingActionResult[0]]: pendingError };\n    loaderData[pendingActionResult[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: Record<string, DataResult>,\n  pendingActionResult: PendingActionResult | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: Record<string, DataResult>,\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    results,\n    pendingActionResult,\n    activeDeferreds,\n    false // This method is only called client side so we always want to bubble\n  );\n\n  // Process results from our revalidating fetchers\n  revalidatingFetchers.forEach((rf) => {\n    let { key, match, controller } = rf;\n    let result = fetcherResults[key];\n    invariant(result, \"Did not find corresponding fetcher result\");\n\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      return;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match?.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  });\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[],\n  errors: RouteData | null | undefined\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      } else {\n        // No-op - this is so we ignore existing data if we have a key in the\n        // incoming object with an undefined value, which is how we unset a prior\n        // loaderData if we encounter a loader error\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n\nfunction getActionDataForCommit(\n  pendingActionResult: PendingActionResult | undefined\n) {\n  if (!pendingActionResult) {\n    return {};\n  }\n  return isErrorResult(pendingActionResult[1])\n    ? {\n        // Clear out prior actionData on errors\n        actionData: {},\n      }\n    : {\n        actionData: {\n          [pendingActionResult[0]]: pendingActionResult[1].data,\n        },\n      };\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(routes: AgnosticDataRouteObject[]): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route =\n    routes.length === 1\n      ? routes[0]\n      : routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n          id: `__shim-error-route__`,\n        };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n  };\n}\n\nfunction getInternalRouterError(\n  status: number,\n  {\n    pathname,\n    routeId,\n    method,\n    type,\n    message,\n  }: {\n    pathname?: string;\n    routeId?: string;\n    method?: string;\n    type?: \"defer-action\" | \"invalid-body\" | \"route-discovery\";\n    message?: string;\n  } = {}\n) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (type === \"route-discovery\") {\n      errorMessage =\n        `Unable to match URL \"${pathname}\" - the \\`unstable_patchRoutesOnNavigation()\\` ` +\n        `function threw the following error:\\n${message}`;\n    } else if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method} request to \"${pathname}\" but ` +\n        `did not provide a \\`loader\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method.toUpperCase()} request to \"${pathname}\" but ` +\n        `did not provide an \\`action\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n\n  return new ErrorResponseImpl(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(\n  results: Record<string, DataResult>\n): { key: string; result: RedirectResult } | undefined {\n  let entries = Object.entries(results);\n  for (let i = entries.length - 1; i >= 0; i--) {\n    let [key, result] = entries[i];\n    if (isRedirectResult(result)) {\n      return { key, result };\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\n\nfunction isPromise<T = unknown>(val: unknown): val is Promise<T> {\n  return typeof val === \"object\" && val != null && \"then\" in val;\n}\n\nfunction isDataStrategyResult(result: unknown): result is DataStrategyResult {\n  return (\n    result != null &&\n    typeof result === \"object\" &&\n    \"type\" in result &&\n    \"result\" in result &&\n    (result.type === ResultType.data || result.type === ResultType.error)\n  );\n}\n\nfunction isRedirectDataStrategyResultResult(result: DataStrategyResult) {\n  return (\n    isResponse(result.result) && redirectStatusCodes.has(result.result.status)\n  );\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nexport function isDataWithResponseInit(\n  value: any\n): value is DataWithResponseInit<unknown> {\n  return (\n    typeof value === \"object\" &&\n    value != null &&\n    \"type\" in value &&\n    \"data\" in value &&\n    \"init\" in value &&\n    value.type === \"DataWithResponseInit\"\n  );\n}\n\nexport function isDeferredData(value: any): value is DeferredData {\n  let deferred: DeferredData = value;\n  return (\n    deferred &&\n    typeof deferred === \"object\" &&\n    typeof deferred.data === \"object\" &&\n    typeof deferred.subscribe === \"function\" &&\n    typeof deferred.cancel === \"function\" &&\n    typeof deferred.resolveData === \"function\"\n  );\n}\n\nfunction isResponse(value: any): value is Response {\n  return (\n    value != null &&\n    typeof value.status === \"number\" &&\n    typeof value.statusText === \"string\" &&\n    typeof value.headers === \"object\" &&\n    typeof value.body !== \"undefined\"\n  );\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isValidMethod(method: string): method is FormMethod | V7_FormMethod {\n  return validRequestMethods.has(method.toLowerCase() as FormMethod);\n}\n\nfunction isMutationMethod(\n  method: string\n): method is MutationFormMethod | V7_MutationFormMethod {\n  return validMutationMethods.has(method.toLowerCase() as MutationFormMethod);\n}\n\nasync function resolveNavigationDeferredResults(\n  matches: (AgnosticDataRouteMatch | null)[],\n  results: Record<string, DataResult>,\n  signal: AbortSignal,\n  currentMatches: AgnosticDataRouteMatch[],\n  currentLoaderData: RouteData\n) {\n  let entries = Object.entries(results);\n  for (let index = 0; index < entries.length; index++) {\n    let [routeId, result] = entries[index];\n    let match = matches.find((m) => m?.route.id === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match!.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && isRevalidatingLoader) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, false).then((result) => {\n        if (result) {\n          results[routeId] = result;\n        }\n      });\n    }\n  }\n}\n\nasync function resolveFetcherDeferredResults(\n  matches: (AgnosticDataRouteMatch | null)[],\n  results: Record<string, DataResult>,\n  revalidatingFetchers: RevalidatingFetcher[]\n) {\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let { key, routeId, controller } = revalidatingFetchers[index];\n    let result = results[key];\n    let match = matches.find((m) => m?.route.id === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    if (isDeferredResult(result)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      invariant(\n        controller,\n        \"Expected an AbortController for revalidating fetcher deferred result\"\n      );\n      await resolveDeferredData(result, controller.signal, true).then(\n        (result) => {\n          if (result) {\n            results[key] = result;\n          }\n        }\n      );\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n\nfunction getSubmissionFromNavigation(\n  navigation: Navigation\n): Submission | undefined {\n  let { formMethod, formAction, formEncType, text, formData, json } =\n    navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text,\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined,\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined,\n    };\n  }\n}\n\nfunction getLoadingNavigation(\n  location: Location,\n  submission?: Submission\n): NavigationStates[\"Loading\"] {\n  if (submission) {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n    };\n    return navigation;\n  } else {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n    };\n    return navigation;\n  }\n}\n\nfunction getSubmittingNavigation(\n  location: Location,\n  submission: Submission\n): NavigationStates[\"Submitting\"] {\n  let navigation: NavigationStates[\"Submitting\"] = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n  };\n  return navigation;\n}\n\nfunction getLoadingFetcher(\n  submission?: Submission,\n  data?: Fetcher[\"data\"]\n): FetcherStates[\"Loading\"] {\n  if (submission) {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data,\n    };\n    return fetcher;\n  } else {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data,\n    };\n    return fetcher;\n  }\n}\n\nfunction getSubmittingFetcher(\n  submission: Submission,\n  existingFetcher?: Fetcher\n): FetcherStates[\"Submitting\"] {\n  let fetcher: FetcherStates[\"Submitting\"] = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined,\n  };\n  return fetcher;\n}\n\nfunction getDoneFetcher(data: Fetcher[\"data\"]): FetcherStates[\"Idle\"] {\n  let fetcher: FetcherStates[\"Idle\"] = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data,\n  };\n  return fetcher;\n}\n\nfunction restoreAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(\n      TRANSITIONS_STORAGE_KEY\n    );\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n    // no-op, use default empty object\n  }\n}\n\nfunction persistAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  if (transitions.size > 0) {\n    let json: Record<string, string[]> = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(\n        TRANSITIONS_STORAGE_KEY,\n        JSON.stringify(json)\n      );\n    } catch (error) {\n      warning(\n        false,\n        `Failed to save applied view transitions in sessionStorage (${error}).`\n      );\n    }\n  }\n}\n//#endregion\n", "import * as React from \"react\";\nimport type {\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  AgnosticRouteMatch,\n  History,\n  LazyRouteFunction,\n  Location,\n  Action as NavigationType,\n  RelativeRoutingType,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject\n  // Omit `future` since those can be pulled from the `router`\n  // `NavigationContext` needs future since it doesn't have a `router` in all cases\n  extends Omit<NavigationContextObject, \"future\"> {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  unstable_flushSync?: boolean;\n  unstable_viewTransition?: boolean;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n  future: {\n    v7_relativeSplatPath: boolean;\n  };\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n  isDataRoute: boolean;\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n  isDataRoute: false,\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  <PERSON>er,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RevalidationState,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  IDLE_BLOCKER,\n  Action as NavigationType,\n  UNSAFE_convertRouteMatchToUiMatch as convertRouteMatchToUiMatch,\n  UNSAFE_decodePath as decodePath,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  DataRouteMatch,\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, decodePath(pathname)),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nconst navigateEffectWarning =\n  `You should call navigate() in a React.useEffect(), not when ` +\n  `your component is first rendered.`;\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(\n  cb: Parameters<typeof React.useLayoutEffect>[0]\n) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  let { isDataRoute } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\n\nfunction useNavigateUnstable(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let { basename, future, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our history listener yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history (but only if we're not in a data router,\n      // otherwise it'll prepend the basename inside of the router).\n      // If this is a root navigation, then we navigate to the raw basename\n      // which allows the basename to have full control over the presence of a\n      // trailing slash on root links\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext,\n    ]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { future } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nexport function useRoutesImpl(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string,\n  dataRouterState?: RemixRouter[\"state\"],\n  future?: RemixRouter[\"future\"]\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined ||\n        matches[matches.length - 1].route.Component !== undefined ||\n        matches[matches.length - 1].route.lazy !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" ` +\n        `does not have an element or Component. This means it will render an <Outlet /> with a ` +\n        `null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterState,\n    future\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own <code style={codeStyles}>ErrorBoundary</code> or{\" \"}\n          <code style={codeStyles}>errorElement</code> prop on your route.\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\nconst defaultErrorElement = <DefaultErrorComponent />;\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (\n      state.location !== props.location ||\n      (state.revalidation !== \"idle\" && props.revalidation === \"idle\")\n    ) {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error !== undefined ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    (match.route.errorElement || match.route.ErrorBoundary)\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState: RemixRouter[\"state\"] | null = null,\n  future: RemixRouter[\"future\"] | null = null\n): React.ReactElement | null {\n  if (matches == null) {\n    if (!dataRouterState) {\n      return null;\n    }\n\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else if (\n      future?.v7_partialHydration &&\n      parentMatches.length === 0 &&\n      !dataRouterState.initialized &&\n      dataRouterState.matches.length > 0\n    ) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id] !== undefined\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n\n      if (match.route.id) {\n        let { loaderData, errors } = dataRouterState;\n        let needsToRunLoader =\n          match.route.loader &&\n          loaderData[match.route.id] === undefined &&\n          (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error: any;\n    let shouldRenderHydrateFallback = false;\n    let errorElement: React.ReactNode | null = null;\n    let hydrateFallbackElement: React.ReactNode | null = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\n            \"route-fallback\",\n            false,\n            \"No `HydrateFallback` element provided to render during initial hydration\"\n          );\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children: React.ReactNode;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = <match.route.Component />;\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return (\n        <RenderedRoute\n          match={match}\n          routeContext={{\n            outlet,\n            matches,\n            isDataRoute: dataRouterState != null,\n          }}\n          children={children}\n        />\n      );\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState &&\n      (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        revalidation={dataRouterState.revalidation}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches, isDataRoute: true }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n}\n\nenum DataRouterStateHook {\n  UseBlocker = \"useBlocker\",\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n  UseRouteId = \"useRouteId\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nexport function useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(\n    () => ({\n      revalidate: dataRouterContext.router.revalidate,\n      state: state.revalidation,\n    }),\n    [dataRouterContext.router.revalidate, state.revalidation]\n  );\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches(): UIMatch[] {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () => matches.map((m) => convertRouteMatchToUiMatch(m, loaderData)),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router, basename } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (arg) => {\n      if (typeof shouldBlock !== \"function\") {\n        return !!shouldBlock;\n      }\n      if (basename === \"/\") {\n        return shouldBlock(arg);\n      }\n\n      // If they provided us a function and we've got an active basename, strip\n      // it from the locations we expose to the user to match the behavior of\n      // useLocation\n      let { currentLocation, nextLocation, historyAction } = arg;\n      return shouldBlock({\n        currentLocation: {\n          ...currentLocation,\n          pathname:\n            stripBasename(currentLocation.pathname, basename) ||\n            currentLocation.pathname,\n        },\n        nextLocation: {\n          ...nextLocation,\n          pathname:\n            stripBasename(nextLocation.pathname, basename) ||\n            nextLocation.pathname,\n        },\n        historyAction,\n      });\n    },\n    [basename, shouldBlock]\n  );\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey)\n    ? state.blockers.get(blockerKey)!\n    : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable(): NavigateFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our router subscriber yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n\n  return navigate;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import type {\n  InitialEntry,\n  LazyRouteFunction,\n  Location,\n  MemoryHistory,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RouterState,\n  RouterSubscriber,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\nimport * as React from \"react\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./context\";\nimport {\n  _renderMatches,\n  useAsyncValue,\n  useInRouterContext,\n  useLocation,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  useRoutesImpl,\n} from \"./hooks\";\n\nexport interface FutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_startTransition: boolean;\n}\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n  // Only accept future flags relevant to rendering behavior\n  // routing flags should be accessed via router.future\n  future?: Partial<Pick<FutureConfig, \"v7_startTransition\">>;\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let { v7_startTransition } = future || {};\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (newState: RouterState) => {\n      if (v7_startTransition && startTransitionImpl) {\n        startTransitionImpl(() => setStateImpl(newState));\n      } else {\n        setStateImpl(newState);\n      }\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={basename}\n            location={state.location}\n            navigationType={state.historyAction}\n            navigator={navigator}\n            future={{\n              v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n            }}\n          >\n            {state.initialized || router.future.v7_partialHydration ? (\n              <DataRoutes\n                routes={router.routes}\n                future={router.future}\n                state={state}\n              />\n            ) : (\n              fallbackElement\n            )}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  future?: Partial<FutureConfig>;\n}\n\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n  future,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  let { future, static: isStatic } = React.useContext(NavigationContext);\n\n  warning(\n    !isStatic,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(\n    to,\n    getResolveToMatches(matches, future.v7_relativeSplatPath),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n\n  React.useEffect(\n    () => navigate(JSON.parse(jsonPath), { replace, state, relative }),\n    [navigate, jsonPath, relative, replace, state]\n  );\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n  future?: Partial<Pick<FutureConfig, \"v7_relativeSplatPath\">>;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n  future,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({\n      basename,\n      navigator,\n      static: staticProp,\n      future: {\n        v7_relativeSplatPath: false,\n        ...future,\n      },\n    }),\n    [basename, future, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key,\n      },\n      navigationType,\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (locationContext == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider children={children} value={locationContext} />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        \"_error\" in promise\n          ? AwaitRenderStatus.error\n          : \"_data\" in promise\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    let treePath = [...parentPath, index];\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary:\n        element.props.ErrorBoundary != null ||\n        element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "import * as React from \"react\";\nimport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  unstable_DataStrategyFunction,\n  unstable_DataStrategyFunctionArgs,\n  unstable_DataStrategyMatch,\n  unstable_DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  HydrationState,\n  InitialEntry,\n  JsonFunction,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  RedirectFunction,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  FutureConfig as RouterFutureConfig,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  unstable_AgnosticPatchRoutesOnNavigationFunction,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  resolvePath,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  FutureConfig,\n  IndexRouteProps,\n  LayoutRouteProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  PathRouteProps,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n} from \"./lib/components\";\nimport {\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createRoutesFromChildren,\n  renderMatches,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  NavigateOptions,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteId,\n  useRouteLoaderData,\n  useRoutes,\n  useRoutesImpl,\n} from \"./lib/hooks\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  DataRouteMatch,\n  DataRouteObject,\n  unstable_DataStrategyFunction,\n  unstable_DataStrategyFunctionArgs,\n  unstable_DataStrategyMatch,\n  unstable_DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  Pathname,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  Blocker,\n  BlockerFunction,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useBlocker,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nfunction mapRouteProperties(route: RouteObject) {\n  let updates: Partial<RouteObject> & { hasErrorBoundary: boolean } = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null,\n  };\n\n  if (route.Component) {\n    if (__DEV__) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - \" +\n            \"`Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React.createElement(route.Component),\n      Component: undefined,\n    });\n  }\n\n  if (route.HydrateFallback) {\n    if (__DEV__) {\n      if (route.hydrateFallbackElement) {\n        warning(\n          false,\n          \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" +\n            \"`HydrateFallback` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined,\n    });\n  }\n\n  if (route.ErrorBoundary) {\n    if (__DEV__) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" +\n            \"`ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined,\n    });\n  }\n\n  return updates;\n}\n\nexport interface unstable_PatchRoutesOnNavigationFunction\n  extends unstable_AgnosticPatchRoutesOnNavigationFunction<RouteMatch> {}\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n    unstable_dataStrategy?: unstable_DataStrategyFunction;\n    unstable_patchRoutesOnNavigation?: unstable_PatchRoutesOnNavigationFunction;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    mapRouteProperties,\n    unstable_dataStrategy: opts?.unstable_dataStrategy,\n    unstable_patchRoutesOnNavigation: opts?.unstable_patchRoutesOnNavigation,\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  LocationContext as UNSAFE_LocationContext,\n  NavigationContext as UNSAFE_NavigationContext,\n  RouteContext as UNSAFE_RouteContext,\n  mapRouteProperties as UNSAFE_mapRouteProperties,\n  useRouteId as UNSAFE_useRouteId,\n  useRoutesImpl as UNSAFE_useRoutesImpl,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAOYA;CAAZ,SAAYA,SAAM;AAQhBA,EAAAA,QAAA,KAAA,IAAA;AAOAA,EAAAA,QAAA,MAAA,IAAA;AAMAA,EAAAA,QAAA,SAAA,IAAA;AACF,GAtBYA,WAAAA,SAsBX,CAAA,EAAA;AAqKD,IAAMC,oBAAoB;AAmCV,SAAAC,oBACdC,SAAkC;AAAA,MAAlCA,YAAA,QAAA;AAAAA,cAAgC,CAAA;EAAE;AAElC,MAAI;IAAEC,iBAAiB,CAAC,GAAG;IAAGC;IAAcC,WAAW;EAAO,IAAGH;AACjE,MAAII;AACJA,YAAUH,eAAeI,IAAI,CAACC,OAAOC,WACnCC,qBACEF,OACA,OAAOA,UAAU,WAAW,OAAOA,MAAMG,OACzCF,WAAU,IAAI,YAAYG,MAAS,CACpC;AAEH,MAAIH,QAAQI,WACVT,gBAAgB,OAAOE,QAAQQ,SAAS,IAAIV,YAAY;AAE1D,MAAIW,SAAShB,OAAOiB;AACpB,MAAIC,WAA4B;AAEhC,WAASJ,WAAWK,GAAS;AAC3B,WAAOC,KAAKC,IAAID,KAAKE,IAAIH,GAAG,CAAC,GAAGZ,QAAQQ,SAAS,CAAC;EACpD;AACA,WAASQ,qBAAkB;AACzB,WAAOhB,QAAQG,KAAK;EACtB;AACA,WAASC,qBACPa,IACAZ,OACAa,KAAY;AAAA,QADZb,UAAa,QAAA;AAAbA,cAAa;IAAI;AAGjB,QAAIc,WAAWC,eACbpB,UAAUgB,mBAAkB,EAAGK,WAAW,KAC1CJ,IACAZ,OACAa,GAAG;AAELI,YACEH,SAASE,SAASE,OAAO,CAAC,MAAM,KAAG,6DACwBC,KAAKC,UAC9DR,EAAE,CACD;AAEL,WAAOE;EACT;AAEA,WAASO,WAAWT,IAAM;AACxB,WAAO,OAAOA,OAAO,WAAWA,KAAKU,WAAWV,EAAE;EACpD;AAEA,MAAIW,UAAyB;IAC3B,IAAIzB,QAAK;AACP,aAAOA;;IAET,IAAIM,SAAM;AACR,aAAOA;;IAET,IAAIU,WAAQ;AACV,aAAOH,mBAAkB;;IAE3BU;IACAG,UAAUZ,IAAE;AACV,aAAO,IAAIa,IAAIJ,WAAWT,EAAE,GAAG,kBAAkB;;IAEnDc,eAAed,IAAM;AACnB,UAAIe,OAAO,OAAOf,OAAO,WAAWgB,UAAUhB,EAAE,IAAIA;AACpD,aAAO;QACLI,UAAUW,KAAKX,YAAY;QAC3Ba,QAAQF,KAAKE,UAAU;QACvBC,MAAMH,KAAKG,QAAQ;;;IAGvBC,KAAKnB,IAAIZ,OAAK;AACZI,eAAShB,OAAO4C;AAChB,UAAIC,eAAelC,qBAAqBa,IAAIZ,KAAK;AACjDF,eAAS;AACTH,cAAQuC,OAAOpC,OAAOH,QAAQQ,QAAQ8B,YAAY;AAClD,UAAIvC,YAAYY,UAAU;AACxBA,iBAAS;UAAEF;UAAQU,UAAUmB;UAAcE,OAAO;QAAC,CAAE;MACtD;;IAEHC,QAAQxB,IAAIZ,OAAK;AACfI,eAAShB,OAAOiD;AAChB,UAAIJ,eAAelC,qBAAqBa,IAAIZ,KAAK;AACjDL,cAAQG,KAAK,IAAImC;AACjB,UAAIvC,YAAYY,UAAU;AACxBA,iBAAS;UAAEF;UAAQU,UAAUmB;UAAcE,OAAO;QAAC,CAAE;MACtD;;IAEHG,GAAGH,OAAK;AACN/B,eAAShB,OAAOiB;AAChB,UAAIkC,YAAYrC,WAAWJ,QAAQqC,KAAK;AACxC,UAAIF,eAAetC,QAAQ4C,SAAS;AACpCzC,cAAQyC;AACR,UAAIjC,UAAU;AACZA,iBAAS;UAAEF;UAAQU,UAAUmB;UAAcE;QAAO,CAAA;MACnD;;IAEHK,OAAOC,IAAY;AACjBnC,iBAAWmC;AACX,aAAO,MAAK;AACVnC,mBAAW;;IAEf;;AAGF,SAAOiB;AACT;AAyBgB,SAAAmB,qBACdnD,SAAmC;AAAA,MAAnCA,YAAA,QAAA;AAAAA,cAAiC,CAAA;EAAE;AAEnC,WAASoD,sBACPC,SACAC,eAAgC;AAEhC,QAAI;MAAE7B;MAAUa;MAAQC;QAASc,QAAO9B;AACxC,WAAOC;MACL;MACA;QAAEC;QAAUa;QAAQC;;;MAEnBe,cAAc7C,SAAS6C,cAAc7C,MAAM8C,OAAQ;MACnDD,cAAc7C,SAAS6C,cAAc7C,MAAMa,OAAQ;IAAS;EAEjE;AAEA,WAASkC,kBAAkBH,SAAgBhC,IAAM;AAC/C,WAAO,OAAOA,OAAO,WAAWA,KAAKU,WAAWV,EAAE;EACpD;AAEA,SAAOoC,mBACLL,uBACAI,mBACA,MACAxD,OAAO;AAEX;AA8BgB,SAAA0D,kBACd1D,SAAgC;AAAA,MAAhCA,YAAA,QAAA;AAAAA,cAA8B,CAAA;EAAE;AAEhC,WAAS2D,mBACPN,SACAC,eAAgC;AAEhC,QAAI;MACF7B,WAAW;MACXa,SAAS;MACTC,OAAO;IAAE,IACPF,UAAUgB,QAAO9B,SAASgB,KAAKqB,OAAO,CAAC,CAAC;AAQ5C,QAAI,CAACnC,SAASoC,WAAW,GAAG,KAAK,CAACpC,SAASoC,WAAW,GAAG,GAAG;AAC1DpC,iBAAW,MAAMA;IAClB;AAED,WAAOD;MACL;MACA;QAAEC;QAAUa;QAAQC;;;MAEnBe,cAAc7C,SAAS6C,cAAc7C,MAAM8C,OAAQ;MACnDD,cAAc7C,SAAS6C,cAAc7C,MAAMa,OAAQ;IAAS;EAEjE;AAEA,WAASwC,eAAeT,SAAgBhC,IAAM;AAC5C,QAAI0C,OAAOV,QAAOW,SAASC,cAAc,MAAM;AAC/C,QAAIC,OAAO;AAEX,QAAIH,QAAQA,KAAKI,aAAa,MAAM,GAAG;AACrC,UAAIC,MAAMf,QAAO9B,SAAS2C;AAC1B,UAAIG,YAAYD,IAAIE,QAAQ,GAAG;AAC/BJ,aAAOG,cAAc,KAAKD,MAAMA,IAAIG,MAAM,GAAGF,SAAS;IACvD;AAED,WAAOH,OAAO,OAAO,OAAO7C,OAAO,WAAWA,KAAKU,WAAWV,EAAE;EAClE;AAEA,WAASmD,qBAAqBjD,UAAoBF,IAAM;AACtDK,YACEH,SAASE,SAASE,OAAO,CAAC,MAAM,KAAG,+DAC0BC,KAAKC,UAChER,EAAE,IACH,GAAG;EAER;AAEA,SAAOoC,mBACLE,oBACAG,gBACAU,sBACAxE,OAAO;AAEX;AAegB,SAAAyE,UAAUC,OAAYC,SAAgB;AACpD,MAAID,UAAU,SAASA,UAAU,QAAQ,OAAOA,UAAU,aAAa;AACrE,UAAM,IAAIE,MAAMD,OAAO;EACxB;AACH;AAEgB,SAAAjD,QAAQmD,MAAWF,SAAe;AAChD,MAAI,CAACE,MAAM;AAET,QAAI,OAAOC,YAAY,YAAaA,SAAQC,KAAKJ,OAAO;AAExD,QAAI;AAMF,YAAM,IAAIC,MAAMD,OAAO;IAExB,SAAQK,GAAG;IAAA;EACb;AACH;AAEA,SAASC,YAAS;AAChB,SAAOhE,KAAKiE,OAAM,EAAGC,SAAS,EAAE,EAAEvB,OAAO,GAAG,CAAC;AAC/C;AAKA,SAASwB,gBAAgB7D,UAAoBhB,OAAa;AACxD,SAAO;IACLgD,KAAKhC,SAASd;IACda,KAAKC,SAASD;IACd+D,KAAK9E;;AAET;AAKM,SAAUiB,eACd8D,SACAjE,IACAZ,OACAa,KAAY;AAAA,MADZb,UAAA,QAAA;AAAAA,YAAa;EAAI;AAGjB,MAAIc,WAAQgE,SAAA;IACV9D,UAAU,OAAO6D,YAAY,WAAWA,UAAUA,QAAQ7D;IAC1Da,QAAQ;IACRC,MAAM;KACF,OAAOlB,OAAO,WAAWgB,UAAUhB,EAAE,IAAIA,IAAE;IAC/CZ;;;;;IAKAa,KAAMD,MAAOA,GAAgBC,OAAQA,OAAO2D,UAAS;GACtD;AACD,SAAO1D;AACT;AAKgB,SAAAQ,WAAUyD,MAIV;AAAA,MAJW;IACzB/D,WAAW;IACXa,SAAS;IACTC,OAAO;EACO,IAAAiD;AACd,MAAIlD,UAAUA,WAAW,IACvBb,aAAYa,OAAOX,OAAO,CAAC,MAAM,MAAMW,SAAS,MAAMA;AACxD,MAAIC,QAAQA,SAAS,IACnBd,aAAYc,KAAKZ,OAAO,CAAC,MAAM,MAAMY,OAAO,MAAMA;AACpD,SAAOd;AACT;AAKM,SAAUY,UAAUD,MAAY;AACpC,MAAIqD,aAA4B,CAAA;AAEhC,MAAIrD,MAAM;AACR,QAAIiC,YAAYjC,KAAKkC,QAAQ,GAAG;AAChC,QAAID,aAAa,GAAG;AAClBoB,iBAAWlD,OAAOH,KAAKwB,OAAOS,SAAS;AACvCjC,aAAOA,KAAKwB,OAAO,GAAGS,SAAS;IAChC;AAED,QAAIqB,cAActD,KAAKkC,QAAQ,GAAG;AAClC,QAAIoB,eAAe,GAAG;AACpBD,iBAAWnD,SAASF,KAAKwB,OAAO8B,WAAW;AAC3CtD,aAAOA,KAAKwB,OAAO,GAAG8B,WAAW;IAClC;AAED,QAAItD,MAAM;AACRqD,iBAAWhE,WAAWW;IACvB;EACF;AAED,SAAOqD;AACT;AASA,SAAShC,mBACPkC,aACA7D,YACA8D,kBACA5F,SAA+B;AAAA,MAA/BA,YAAA,QAAA;AAAAA,cAA6B,CAAA;EAAE;AAE/B,MAAI;IAAEqD,QAAAA,UAASW,SAAS6B;IAAc1F,WAAW;EAAO,IAAGH;AAC3D,MAAIsD,gBAAgBD,QAAOrB;AAC3B,MAAInB,SAAShB,OAAOiB;AACpB,MAAIC,WAA4B;AAEhC,MAAIR,QAAQuF,SAAQ;AAIpB,MAAIvF,SAAS,MAAM;AACjBA,YAAQ;AACR+C,kBAAcyC,aAAYR,SAAMjC,CAAAA,GAAAA,cAAc7C,OAAK;MAAE4E,KAAK9E;IAAK,CAAA,GAAI,EAAE;EACtE;AAED,WAASuF,WAAQ;AACf,QAAIrF,QAAQ6C,cAAc7C,SAAS;MAAE4E,KAAK;;AAC1C,WAAO5E,MAAM4E;EACf;AAEA,WAASW,YAAS;AAChBnF,aAAShB,OAAOiB;AAChB,QAAIkC,YAAY8C,SAAQ;AACxB,QAAIlD,QAAQI,aAAa,OAAO,OAAOA,YAAYzC;AACnDA,YAAQyC;AACR,QAAIjC,UAAU;AACZA,eAAS;QAAEF;QAAQU,UAAUS,QAAQT;QAAUqB;MAAK,CAAE;IACvD;EACH;AAEA,WAASJ,KAAKnB,IAAQZ,OAAW;AAC/BI,aAAShB,OAAO4C;AAChB,QAAIlB,WAAWC,eAAeQ,QAAQT,UAAUF,IAAIZ,KAAK;AACzD,QAAImF,iBAAkBA,kBAAiBrE,UAAUF,EAAE;AAEnDd,YAAQuF,SAAQ,IAAK;AACrB,QAAIG,eAAeb,gBAAgB7D,UAAUhB,KAAK;AAClD,QAAI6D,MAAMpC,QAAQF,WAAWP,QAAQ;AAGrC,QAAI;AACF+B,oBAAc4C,UAAUD,cAAc,IAAI7B,GAAG;aACtC+B,OAAO;AAKd,UAAIA,iBAAiBC,gBAAgBD,MAAME,SAAS,kBAAkB;AACpE,cAAMF;MACP;AAGD9C,MAAAA,QAAO9B,SAAS+E,OAAOlC,GAAG;IAC3B;AAED,QAAIjE,YAAYY,UAAU;AACxBA,eAAS;QAAEF;QAAQU,UAAUS,QAAQT;QAAUqB,OAAO;MAAC,CAAE;IAC1D;EACH;AAEA,WAASC,SAAQxB,IAAQZ,OAAW;AAClCI,aAAShB,OAAOiD;AAChB,QAAIvB,WAAWC,eAAeQ,QAAQT,UAAUF,IAAIZ,KAAK;AACzD,QAAImF,iBAAkBA,kBAAiBrE,UAAUF,EAAE;AAEnDd,YAAQuF,SAAQ;AAChB,QAAIG,eAAeb,gBAAgB7D,UAAUhB,KAAK;AAClD,QAAI6D,MAAMpC,QAAQF,WAAWP,QAAQ;AACrC+B,kBAAcyC,aAAaE,cAAc,IAAI7B,GAAG;AAEhD,QAAIjE,YAAYY,UAAU;AACxBA,eAAS;QAAEF;QAAQU,UAAUS,QAAQT;QAAUqB,OAAO;MAAC,CAAE;IAC1D;EACH;AAEA,WAASX,UAAUZ,IAAM;AAIvB,QAAI0C,OACFV,QAAO9B,SAASgF,WAAW,SACvBlD,QAAO9B,SAASgF,SAChBlD,QAAO9B,SAAS2C;AAEtB,QAAIA,OAAO,OAAO7C,OAAO,WAAWA,KAAKU,WAAWV,EAAE;AAItD6C,WAAOA,KAAKrB,QAAQ,MAAM,KAAK;AAC/B4B,cACEV,MACsEG,wEAAAA,IAAM;AAE9E,WAAO,IAAIhC,IAAIgC,MAAMH,IAAI;EAC3B;AAEA,MAAI/B,UAAmB;IACrB,IAAInB,SAAM;AACR,aAAOA;;IAET,IAAIU,WAAQ;AACV,aAAOoE,YAAYtC,SAAQC,aAAa;;IAE1CL,OAAOC,IAAY;AACjB,UAAInC,UAAU;AACZ,cAAM,IAAI6D,MAAM,4CAA4C;MAC7D;AACDvB,MAAAA,QAAOmD,iBAAiB1G,mBAAmBkG,SAAS;AACpDjF,iBAAWmC;AAEX,aAAO,MAAK;AACVG,QAAAA,QAAOoD,oBAAoB3G,mBAAmBkG,SAAS;AACvDjF,mBAAW;;;IAGfe,WAAWT,IAAE;AACX,aAAOS,WAAWuB,SAAQhC,EAAE;;IAE9BY;IACAE,eAAed,IAAE;AAEf,UAAI+C,MAAMnC,UAAUZ,EAAE;AACtB,aAAO;QACLI,UAAU2C,IAAI3C;QACda,QAAQ8B,IAAI9B;QACZC,MAAM6B,IAAI7B;;;IAGdC;IACAK,SAAAA;IACAE,GAAG/B,GAAC;AACF,aAAOsC,cAAcP,GAAG/B,CAAC;IAC3B;;AAGF,SAAOgB;AACT;AC7tBA,IAAY0E;CAAZ,SAAYA,aAAU;AACpBA,EAAAA,YAAA,MAAA,IAAA;AACAA,EAAAA,YAAA,UAAA,IAAA;AACAA,EAAAA,YAAA,UAAA,IAAA;AACAA,EAAAA,YAAA,OAAA,IAAA;AACF,GALYA,eAAAA,aAKX,CAAA,EAAA;AAmRM,IAAMC,qBAAqB,oBAAIC,IAAuB,CAC3D,QACA,iBACA,QACA,MACA,SACA,UAAU,CACX;AAoJD,SAASC,aACPC,OAA0B;AAE1B,SAAOA,MAAMvG,UAAU;AACzB;AAIM,SAAUwG,0BACdC,QACAC,qBACAC,YACAC,UAA4B;AAAA,MAD5BD,eAAuB,QAAA;AAAvBA,iBAAuB,CAAA;EAAE;AAAA,MACzBC,aAAA,QAAA;AAAAA,eAA0B,CAAA;EAAE;AAE5B,SAAOH,OAAO3G,IAAI,CAACyG,OAAOvG,UAAS;AACjC,QAAI6G,WAAW,CAAC,GAAGF,YAAYG,OAAO9G,KAAK,CAAC;AAC5C,QAAI+G,KAAK,OAAOR,MAAMQ,OAAO,WAAWR,MAAMQ,KAAKF,SAASG,KAAK,GAAG;AACpE9C,cACEqC,MAAMvG,UAAU,QAAQ,CAACuG,MAAMU,UAAQ,2CACI;AAE7C/C,cACE,CAAC0C,SAASG,EAAE,GACZ,uCAAqCA,KACnC,kEAAwD;AAG5D,QAAIT,aAAaC,KAAK,GAAG;AACvB,UAAIW,aAAUlC,SAAA,CAAA,GACTuB,OACAG,oBAAmBH,KAAK,GAAC;QAC5BQ;OACD;AACDH,eAASG,EAAE,IAAIG;AACf,aAAOA;IACR,OAAM;AACL,UAAIC,oBAAiBnC,SAAA,CAAA,GAChBuB,OACAG,oBAAmBH,KAAK,GAAC;QAC5BQ;QACAE,UAAU9G;OACX;AACDyG,eAASG,EAAE,IAAII;AAEf,UAAIZ,MAAMU,UAAU;AAClBE,0BAAkBF,WAAWT,0BAC3BD,MAAMU,UACNP,qBACAG,UACAD,QAAQ;MAEX;AAED,aAAOO;IACR;EACH,CAAC;AACH;AAOM,SAAUC,YAGdX,QACAY,aACAC,UAAc;AAAA,MAAdA,aAAQ,QAAA;AAARA,eAAW;EAAG;AAEd,SAAOC,gBAAgBd,QAAQY,aAAaC,UAAU,KAAK;AAC7D;AAEM,SAAUC,gBAGdd,QACAY,aACAC,UACAE,cAAqB;AAErB,MAAIxG,WACF,OAAOqG,gBAAgB,WAAWvF,UAAUuF,WAAW,IAAIA;AAE7D,MAAInG,WAAWuG,cAAczG,SAASE,YAAY,KAAKoG,QAAQ;AAE/D,MAAIpG,YAAY,MAAM;AACpB,WAAO;EACR;AAED,MAAIwG,WAAWC,cAAclB,MAAM;AACnCmB,oBAAkBF,QAAQ;AAE1B,MAAIG,UAAU;AACd,WAASC,IAAI,GAAGD,WAAW,QAAQC,IAAIJ,SAASrH,QAAQ,EAAEyH,GAAG;AAO3D,QAAIC,UAAUC,WAAW9G,QAAQ;AACjC2G,cAAUI,iBACRP,SAASI,CAAC,GACVC,SACAP,YAAY;EAEf;AAED,SAAOK;AACT;AAUgB,SAAAK,2BACdC,OACAC,YAAqB;AAErB,MAAI;IAAE7B;IAAOrF;IAAUmH;EAAM,IAAKF;AAClC,SAAO;IACLpB,IAAIR,MAAMQ;IACV7F;IACAmH;IACAC,MAAMF,WAAW7B,MAAMQ,EAAE;IACzBwB,QAAQhC,MAAMgC;;AAElB;AAmBA,SAASZ,cAGPlB,QACAiB,UACAc,aACA7B,YAAe;AAAA,MAFfe,aAA2C,QAAA;AAA3CA,eAA2C,CAAA;EAAE;AAAA,MAC7Cc,gBAAA,QAAA;AAAAA,kBAA4C,CAAA;EAAE;AAAA,MAC9C7B,eAAU,QAAA;AAAVA,iBAAa;EAAE;AAEf,MAAI8B,eAAeA,CACjBlC,OACAvG,OACA0I,iBACE;AACF,QAAIC,OAAmC;MACrCD,cACEA,iBAAiBvI,SAAYoG,MAAM1E,QAAQ,KAAK6G;MAClDE,eAAerC,MAAMqC,kBAAkB;MACvCC,eAAe7I;MACfuG;;AAGF,QAAIoC,KAAKD,aAAapF,WAAW,GAAG,GAAG;AACrCY,gBACEyE,KAAKD,aAAapF,WAAWqD,UAAU,GACvC,0BAAwBgC,KAAKD,eAAY,0BAAA,MACnC/B,aAAU,mDAA+C,6DACA;AAGjEgC,WAAKD,eAAeC,KAAKD,aAAa1E,MAAM2C,WAAWtG,MAAM;IAC9D;AAED,QAAIwB,OAAOiH,UAAU,CAACnC,YAAYgC,KAAKD,YAAY,CAAC;AACpD,QAAIK,aAAaP,YAAYQ,OAAOL,IAAI;AAKxC,QAAIpC,MAAMU,YAAYV,MAAMU,SAAS5G,SAAS,GAAG;AAC/C6D;;;QAGEqC,MAAMvG,UAAU;QAChB,6DACuC6B,uCAAAA,OAAI;MAAI;AAEjD8F,oBAAcpB,MAAMU,UAAUS,UAAUqB,YAAYlH,IAAI;IACzD;AAID,QAAI0E,MAAM1E,QAAQ,QAAQ,CAAC0E,MAAMvG,OAAO;AACtC;IACD;AAED0H,aAASzF,KAAK;MACZJ;MACAoH,OAAOC,aAAarH,MAAM0E,MAAMvG,KAAK;MACrC+I;IACD,CAAA;;AAEHtC,SAAO0C,QAAQ,CAAC5C,OAAOvG,UAAS;AAAA,QAAAoJ;AAE9B,QAAI7C,MAAM1E,SAAS,MAAM,GAAAuH,cAAC7C,MAAM1E,SAAI,QAAVuH,YAAYC,SAAS,GAAG,IAAG;AACnDZ,mBAAalC,OAAOvG,KAAK;IAC1B,OAAM;AACL,eAASsJ,YAAYC,wBAAwBhD,MAAM1E,IAAI,GAAG;AACxD4G,qBAAalC,OAAOvG,OAAOsJ,QAAQ;MACpC;IACF;EACH,CAAC;AAED,SAAO5B;AACT;AAgBA,SAAS6B,wBAAwB1H,MAAY;AAC3C,MAAI2H,WAAW3H,KAAK4H,MAAM,GAAG;AAC7B,MAAID,SAASnJ,WAAW,EAAG,QAAO,CAAA;AAElC,MAAI,CAACqJ,OAAO,GAAGC,IAAI,IAAIH;AAGvB,MAAII,aAAaF,MAAMG,SAAS,GAAG;AAEnC,MAAIC,WAAWJ,MAAMpH,QAAQ,OAAO,EAAE;AAEtC,MAAIqH,KAAKtJ,WAAW,GAAG;AAGrB,WAAOuJ,aAAa,CAACE,UAAU,EAAE,IAAI,CAACA,QAAQ;EAC/C;AAED,MAAIC,eAAeR,wBAAwBI,KAAK3C,KAAK,GAAG,CAAC;AAEzD,MAAIgD,SAAmB,CAAA;AASvBA,SAAO/H,KACL,GAAG8H,aAAajK,IAAKmK,aACnBA,YAAY,KAAKH,WAAW,CAACA,UAAUG,OAAO,EAAEjD,KAAK,GAAG,CAAC,CAC1D;AAIH,MAAI4C,YAAY;AACdI,WAAO/H,KAAK,GAAG8H,YAAY;EAC5B;AAGD,SAAOC,OAAOlK,IAAKwJ,cACjBzH,KAAKyB,WAAW,GAAG,KAAKgG,aAAa,KAAK,MAAMA,QAAQ;AAE5D;AAEA,SAAS1B,kBAAkBF,UAAuB;AAChDA,WAASwC,KAAK,CAACC,GAAGC,MAChBD,EAAElB,UAAUmB,EAAEnB,QACVmB,EAAEnB,QAAQkB,EAAElB,QACZoB,eACEF,EAAEpB,WAAWjJ,IAAK6I,UAASA,KAAKE,aAAa,GAC7CuB,EAAErB,WAAWjJ,IAAK6I,UAASA,KAAKE,aAAa,CAAC,CAC/C;AAET;AAEA,IAAMyB,UAAU;AAChB,IAAMC,sBAAsB;AAC5B,IAAMC,kBAAkB;AACxB,IAAMC,oBAAoB;AAC1B,IAAMC,qBAAqB;AAC3B,IAAMC,eAAe;AACrB,IAAMC,UAAWC,OAAcA,MAAM;AAErC,SAAS3B,aAAarH,MAAc7B,OAA0B;AAC5D,MAAIwJ,WAAW3H,KAAK4H,MAAM,GAAG;AAC7B,MAAIqB,eAAetB,SAASnJ;AAC5B,MAAImJ,SAASuB,KAAKH,OAAO,GAAG;AAC1BE,oBAAgBH;EACjB;AAED,MAAI3K,OAAO;AACT8K,oBAAgBN;EACjB;AAED,SAAOhB,SACJwB,OAAQH,OAAM,CAACD,QAAQC,CAAC,CAAC,EACzBI,OACC,CAAChC,OAAOiC,YACNjC,SACCqB,QAAQa,KAAKD,OAAO,IACjBX,sBACAW,YAAY,KACZT,oBACAC,qBACNI,YAAY;AAElB;AAEA,SAAST,eAAeF,GAAaC,GAAW;AAC9C,MAAIgB,WACFjB,EAAE9J,WAAW+J,EAAE/J,UAAU8J,EAAEnG,MAAM,GAAG,EAAE,EAAEqH,MAAM,CAAC5K,GAAGqH,MAAMrH,MAAM2J,EAAEtC,CAAC,CAAC;AAEpE,SAAOsD;;;;;IAKHjB,EAAEA,EAAE9J,SAAS,CAAC,IAAI+J,EAAEA,EAAE/J,SAAS,CAAC;;;;IAGhC;;AACN;AAEA,SAAS4H,iBAIPqD,QACApK,UACAsG,cAAoB;AAAA,MAApBA,iBAAY,QAAA;AAAZA,mBAAe;EAAK;AAEpB,MAAI;IAAEuB;EAAY,IAAGuC;AAErB,MAAIC,gBAAgB,CAAA;AACpB,MAAIC,kBAAkB;AACtB,MAAI3D,UAA2D,CAAA;AAC/D,WAASC,IAAI,GAAGA,IAAIiB,WAAW1I,QAAQ,EAAEyH,GAAG;AAC1C,QAAIa,OAAOI,WAAWjB,CAAC;AACvB,QAAI2D,MAAM3D,MAAMiB,WAAW1I,SAAS;AACpC,QAAIqL,oBACFF,oBAAoB,MAChBtK,WACAA,SAAS8C,MAAMwH,gBAAgBnL,MAAM,KAAK;AAChD,QAAI8H,QAAQwD,UACV;MAAE9J,MAAM8G,KAAKD;MAAcE,eAAeD,KAAKC;MAAe6C;OAC9DC,iBAAiB;AAGnB,QAAInF,QAAQoC,KAAKpC;AAEjB,QACE,CAAC4B,SACDsD,OACAjE,gBACA,CAACuB,WAAWA,WAAW1I,SAAS,CAAC,EAAEkG,MAAMvG,OACzC;AACAmI,cAAQwD,UACN;QACE9J,MAAM8G,KAAKD;QACXE,eAAeD,KAAKC;QACpB6C,KAAK;SAEPC,iBAAiB;IAEpB;AAED,QAAI,CAACvD,OAAO;AACV,aAAO;IACR;AAEDyD,WAAO7F,OAAOwF,eAAepD,MAAME,MAAM;AAEzCR,YAAQ5F,KAAK;;MAEXoG,QAAQkD;MACRrK,UAAU4H,UAAU,CAAC0C,iBAAiBrD,MAAMjH,QAAQ,CAAC;MACrD2K,cAAcC,kBACZhD,UAAU,CAAC0C,iBAAiBrD,MAAM0D,YAAY,CAAC,CAAC;MAElDtF;IACD,CAAA;AAED,QAAI4B,MAAM0D,iBAAiB,KAAK;AAC9BL,wBAAkB1C,UAAU,CAAC0C,iBAAiBrD,MAAM0D,YAAY,CAAC;IAClE;EACF;AAED,SAAOhE;AACT;SAOgBkE,aACdC,cACA3D,QAEa;AAAA,MAFbA,WAAAA,QAAAA;AAAAA,aAEI,CAAA;EAAS;AAEb,MAAIxG,OAAemK;AACnB,MAAInK,KAAKgI,SAAS,GAAG,KAAKhI,SAAS,OAAO,CAACA,KAAKgI,SAAS,IAAI,GAAG;AAC9D1I,YACE,OACA,iBAAeU,OACTA,sCAAAA,MAAAA,KAAKS,QAAQ,OAAO,IAAI,IAAsC,uCAAA,sEAE9BT,sCAAAA,KAAKS,QAAQ,OAAO,IAAI,IAAC,KAAI;AAErET,WAAOA,KAAKS,QAAQ,OAAO,IAAI;EAChC;AAGD,QAAM2J,SAASpK,KAAKyB,WAAW,GAAG,IAAI,MAAM;AAE5C,QAAMhC,YAAa4K,OACjBA,KAAK,OAAO,KAAK,OAAOA,MAAM,WAAWA,IAAIpF,OAAOoF,CAAC;AAEvD,QAAM1C,WAAW3H,KACd4H,MAAM,KAAK,EACX3J,IAAI,CAACoL,SAASlL,OAAOmM,UAAS;AAC7B,UAAMC,gBAAgBpM,UAAUmM,MAAM9L,SAAS;AAG/C,QAAI+L,iBAAiBlB,YAAY,KAAK;AACpC,YAAMmB,OAAO;AAEb,aAAO/K,UAAU+G,OAAOgE,IAAI,CAAC;IAC9B;AAED,UAAMC,WAAWpB,QAAQ/C,MAAM,kBAAkB;AACjD,QAAImE,UAAU;AACZ,YAAM,CAAA,EAAGvL,KAAKwL,QAAQ,IAAID;AAC1B,UAAIE,QAAQnE,OAAOtH,GAAsB;AACzCmD,gBAAUqI,aAAa,OAAOC,SAAS,MAAI,eAAezL,MAAG,SAAS;AACtE,aAAOO,UAAUkL,KAAK;IACvB;AAGD,WAAOtB,QAAQ5I,QAAQ,QAAQ,EAAE;GAClC,EAEA0I,OAAQE,aAAY,CAAC,CAACA,OAAO;AAEhC,SAAOe,SAASzC,SAASxC,KAAK,GAAG;AACnC;AAuDgB,SAAA2E,UAIdc,SACAvL,UAAgB;AAEhB,MAAI,OAAOuL,YAAY,UAAU;AAC/BA,cAAU;MAAE5K,MAAM4K;MAAS7D,eAAe;MAAO6C,KAAK;;EACvD;AAED,MAAI,CAACiB,SAASC,cAAc,IAAIC,YAC9BH,QAAQ5K,MACR4K,QAAQ7D,eACR6D,QAAQhB,GAAG;AAGb,MAAItD,QAAQjH,SAASiH,MAAMuE,OAAO;AAClC,MAAI,CAACvE,MAAO,QAAO;AAEnB,MAAIqD,kBAAkBrD,MAAM,CAAC;AAC7B,MAAI0D,eAAeL,gBAAgBlJ,QAAQ,WAAW,IAAI;AAC1D,MAAIuK,gBAAgB1E,MAAMnE,MAAM,CAAC;AACjC,MAAIqE,SAAiBsE,eAAe1B,OAClC,CAAC6B,MAAI7H,MAA6BjF,UAAS;AAAA,QAApC;MAAE+M;MAAWnD;QAAY3E;AAG9B,QAAI8H,cAAc,KAAK;AACrB,UAAIC,aAAaH,cAAc7M,KAAK,KAAK;AACzC6L,qBAAeL,gBACZxH,MAAM,GAAGwH,gBAAgBnL,SAAS2M,WAAW3M,MAAM,EACnDiC,QAAQ,WAAW,IAAI;IAC3B;AAED,UAAM6B,QAAQ0I,cAAc7M,KAAK;AACjC,QAAI4J,cAAc,CAACzF,OAAO;AACxB2I,WAAKC,SAAS,IAAI5M;IACnB,OAAM;AACL2M,WAAKC,SAAS,KAAK5I,SAAS,IAAI7B,QAAQ,QAAQ,GAAG;IACpD;AACD,WAAOwK;KAET,CAAA,CAAE;AAGJ,SAAO;IACLzE;IACAnH,UAAUsK;IACVK;IACAY;;AAEJ;AAIA,SAASG,YACP/K,MACA+G,eACA6C,KAAU;AAAA,MADV7C,kBAAa,QAAA;AAAbA,oBAAgB;EAAK;AAAA,MACrB6C,QAAG,QAAA;AAAHA,UAAM;EAAI;AAEVtK,UACEU,SAAS,OAAO,CAACA,KAAKgI,SAAS,GAAG,KAAKhI,KAAKgI,SAAS,IAAI,GACzD,iBAAehI,OACTA,sCAAAA,MAAAA,KAAKS,QAAQ,OAAO,IAAI,IAAsC,uCAAA,sEACE,sCAChCT,KAAKS,QAAQ,OAAO,IAAI,IAAC,KAAI;AAGrE,MAAI+F,SAA8B,CAAA;AAClC,MAAI4E,eACF,MACApL,KACGS,QAAQ,WAAW,EAAE,EACrBA,QAAQ,QAAQ,GAAG,EACnBA,QAAQ,sBAAsB,MAAM,EACpCA,QACC,qBACA,CAAC4K,GAAWH,WAAmBnD,eAAc;AAC3CvB,WAAOpG,KAAK;MAAE8K;MAAWnD,YAAYA,cAAc;IAAI,CAAE;AACzD,WAAOA,aAAa,iBAAiB;EACvC,CAAC;AAGP,MAAI/H,KAAKgI,SAAS,GAAG,GAAG;AACtBxB,WAAOpG,KAAK;MAAE8K,WAAW;IAAK,CAAA;AAC9BE,oBACEpL,SAAS,OAAOA,SAAS,OACrB,UACA;aACG4J,KAAK;AAEdwB,oBAAgB;aACPpL,SAAS,MAAMA,SAAS,KAAK;AAQtCoL,oBAAgB;EACjB,MAAM;AAIP,MAAIP,UAAU,IAAIS,OAAOF,cAAcrE,gBAAgBzI,SAAY,GAAG;AAEtE,SAAO,CAACuM,SAASrE,MAAM;AACzB;AAEM,SAAUL,WAAW7D,OAAa;AACtC,MAAI;AACF,WAAOA,MACJsF,MAAM,GAAG,EACT3J,IAAKsN,OAAMC,mBAAmBD,CAAC,EAAE9K,QAAQ,OAAO,KAAK,CAAC,EACtD0E,KAAK,GAAG;WACJpB,OAAO;AACdzE,YACE,OACA,mBAAiBgD,QACgD,6GAAA,eAClDyB,QAAK,KAAI;AAG1B,WAAOzB;EACR;AACH;AAKgB,SAAAsD,cACdvG,UACAoG,UAAgB;AAEhB,MAAIA,aAAa,IAAK,QAAOpG;AAE7B,MAAI,CAACA,SAASoM,YAAW,EAAGhK,WAAWgE,SAASgG,YAAW,CAAE,GAAG;AAC9D,WAAO;EACR;AAID,MAAIC,aAAajG,SAASuC,SAAS,GAAG,IAClCvC,SAASjH,SAAS,IAClBiH,SAASjH;AACb,MAAImN,WAAWtM,SAASE,OAAOmM,UAAU;AACzC,MAAIC,YAAYA,aAAa,KAAK;AAEhC,WAAO;EACR;AAED,SAAOtM,SAAS8C,MAAMuJ,UAAU,KAAK;AACvC;SAOgBE,YAAY3M,IAAQ4M,cAAkB;AAAA,MAAlBA,iBAAY,QAAA;AAAZA,mBAAe;EAAG;AACpD,MAAI;IACFxM,UAAUyM;IACV5L,SAAS;IACTC,OAAO;MACL,OAAOlB,OAAO,WAAWgB,UAAUhB,EAAE,IAAIA;AAE7C,MAAII,WAAWyM,aACXA,WAAWrK,WAAW,GAAG,IACvBqK,aACAC,gBAAgBD,YAAYD,YAAY,IAC1CA;AAEJ,SAAO;IACLxM;IACAa,QAAQ8L,gBAAgB9L,MAAM;IAC9BC,MAAM8L,cAAc9L,IAAI;;AAE5B;AAEA,SAAS4L,gBAAgBlF,cAAsBgF,cAAoB;AACjE,MAAIlE,WAAWkE,aAAapL,QAAQ,QAAQ,EAAE,EAAEmH,MAAM,GAAG;AACzD,MAAIsE,mBAAmBrF,aAAae,MAAM,GAAG;AAE7CsE,mBAAiB5E,QAAS+B,aAAW;AACnC,QAAIA,YAAY,MAAM;AAEpB,UAAI1B,SAASnJ,SAAS,EAAGmJ,UAASwE,IAAG;IACtC,WAAU9C,YAAY,KAAK;AAC1B1B,eAASvH,KAAKiJ,OAAO;IACtB;EACH,CAAC;AAED,SAAO1B,SAASnJ,SAAS,IAAImJ,SAASxC,KAAK,GAAG,IAAI;AACpD;AAEA,SAASiH,oBACPC,MACAC,OACAC,MACAvM,MAAmB;AAEnB,SACE,uBAAqBqM,OACbC,0CAAAA,SAAAA,QAAK,cAAa9M,KAAKC,UAC7BO,IAAI,IACL,yCACOuM,SAAAA,OAAI,8DACuD;AAEvE;AAyBM,SAAUC,2BAEdxG,SAAY;AACZ,SAAOA,QAAQmD,OACb,CAAC7C,OAAOnI,UACNA,UAAU,KAAMmI,MAAM5B,MAAM1E,QAAQsG,MAAM5B,MAAM1E,KAAKxB,SAAS,CAAE;AAEtE;AAIgB,SAAAiO,oBAEdzG,SAAc0G,sBAA6B;AAC3C,MAAIC,cAAcH,2BAA2BxG,OAAO;AAKpD,MAAI0G,sBAAsB;AACxB,WAAOC,YAAY1O,IAAI,CAACqI,OAAOrD,QAC7BA,QAAQ0J,YAAYnO,SAAS,IAAI8H,MAAMjH,WAAWiH,MAAM0D,YAAY;EAEvE;AAED,SAAO2C,YAAY1O,IAAKqI,WAAUA,MAAM0D,YAAY;AACtD;AAKM,SAAU4C,UACdC,OACAC,gBACAC,kBACAC,gBAAsB;AAAA,MAAtBA,mBAAc,QAAA;AAAdA,qBAAiB;EAAK;AAEtB,MAAI/N;AACJ,MAAI,OAAO4N,UAAU,UAAU;AAC7B5N,SAAKgB,UAAU4M,KAAK;EACrB,OAAM;AACL5N,SAAEkE,SAAQ0J,CAAAA,GAAAA,KAAK;AAEfxK,cACE,CAACpD,GAAGI,YAAY,CAACJ,GAAGI,SAASmI,SAAS,GAAG,GACzC4E,oBAAoB,KAAK,YAAY,UAAUnN,EAAE,CAAC;AAEpDoD,cACE,CAACpD,GAAGI,YAAY,CAACJ,GAAGI,SAASmI,SAAS,GAAG,GACzC4E,oBAAoB,KAAK,YAAY,QAAQnN,EAAE,CAAC;AAElDoD,cACE,CAACpD,GAAGiB,UAAU,CAACjB,GAAGiB,OAAOsH,SAAS,GAAG,GACrC4E,oBAAoB,KAAK,UAAU,QAAQnN,EAAE,CAAC;EAEjD;AAED,MAAIgO,cAAcJ,UAAU,MAAM5N,GAAGI,aAAa;AAClD,MAAIyM,aAAamB,cAAc,MAAMhO,GAAGI;AAExC,MAAI6N;AAWJ,MAAIpB,cAAc,MAAM;AACtBoB,WAAOH;EACR,OAAM;AACL,QAAII,qBAAqBL,eAAetO,SAAS;AAMjD,QAAI,CAACwO,kBAAkBlB,WAAWrK,WAAW,IAAI,GAAG;AAClD,UAAI2L,aAAatB,WAAWlE,MAAM,GAAG;AAErC,aAAOwF,WAAW,CAAC,MAAM,MAAM;AAC7BA,mBAAWC,MAAK;AAChBF,8BAAsB;MACvB;AAEDlO,SAAGI,WAAW+N,WAAWjI,KAAK,GAAG;IAClC;AAED+H,WAAOC,sBAAsB,IAAIL,eAAeK,kBAAkB,IAAI;EACvE;AAED,MAAInN,OAAO4L,YAAY3M,IAAIiO,IAAI;AAG/B,MAAII,2BACFxB,cAAcA,eAAe,OAAOA,WAAW9D,SAAS,GAAG;AAE7D,MAAIuF,2BACDN,eAAenB,eAAe,QAAQiB,iBAAiB/E,SAAS,GAAG;AACtE,MACE,CAAChI,KAAKX,SAAS2I,SAAS,GAAG,MAC1BsF,4BAA4BC,0BAC7B;AACAvN,SAAKX,YAAY;EAClB;AAED,SAAOW;AACT;IAiBawN,YAAaC,WACxBA,MAAMC,KAAK,GAAG,EAAEC,QAAQ,UAAU,GAAG;IAK1BC,oBAAqBC,cAChCA,SAASF,QAAQ,QAAQ,EAAE,EAAEA,QAAQ,QAAQ,GAAG;AAK3C,IAAMG,kBAAmBC,YAC9B,CAACA,UAAUA,WAAW,MAClB,KACAA,OAAOC,WAAW,GAAG,IACrBD,SACA,MAAMA;AAKL,IAAME,gBAAiBC,UAC5B,CAACA,QAAQA,SAAS,MAAM,KAAKA,KAAKF,WAAW,GAAG,IAAIE,OAAO,MAAMA;AAW5D,IAAMC,OAAqB,SAArBA,MAAsBC,MAAMC,MAAa;AAAA,MAAbA,SAAI,QAAA;AAAJA,WAAO,CAAA;EAAE;AAChD,MAAIC,eAAe,OAAOD,SAAS,WAAW;IAAEE,QAAQF;EAAI,IAAKA;AAEjE,MAAIG,UAAU,IAAIC,QAAQH,aAAaE,OAAO;AAC9C,MAAI,CAACA,QAAQE,IAAI,cAAc,GAAG;AAChCF,YAAQG,IAAI,gBAAgB,iCAAiC;EAC9D;AAED,SAAO,IAAIC,SAASC,KAAKC,UAAUV,IAAI,GAACW,SAAA,CAAA,GACnCT,cAAY;IACfE;EAAO,CAAA,CACR;AACH;AA8BM,IAAOQ,uBAAP,cAAoCC,MAAK;AAAA;IAElCC,qBAAY;EAWvBC,YAAYC,MAA+BC,cAA2B;AAV9D,SAAAC,iBAA8B,oBAAIC,IAAG;AAIrC,SAAAC,cACN,oBAAID,IAAG;AAGT,SAAYE,eAAa,CAAA;AAGvBC,cACEN,QAAQ,OAAOA,SAAS,YAAY,CAACO,MAAMC,QAAQR,IAAI,GACvD,oCAAoC;AAKtC,QAAIS;AACJ,SAAKC,eAAe,IAAIC,QAAQ,CAACC,GAAGC,MAAOJ,SAASI,CAAE;AACtD,SAAKC,aAAa,IAAIC,gBAAe;AACrC,QAAIC,UAAUA,MACZP,OAAO,IAAIb,qBAAqB,uBAAuB,CAAC;AAC1D,SAAKqB,sBAAsB,MACzB,KAAKH,WAAWI,OAAOC,oBAAoB,SAASH,OAAO;AAC7D,SAAKF,WAAWI,OAAOE,iBAAiB,SAASJ,OAAO;AAExD,SAAKhB,OAAOqB,OAAOC,QAAQtB,IAAI,EAAEuB,OAC/B,CAACC,KAAGC,UAAA;AAAA,UAAE,CAACC,KAAKC,KAAK,IAACF;AAAA,aAChBJ,OAAOO,OAAOJ,KAAK;QACjB,CAACE,GAAG,GAAG,KAAKG,aAAaH,KAAKC,KAAK;OACpC;OACH,CAAA,CAAE;AAGJ,QAAI,KAAKG,MAAM;AAEb,WAAKb,oBAAmB;IACzB;AAED,SAAKc,OAAO9B;EACd;EAEQ4B,aACNH,KACAC,OAAiC;AAEjC,QAAI,EAAEA,iBAAiBhB,UAAU;AAC/B,aAAOgB;IACR;AAED,SAAKtB,aAAa2B,KAAKN,GAAG;AAC1B,SAAKxB,eAAe+B,IAAIP,GAAG;AAI3B,QAAIQ,UAA0BvB,QAAQwB,KAAK,CAACR,OAAO,KAAKjB,YAAY,CAAC,EAAE0B,KACpEpC,UAAS,KAAKqC,SAASH,SAASR,KAAKY,QAAWtC,IAAe,GAC/DuC,WAAU,KAAKF,SAASH,SAASR,KAAKa,KAAgB,CAAC;AAK1DL,YAAQM,MAAM,MAAO;IAAA,CAAC;AAEtBnB,WAAOoB,eAAeP,SAAS,YAAY;MAAEQ,KAAKA,MAAM;IAAI,CAAE;AAC9D,WAAOR;EACT;EAEQG,SACNH,SACAR,KACAa,OACAvC,MAAc;AAEd,QACE,KAAKc,WAAWI,OAAOyB,WACvBJ,iBAAiB3C,sBACjB;AACA,WAAKqB,oBAAmB;AACxBI,aAAOoB,eAAeP,SAAS,UAAU;QAAEQ,KAAKA,MAAMH;MAAK,CAAE;AAC7D,aAAO5B,QAAQF,OAAO8B,KAAK;IAC5B;AAED,SAAKrC,eAAe0C,OAAOlB,GAAG;AAE9B,QAAI,KAAKI,MAAM;AAEb,WAAKb,oBAAmB;IACzB;AAID,QAAIsB,UAAUD,UAAatC,SAASsC,QAAW;AAC7C,UAAIO,iBAAiB,IAAIhD,MACvB,4BAA0B6B,MAAG,uFACwB;AAEvDL,aAAOoB,eAAeP,SAAS,UAAU;QAAEQ,KAAKA,MAAMG;MAAc,CAAE;AACtE,WAAKC,KAAK,OAAOpB,GAAG;AACpB,aAAOf,QAAQF,OAAOoC,cAAc;IACrC;AAED,QAAI7C,SAASsC,QAAW;AACtBjB,aAAOoB,eAAeP,SAAS,UAAU;QAAEQ,KAAKA,MAAMH;MAAK,CAAE;AAC7D,WAAKO,KAAK,OAAOpB,GAAG;AACpB,aAAOf,QAAQF,OAAO8B,KAAK;IAC5B;AAEDlB,WAAOoB,eAAeP,SAAS,SAAS;MAAEQ,KAAKA,MAAM1C;IAAI,CAAE;AAC3D,SAAK8C,KAAK,OAAOpB,GAAG;AACpB,WAAO1B;EACT;EAEQ8C,KAAKH,SAAkBI,YAAmB;AAChD,SAAK3C,YAAY4C,QAASC,gBAAeA,WAAWN,SAASI,UAAU,CAAC;EAC1E;EAEAG,UAAUC,IAAmD;AAC3D,SAAK/C,YAAY6B,IAAIkB,EAAE;AACvB,WAAO,MAAM,KAAK/C,YAAYwC,OAAOO,EAAE;EACzC;EAEAC,SAAM;AACJ,SAAKtC,WAAWuC,MAAK;AACrB,SAAKnD,eAAe8C,QAAQ,CAACM,GAAGC,MAAM,KAAKrD,eAAe0C,OAAOW,CAAC,CAAC;AACnE,SAAKT,KAAK,IAAI;EAChB;EAEA,MAAMU,YAAYtC,QAAmB;AACnC,QAAIyB,UAAU;AACd,QAAI,CAAC,KAAKb,MAAM;AACd,UAAId,UAAUA,MAAM,KAAKoC,OAAM;AAC/BlC,aAAOE,iBAAiB,SAASJ,OAAO;AACxC2B,gBAAU,MAAM,IAAIhC,QAAS8C,aAAW;AACtC,aAAKP,UAAWP,CAAAA,aAAW;AACzBzB,iBAAOC,oBAAoB,SAASH,OAAO;AAC3C,cAAI2B,YAAW,KAAKb,MAAM;AACxB2B,oBAAQd,QAAO;UAChB;QACH,CAAC;MACH,CAAC;IACF;AACD,WAAOA;EACT;EAEA,IAAIb,OAAI;AACN,WAAO,KAAK5B,eAAewD,SAAS;EACtC;EAEA,IAAIC,gBAAa;AACfrD,cACE,KAAKN,SAAS,QAAQ,KAAK8B,MAC3B,2DAA2D;AAG7D,WAAOT,OAAOC,QAAQ,KAAKtB,IAAI,EAAEuB,OAC/B,CAACC,KAAGoC,UAAA;AAAA,UAAE,CAAClC,KAAKC,KAAK,IAACiC;AAAA,aAChBvC,OAAOO,OAAOJ,KAAK;QACjB,CAACE,GAAG,GAAGmC,qBAAqBlC,KAAK;OAClC;OACH,CAAA,CAAE;EAEN;EAEA,IAAImC,cAAW;AACb,WAAOvD,MAAMwD,KAAK,KAAK7D,cAAc;EACvC;AACD;AAED,SAAS8D,iBAAiBrC,OAAU;AAClC,SACEA,iBAAiBhB,WAAYgB,MAAyBsC,aAAa;AAEvE;AAEA,SAASJ,qBAAqBlC,OAAU;AACtC,MAAI,CAACqC,iBAAiBrC,KAAK,GAAG;AAC5B,WAAOA;EACR;AAED,MAAIA,MAAMuC,QAAQ;AAChB,UAAMvC,MAAMuC;EACb;AACD,SAAOvC,MAAMwC;AACf;AAOO,IAAMC,QAAuB,SAAvBA,OAAwBpE,MAAM+B,MAAa;AAAA,MAAbA,SAAI,QAAA;AAAJA,WAAO,CAAA;EAAE;AAClD,MAAI9B,eAAe,OAAO8B,SAAS,WAAW;IAAEsC,QAAQtC;EAAI,IAAKA;AAEjE,SAAO,IAAIjC,aAAaE,MAAMC,YAAY;AAC5C;AAWO,IAAMqE,WAA6B,SAA7BA,UAA8BC,KAAKxC,MAAc;AAAA,MAAdA,SAAI,QAAA;AAAJA,WAAO;EAAG;AACxD,MAAI9B,eAAe8B;AACnB,MAAI,OAAO9B,iBAAiB,UAAU;AACpCA,mBAAe;MAAEoE,QAAQpE;;aAChB,OAAOA,aAAaoE,WAAW,aAAa;AACrDpE,iBAAaoE,SAAS;EACvB;AAED,MAAIG,UAAU,IAAIC,QAAQxE,aAAauE,OAAO;AAC9CA,UAAQE,IAAI,YAAYH,GAAG;AAE3B,SAAO,IAAII,SAAS,MAAIC,SAAA,CAAA,GACnB3E,cAAY;IACfuE;EAAO,CAAA,CACR;AACH;IAOaK,mBAAqCA,CAACN,KAAKxC,SAAQ;AAC9D,MAAI+C,WAAWR,SAASC,KAAKxC,IAAI;AACjC+C,WAASN,QAAQE,IAAI,2BAA2B,MAAM;AACtD,SAAOI;AACT;IAQaC,UAA4BA,CAACR,KAAKxC,SAAQ;AACrD,MAAI+C,WAAWR,SAASC,KAAKxC,IAAI;AACjC+C,WAASN,QAAQE,IAAI,mBAAmB,MAAM;AAC9C,SAAOI;AACT;IAgBaE,0BAAiB;EAO5BjF,YACEsE,QACAY,YACAjF,MACAkF,UAAgB;AAAA,QAAhBA,aAAQ,QAAA;AAARA,iBAAW;IAAK;AAEhB,SAAKb,SAASA;AACd,SAAKY,aAAaA,cAAc;AAChC,SAAKC,WAAWA;AAChB,QAAIlF,gBAAgBH,OAAO;AACzB,WAAKG,OAAOA,KAAKmF,SAAQ;AACzB,WAAK5C,QAAQvC;IACd,OAAM;AACL,WAAKA,OAAOA;IACb;EACH;AACD;AAMK,SAAUoF,qBAAqB7C,OAAU;AAC7C,SACEA,SAAS,QACT,OAAOA,MAAM8B,WAAW,YACxB,OAAO9B,MAAM0C,eAAe,YAC5B,OAAO1C,MAAM2C,aAAa,aAC1B,UAAU3C;AAEd;ACr/BA,IAAM8C,0BAAgD,CACpD,QACA,OACA,SACA,QAAQ;AAEV,IAAMC,uBAAuB,IAAInF,IAC/BkF,uBAAuB;AAGzB,IAAME,yBAAuC,CAC3C,OACA,GAAGF,uBAAuB;AAE5B,IAAMG,sBAAsB,IAAIrF,IAAgBoF,sBAAsB;AAEtE,IAAME,sBAAsB,oBAAItF,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAC7D,IAAMuF,oCAAoC,oBAAIvF,IAAI,CAAC,KAAK,GAAG,CAAC;AAErD,IAAMwF,kBAA4C;EACvDC,OAAO;EACPC,UAAUvD;EACVwD,YAAYxD;EACZyD,YAAYzD;EACZ0D,aAAa1D;EACb2D,UAAU3D;EACV4D,MAAM5D;EACN6D,MAAM7D;;AAGD,IAAM8D,eAAsC;EACjDR,OAAO;EACP5F,MAAMsC;EACNwD,YAAYxD;EACZyD,YAAYzD;EACZ0D,aAAa1D;EACb2D,UAAU3D;EACV4D,MAAM5D;EACN6D,MAAM7D;;AAGD,IAAM+D,eAAiC;EAC5CT,OAAO;EACPU,SAAShE;EACTiE,OAAOjE;EACPuD,UAAUvD;;AAGZ,IAAMkE,qBAAqB;AAE3B,IAAMC,4BAAyDC,YAAW;EACxEC,kBAAkBC,QAAQF,MAAMC,gBAAgB;AACjD;AAED,IAAME,0BAA0B;AAW1B,SAAUC,aAAa/E,MAAgB;AAC3C,QAAMgF,eAAehF,KAAKiF,SACtBjF,KAAKiF,SACL,OAAOA,WAAW,cAClBA,SACA1E;AACJ,QAAM2E,YACJ,OAAOF,iBAAiB,eACxB,OAAOA,aAAaG,aAAa,eACjC,OAAOH,aAAaG,SAASC,kBAAkB;AACjD,QAAMC,WAAW,CAACH;AAElB3G,YACEyB,KAAKsF,OAAOC,SAAS,GACrB,2DAA2D;AAG7D,MAAIC;AACJ,MAAIxF,KAAKwF,oBAAoB;AAC3BA,IAAAA,sBAAqBxF,KAAKwF;EAC3B,WAAUxF,KAAKyF,qBAAqB;AAEnC,QAAIA,sBAAsBzF,KAAKyF;AAC/BD,IAAAA,sBAAsBb,YAAW;MAC/BC,kBAAkBa,oBAAoBd,KAAK;IAC5C;EACF,OAAM;AACLa,IAAAA,sBAAqBd;EACtB;AAGD,MAAIgB,WAA0B,CAAA;AAE9B,MAAIC,aAAaC,0BACf5F,KAAKsF,QACLE,qBACAjF,QACAmF,QAAQ;AAEV,MAAIG;AACJ,MAAIC,WAAW9F,KAAK8F,YAAY;AAChC,MAAIC,mBAAmB/F,KAAKgG,yBAAyBC;AACrD,MAAIC,8BAA8BlG,KAAKmG;AAGvC,MAAIC,SAAMvD,SAAA;IACRwD,mBAAmB;IACnBC,wBAAwB;IACxBC,qBAAqB;IACrBC,oBAAoB;IACpBC,sBAAsB;IACtBC,gCAAgC;KAC7B1G,KAAKoG,MAAM;AAGhB,MAAIO,kBAAuC;AAE3C,MAAItI,cAAc,oBAAID,IAAG;AAGzB,MAAIwI,0BAA0B;AAC9B,MAAIC,mBAAmB,oBAAIzI,IAAG;AAE9B,MAAI0I,uBAAsD;AAE1D,MAAIC,0BAAkE;AAEtE,MAAIC,oBAAsD;AAO1D,MAAIC,wBAAwBjH,KAAKkH,iBAAiB;AAElD,MAAIC,iBAAiBC,YAAYzB,YAAY3F,KAAKqH,QAAQvD,UAAUgC,QAAQ;AAC5E,MAAIwB,gBAAkC;AAEtC,MAAIH,kBAAkB,QAAQ,CAACjB,6BAA6B;AAG1D,QAAI1F,QAAQ+G,uBAAuB,KAAK;MACtCC,UAAUxH,KAAKqH,QAAQvD,SAAS0D;IACjC,CAAA;AACD,QAAI;MAAEC;MAAS9C;IAAK,IAAK+C,uBAAuB/B,UAAU;AAC1DwB,qBAAiBM;AACjBH,oBAAgB;MAAE,CAAC3C,MAAMgD,EAAE,GAAGnH;;EAC/B;AAQD,MAAI2G,kBAAkB,CAACnH,KAAKkH,eAAe;AACzC,QAAIU,WAAWC,cACbV,gBACAxB,YACA3F,KAAKqH,QAAQvD,SAAS0D,QAAQ;AAEhC,QAAII,SAASE,QAAQ;AACnBX,uBAAiB;IAClB;EACF;AAED,MAAIY;AACJ,MAAI,CAACZ,gBAAgB;AACnBY,kBAAc;AACdZ,qBAAiB,CAAA;AAKjB,QAAIf,OAAOG,qBAAqB;AAC9B,UAAIqB,WAAWC,cACb,MACAlC,YACA3F,KAAKqH,QAAQvD,SAAS0D,QAAQ;AAEhC,UAAII,SAASE,UAAUF,SAASH,SAAS;AACvCN,yBAAiBS,SAASH;MAC3B;IACF;EACF,WAAUN,eAAea,KAAMC,OAAMA,EAAEtD,MAAMuD,IAAI,GAAG;AAGnDH,kBAAc;EACf,WAAU,CAACZ,eAAea,KAAMC,OAAMA,EAAEtD,MAAMwD,MAAM,GAAG;AAEtDJ,kBAAc;EACf,WAAU3B,OAAOG,qBAAqB;AAIrC,QAAI6B,aAAapI,KAAKkH,gBAAgBlH,KAAKkH,cAAckB,aAAa;AACtE,QAAIC,SAASrI,KAAKkH,gBAAgBlH,KAAKkH,cAAcmB,SAAS;AAC9D,QAAIC,qBAAsBL,OAA6B;AAErD,UAAI,CAACA,EAAEtD,MAAMwD,QAAQ;AACnB,eAAO;MACR;AAED,UACE,OAAOF,EAAEtD,MAAMwD,WAAW,cAC1BF,EAAEtD,MAAMwD,OAAOI,YAAY,MAC3B;AACA,eAAO;MACR;AAED,aACGH,cAAcA,WAAWH,EAAEtD,MAAMgD,EAAE,MAAMpH,UACzC8H,UAAUA,OAAOJ,EAAEtD,MAAMgD,EAAE,MAAMpH;;AAKtC,QAAI8H,QAAQ;AACV,UAAIG,MAAMrB,eAAesB,UACtBR,OAAMI,OAAQJ,EAAEtD,MAAMgD,EAAE,MAAMpH,MAAS;AAE1CwH,oBAAcZ,eAAeuB,MAAM,GAAGF,MAAM,CAAC,EAAEG,MAAML,kBAAkB;IACxE,OAAM;AACLP,oBAAcZ,eAAewB,MAAML,kBAAkB;IACtD;EACF,OAAM;AAGLP,kBAAc/H,KAAKkH,iBAAiB;EACrC;AAED,MAAI0B;AACJ,MAAI/E,QAAqB;IACvBgF,eAAe7I,KAAKqH,QAAQyB;IAC5BhF,UAAU9D,KAAKqH,QAAQvD;IACvB2D,SAASN;IACTY;IACAgB,YAAYnF;;IAEZoF,uBAAuBhJ,KAAKkH,iBAAiB,OAAO,QAAQ;IAC5D+B,oBAAoB;IACpBC,cAAc;IACdd,YAAapI,KAAKkH,iBAAiBlH,KAAKkH,cAAckB,cAAe,CAAA;IACrEe,YAAanJ,KAAKkH,iBAAiBlH,KAAKkH,cAAciC,cAAe;IACrEd,QAASrI,KAAKkH,iBAAiBlH,KAAKkH,cAAcmB,UAAWf;IAC7D8B,UAAU,oBAAIC,IAAG;IACjBC,UAAU,oBAAID,IAAG;;AAKnB,MAAIE,gBAA+BC,OAAcC;AAIjD,MAAIC,4BAA4B;AAGhC,MAAIC;AAGJ,MAAIC,+BAA+B;AAGnC,MAAIC,yBAAmD,oBAAIR,IAAG;AAM9D,MAAIS,8BAAmD;AAIvD,MAAIC,8BAA8B;AAMlC,MAAIC,yBAAyB;AAI7B,MAAIC,0BAAoC,CAAA;AAIxC,MAAIC,wBAAqC,oBAAI9L,IAAG;AAGhD,MAAI+L,mBAAmB,oBAAId,IAAG;AAG9B,MAAIe,qBAAqB;AAKzB,MAAIC,0BAA0B;AAG9B,MAAIC,iBAAiB,oBAAIjB,IAAG;AAG5B,MAAIkB,mBAAmB,oBAAInM,IAAG;AAG9B,MAAIoM,mBAAmB,oBAAInB,IAAG;AAG9B,MAAIoB,iBAAiB,oBAAIpB,IAAG;AAI5B,MAAIqB,kBAAkB,oBAAItM,IAAG;AAM7B,MAAIuM,kBAAkB,oBAAItB,IAAG;AAI7B,MAAIuB,mBAAmB,oBAAIvB,IAAG;AAI9B,MAAIwB,qBAAqB,oBAAIxB,IAAG;AAOhC,MAAIyB,8BAAwDvK;AAK5D,WAASwK,aAAU;AAGjBpE,sBAAkB3G,KAAKqH,QAAQ2D,OAC7BC,UAA+C;AAAA,UAA9C;QAAEnC,QAAQD;QAAe/E;QAAUoH;MAAK,IAAED;AAGzC,UAAIH,6BAA6B;AAC/BA,oCAA2B;AAC3BA,sCAA8BvK;AAC9B;MACD;AAED4K,cACEP,iBAAiBjJ,SAAS,KAAKuJ,SAAS,MACxC,4YAK2D;AAG7D,UAAIE,aAAaC,sBAAsB;QACrCC,iBAAiBzH,MAAMC;QACvByH,cAAczH;QACd+E;MACD,CAAA;AAED,UAAIuC,cAAcF,SAAS,MAAM;AAE/B,YAAIM,2BAA2B,IAAI5M,QAAe8C,aAAW;AAC3DoJ,wCAA8BpJ;QAChC,CAAC;AACD1B,aAAKqH,QAAQoE,GAAGP,QAAQ,EAAE;AAG1BQ,sBAAcN,YAAY;UACxBvH,OAAO;UACPC;UACAS,UAAO;AACLmH,0BAAcN,YAAa;cACzBvH,OAAO;cACPU,SAAShE;cACTiE,OAAOjE;cACPuD;YACD,CAAA;AAID0H,qCAAyBnL,KAAK,MAAML,KAAKqH,QAAQoE,GAAGP,KAAK,CAAC;;UAE5D1G,QAAK;AACH,gBAAI8E,WAAW,IAAID,IAAIxF,MAAMyF,QAAQ;AACrCA,qBAAS3G,IAAIyI,YAAa9G,YAAY;AACtCqH,wBAAY;cAAErC;YAAQ,CAAE;UAC1B;QACD,CAAA;AACD;MACD;AAED,aAAOsC,gBAAgB/C,eAAe/E,QAAQ;IAChD,CAAC;AAGH,QAAIoB,WAAW;AAGb2G,gCAA0B7G,cAAc6E,sBAAsB;AAC9D,UAAIiC,0BAA0BA,MAC5BC,0BAA0B/G,cAAc6E,sBAAsB;AAChE7E,mBAAa3F,iBAAiB,YAAYyM,uBAAuB;AACjEhC,oCAA8BA,MAC5B9E,aAAa5F,oBAAoB,YAAY0M,uBAAuB;IACvE;AAOD,QAAI,CAACjI,MAAMkE,aAAa;AACtB6D,sBAAgBpC,OAAcC,KAAK5F,MAAMC,UAAU;QACjDkI,kBAAkB;MACnB,CAAA;IACF;AAED,WAAOpD;EACT;AAGA,WAASqD,UAAO;AACd,QAAItF,iBAAiB;AACnBA,sBAAe;IAChB;AACD,QAAImD,6BAA6B;AAC/BA,kCAA2B;IAC5B;AACDzL,gBAAY6N,MAAK;AACjBvC,mCAA+BA,4BAA4BrI,MAAK;AAChEuC,UAAMuF,SAASnI,QAAQ,CAACpC,GAAGc,QAAQwM,cAAcxM,GAAG,CAAC;AACrDkE,UAAMyF,SAASrI,QAAQ,CAACpC,GAAGc,QAAQyM,cAAczM,GAAG,CAAC;EACvD;AAGA,WAASwB,UAAUC,IAAoB;AACrC/C,gBAAY6B,IAAIkB,EAAE;AAClB,WAAO,MAAM/C,YAAYwC,OAAOO,EAAE;EACpC;AAGA,WAASuK,YACPU,UACAC,MAGM;AAAA,QAHNA,SAAAA,QAAAA;AAAAA,aAGI,CAAA;IAAE;AAENzI,YAAKhB,SAAA,CAAA,GACAgB,OACAwI,QAAQ;AAKb,QAAIE,oBAA8B,CAAA;AAClC,QAAIC,sBAAgC,CAAA;AAEpC,QAAIpG,OAAOC,mBAAmB;AAC5BxC,YAAMuF,SAASnI,QAAQ,CAACwL,SAAS9M,QAAO;AACtC,YAAI8M,QAAQ5I,UAAU,QAAQ;AAC5B,cAAI6G,gBAAgBgC,IAAI/M,GAAG,GAAG;AAE5B6M,gCAAoBvM,KAAKN,GAAG;UAC7B,OAAM;AAGL4M,8BAAkBtM,KAAKN,GAAG;UAC3B;QACF;MACH,CAAC;IACF;AAKD,KAAC,GAAGtB,WAAW,EAAE4C,QAASC,gBACxBA,WAAW2C,OAAO;MAChB6G,iBAAiB8B;MACjBG,6BAA6BL,KAAKM;MAClCC,oBAAoBP,KAAKQ,cAAc;IACxC,CAAA,CAAC;AAIJ,QAAI1G,OAAOC,mBAAmB;AAC5BkG,wBAAkBtL,QAAStB,SAAQkE,MAAMuF,SAASvI,OAAOlB,GAAG,CAAC;AAC7D6M,0BAAoBvL,QAAStB,SAAQwM,cAAcxM,GAAG,CAAC;IACxD;EACH;AAOA,WAASoN,mBACPjJ,UACAuI,UAA0EW,OAC/B;AAAA,QAAAC,iBAAAC;AAAA,QAA3C;MAAEJ;IAAS,IAAAE,UAAA,SAA8B,CAAA,IAAEA;AAO3C,QAAIG,iBACFtJ,MAAMsF,cAAc,QACpBtF,MAAMkF,WAAWhF,cAAc,QAC/BqJ,iBAAiBvJ,MAAMkF,WAAWhF,UAAU,KAC5CF,MAAMkF,WAAWlF,UAAU,eAC3BoJ,kBAAAnJ,SAASD,UAAK,OAAA,SAAdoJ,gBAAgBI,iBAAgB;AAElC,QAAIlE;AACJ,QAAIkD,SAASlD,YAAY;AACvB,UAAI7J,OAAOgO,KAAKjB,SAASlD,UAAU,EAAE5D,SAAS,GAAG;AAC/C4D,qBAAakD,SAASlD;MACvB,OAAM;AAELA,qBAAa;MACd;eACQgE,gBAAgB;AAEzBhE,mBAAatF,MAAMsF;IACpB,OAAM;AAELA,mBAAa;IACd;AAGD,QAAIf,aAAaiE,SAASjE,aACtBmF,gBACE1J,MAAMuE,YACNiE,SAASjE,YACTiE,SAAS5E,WAAW,CAAA,GACpB4E,SAAShE,MAAM,IAEjBxE,MAAMuE;AAIV,QAAIkB,WAAWzF,MAAMyF;AACrB,QAAIA,SAAS3H,OAAO,GAAG;AACrB2H,iBAAW,IAAID,IAAIC,QAAQ;AAC3BA,eAASrI,QAAQ,CAACpC,GAAG2C,MAAM8H,SAAS3G,IAAInB,GAAG8C,YAAY,CAAC;IACzD;AAID,QAAI2E,qBACFS,8BAA8B,QAC7B7F,MAAMkF,WAAWhF,cAAc,QAC9BqJ,iBAAiBvJ,MAAMkF,WAAWhF,UAAU,OAC5CmJ,mBAAApJ,SAASD,UAATqJ,OAAAA,SAAAA,iBAAgBG,iBAAgB;AAGpC,QAAIxH,oBAAoB;AACtBF,mBAAaE;AACbA,2BAAqBtF;IACtB;AAED,QAAIwJ,4BAA6B;aAEtBR,kBAAkBC,OAAcC,IAAK;aAErCF,kBAAkBC,OAAcgE,MAAM;AAC/CxN,WAAKqH,QAAQpH,KAAK6D,UAAUA,SAASD,KAAK;IAC3C,WAAU0F,kBAAkBC,OAAciE,SAAS;AAClDzN,WAAKqH,QAAQrE,QAAQc,UAAUA,SAASD,KAAK;IAC9C;AAED,QAAI+I;AAGJ,QAAIrD,kBAAkBC,OAAcC,KAAK;AAEvC,UAAIiE,aAAa7D,uBAAuBlJ,IAAIkD,MAAMC,SAAS0D,QAAQ;AACnE,UAAIkG,cAAcA,WAAWhB,IAAI5I,SAAS0D,QAAQ,GAAG;AACnDoF,6BAAqB;UACnBtB,iBAAiBzH,MAAMC;UACvByH,cAAczH;;iBAEP+F,uBAAuB6C,IAAI5I,SAAS0D,QAAQ,GAAG;AAGxDoF,6BAAqB;UACnBtB,iBAAiBxH;UACjByH,cAAc1H,MAAMC;;MAEvB;eACQ8F,8BAA8B;AAEvC,UAAI+D,UAAU9D,uBAAuBlJ,IAAIkD,MAAMC,SAAS0D,QAAQ;AAChE,UAAImG,SAAS;AACXA,gBAAQzN,IAAI4D,SAAS0D,QAAQ;MAC9B,OAAM;AACLmG,kBAAU,oBAAIvP,IAAY,CAAC0F,SAAS0D,QAAQ,CAAC;AAC7CqC,+BAAuBlH,IAAIkB,MAAMC,SAAS0D,UAAUmG,OAAO;MAC5D;AACDf,2BAAqB;QACnBtB,iBAAiBzH,MAAMC;QACvByH,cAAczH;;IAEjB;AAED6H,gBAAW9I,SAAA,CAAA,GAEJwJ,UAAQ;MACXlD;MACAf;MACAS,eAAeU;MACfzF;MACAiE,aAAa;MACbgB,YAAYnF;MACZsF,cAAc;MACdF,uBAAuB4E,uBACrB9J,UACAuI,SAAS5E,WAAW5D,MAAM4D,OAAO;MAEnCwB;MACAK;KAEF,GAAA;MACEsD;MACAE,WAAWA,cAAc;IAC1B,CAAA;AAIHvD,oBAAgBC,OAAcC;AAC9BC,gCAA4B;AAC5BE,mCAA+B;AAC/BG,kCAA8B;AAC9BC,6BAAyB;AACzBC,8BAA0B,CAAA;EAC5B;AAIA,iBAAe4D,SACbC,IACAxB,MAA4B;AAE5B,QAAI,OAAOwB,OAAO,UAAU;AAC1B9N,WAAKqH,QAAQoE,GAAGqC,EAAE;AAClB;IACD;AAED,QAAIC,iBAAiBC,YACnBnK,MAAMC,UACND,MAAM4D,SACN3B,UACAM,OAAOI,oBACPsH,IACA1H,OAAOK,sBACP6F,QAAAA,OAAAA,SAAAA,KAAM2B,aACN3B,QAAI,OAAA,SAAJA,KAAM4B,QAAQ;AAEhB,QAAI;MAAEC;MAAMC;MAAY5N;IAAK,IAAK6N,yBAChCjI,OAAOE,wBACP,OACAyH,gBACAzB,IAAI;AAGN,QAAIhB,kBAAkBzH,MAAMC;AAC5B,QAAIyH,eAAe+C,eAAezK,MAAMC,UAAUqK,MAAM7B,QAAQA,KAAKzI,KAAK;AAO1E0H,mBAAY1I,SACP0I,CAAAA,GAAAA,cACAvL,KAAKqH,QAAQkH,eAAehD,YAAY,CAAC;AAG9C,QAAIiD,cAAclC,QAAQA,KAAKtJ,WAAW,OAAOsJ,KAAKtJ,UAAUzC;AAEhE,QAAIsI,gBAAgBW,OAAcgE;AAElC,QAAIgB,gBAAgB,MAAM;AACxB3F,sBAAgBW,OAAciE;IAC/B,WAAUe,gBAAgB,MAAO;aAGhCJ,cAAc,QACdhB,iBAAiBgB,WAAWrK,UAAU,KACtCqK,WAAWpK,eAAeH,MAAMC,SAAS0D,WAAW3D,MAAMC,SAAS2K,QACnE;AAKA5F,sBAAgBW,OAAciE;IAC/B;AAED,QAAIxE,qBACFqD,QAAQ,wBAAwBA,OAC5BA,KAAKrD,uBAAuB,OAC5B1I;AAEN,QAAIuM,aAAaR,QAAQA,KAAKO,wBAAwB;AAEtD,QAAIzB,aAAaC,sBAAsB;MACrCC;MACAC;MACA1C;IACD,CAAA;AAED,QAAIuC,YAAY;AAEdM,oBAAcN,YAAY;QACxBvH,OAAO;QACPC,UAAUyH;QACVhH,UAAO;AACLmH,wBAAcN,YAAa;YACzBvH,OAAO;YACPU,SAAShE;YACTiE,OAAOjE;YACPuD,UAAUyH;UACX,CAAA;AAEDsC,mBAASC,IAAIxB,IAAI;;QAEnB9H,QAAK;AACH,cAAI8E,WAAW,IAAID,IAAIxF,MAAMyF,QAAQ;AACrCA,mBAAS3G,IAAIyI,YAAa9G,YAAY;AACtCqH,sBAAY;YAAErC;UAAQ,CAAE;QAC1B;MACD,CAAA;AACD;IACD;AAED,WAAO,MAAMsC,gBAAgB/C,eAAe0C,cAAc;MACxD6C;;;MAGAM,cAAclO;MACdyI;MACAjG,SAASsJ,QAAQA,KAAKtJ;MACtB2L,sBAAsBrC,QAAQA,KAAKsC;MACnC9B;IACD,CAAA;EACH;AAKA,WAAS+B,aAAU;AACjBC,yBAAoB;AACpBnD,gBAAY;MAAEzC,cAAc;IAAS,CAAE;AAIvC,QAAIrF,MAAMkF,WAAWlF,UAAU,cAAc;AAC3C;IACD;AAKD,QAAIA,MAAMkF,WAAWlF,UAAU,QAAQ;AACrC+H,sBAAgB/H,MAAMgF,eAAehF,MAAMC,UAAU;QACnDiL,gCAAgC;MACjC,CAAA;AACD;IACD;AAKDnD,oBACErC,iBAAiB1F,MAAMgF,eACvBhF,MAAMkF,WAAWjF,UACjB;MACEkL,oBAAoBnL,MAAMkF;;MAE1B4F,sBAAsB/E,iCAAiC;IACxD,CAAA;EAEL;AAKA,iBAAegC,gBACb/C,eACA/E,UACAwI,MAWC;AAKD3C,mCAA+BA,4BAA4BrI,MAAK;AAChEqI,kCAA8B;AAC9BJ,oBAAgBV;AAChBkB,mCACGuC,QAAQA,KAAKyC,oCAAoC;AAIpDE,uBAAmBpL,MAAMC,UAAUD,MAAM4D,OAAO;AAChDiC,iCAA6B4C,QAAQA,KAAKrD,wBAAwB;AAElEW,oCAAgC0C,QAAQA,KAAKqC,0BAA0B;AAEvE,QAAIO,cAAcrJ,sBAAsBF;AACxC,QAAIwJ,oBAAoB7C,QAAQA,KAAK0C;AACrC,QAAIvH,UAAUL,YAAY8H,aAAapL,UAAUgC,QAAQ;AACzD,QAAIgH,aAAaR,QAAQA,KAAKQ,eAAe;AAE7C,QAAIlF,WAAWC,cAAcJ,SAASyH,aAAapL,SAAS0D,QAAQ;AACpE,QAAII,SAASE,UAAUF,SAASH,SAAS;AACvCA,gBAAUG,SAASH;IACpB;AAGD,QAAI,CAACA,SAAS;AACZ,UAAI;QAAEjH;QAAO4O;QAAiBzK;MAAK,IAAK0K,sBACtCvL,SAAS0D,QAAQ;AAEnBuF,yBACEjJ,UACA;QACE2D,SAAS2H;QACThH,YAAY,CAAA;QACZC,QAAQ;UACN,CAAC1D,MAAMgD,EAAE,GAAGnH;QACb;MACF,GACD;QAAEsM;MAAW,CAAA;AAEf;IACD;AAQD,QACEjJ,MAAMkE,eACN,CAACiC,0BACDsF,iBAAiBzL,MAAMC,UAAUA,QAAQ,KACzC,EAAEwI,QAAQA,KAAK8B,cAAchB,iBAAiBd,KAAK8B,WAAWrK,UAAU,IACxE;AACAgJ,yBAAmBjJ,UAAU;QAAE2D;MAAS,GAAE;QAAEqF;MAAW,CAAA;AACvD;IACD;AAGDnD,kCAA8B,IAAI3K,gBAAe;AACjD,QAAIuQ,UAAUC,wBACZxP,KAAKqH,SACLvD,UACA6F,4BAA4BxK,QAC5BmN,QAAQA,KAAK8B,UAAU;AAEzB,QAAIqB;AAEJ,QAAInD,QAAQA,KAAKoC,cAAc;AAK7Be,4BAAsB,CACpBC,oBAAoBjI,OAAO,EAAE9C,MAAMgD,IACnC;QAAEgI,MAAMC,WAAWpP;QAAOA,OAAO8L,KAAKoC;MAAc,CAAA;IAEvD,WACCpC,QACAA,KAAK8B,cACLhB,iBAAiBd,KAAK8B,WAAWrK,UAAU,GAC3C;AAEA,UAAI8L,eAAe,MAAMC,aACvBP,SACAzL,UACAwI,KAAK8B,YACL3G,SACAG,SAASE,QACT;QAAE9E,SAASsJ,KAAKtJ;QAAS8J;MAAS,CAAE;AAGtC,UAAI+C,aAAaE,gBAAgB;AAC/B;MACD;AAID,UAAIF,aAAaJ,qBAAqB;AACpC,YAAI,CAACO,SAASC,MAAM,IAAIJ,aAAaJ;AACrC,YACES,cAAcD,MAAM,KACpB5M,qBAAqB4M,OAAOzP,KAAK,KACjCyP,OAAOzP,MAAM8B,WAAW,KACxB;AACAqH,wCAA8B;AAE9BoD,6BAAmBjJ,UAAU;YAC3B2D,SAASoI,aAAapI;YACtBW,YAAY,CAAA;YACZC,QAAQ;cACN,CAAC2H,OAAO,GAAGC,OAAOzP;YACnB;UACF,CAAA;AACD;QACD;MACF;AAEDiH,gBAAUoI,aAAapI,WAAWA;AAClCgI,4BAAsBI,aAAaJ;AACnCN,0BAAoBgB,qBAAqBrM,UAAUwI,KAAK8B,UAAU;AAClEtB,kBAAY;AAEZlF,eAASE,SAAS;AAGlByH,gBAAUC,wBACRxP,KAAKqH,SACLkI,QAAQ/M,KACR+M,QAAQpQ,MAAM;IAEjB;AAGD,QAAI;MACF4Q;MACAtI,SAAS2I;MACThI;MACAC;QACE,MAAMgI,cACRd,SACAzL,UACA2D,SACAG,SAASE,QACTqH,mBACA7C,QAAQA,KAAK8B,YACb9B,QAAQA,KAAKgE,mBACbhE,QAAQA,KAAKtJ,SACbsJ,QAAQA,KAAKN,qBAAqB,MAClCc,WACA2C,mBAAmB;AAGrB,QAAIM,gBAAgB;AAClB;IACD;AAKDpG,kCAA8B;AAE9BoD,uBAAmBjJ,UAAQjB,SAAA;MACzB4E,SAAS2I,kBAAkB3I;OACxB8I,uBAAuBd,mBAAmB,GAAC;MAC9CrH;MACAC;IAAM,CAAA,CACP;EACH;AAIA,iBAAeyH,aACbP,SACAzL,UACAsK,YACA3G,SACA+I,YACAlE,MAAqD;AAAA,QAArDA,SAAAA,QAAAA;AAAAA,aAAmD,CAAA;IAAE;AAErDwC,yBAAoB;AAGpB,QAAI/F,aAAa0H,wBAAwB3M,UAAUsK,UAAU;AAC7DzC,gBAAY;MAAE5C;IAAU,GAAI;MAAE+D,WAAWR,KAAKQ,cAAc;IAAI,CAAE;AAElE,QAAI0D,YAAY;AACd,UAAIE,iBAAiB,MAAMC,eACzBlJ,SACA3D,SAAS0D,UACT+H,QAAQpQ,MAAM;AAEhB,UAAIuR,eAAef,SAAS,WAAW;AACrC,eAAO;UAAEI,gBAAgB;;MAC1B,WAAUW,eAAef,SAAS,SAAS;AAC1C,YAAI;UAAEiB;UAAYpQ;YAAUqQ,yBAC1B/M,SAAS0D,UACTkJ,cAAc;AAEhB,eAAO;UACLjJ,SAASiJ,eAAeI;UACxBrB,qBAAqB,CACnBmB,YACA;YACEjB,MAAMC,WAAWpP;YACjBA;WACD;;MAGN,WAAU,CAACkQ,eAAejJ,SAAS;AAClC,YAAI;UAAE2H;UAAiB5O;UAAOmE;QAAK,IAAK0K,sBACtCvL,SAAS0D,QAAQ;AAEnB,eAAO;UACLC,SAAS2H;UACTK,qBAAqB,CACnB9K,MAAMgD,IACN;YACEgI,MAAMC,WAAWpP;YACjBA;WACD;;MAGN,OAAM;AACLiH,kBAAUiJ,eAAejJ;MAC1B;IACF;AAGD,QAAIwI;AACJ,QAAIc,cAAcC,eAAevJ,SAAS3D,QAAQ;AAElD,QAAI,CAACiN,YAAYpM,MAAMmE,UAAU,CAACiI,YAAYpM,MAAMuD,MAAM;AACxD+H,eAAS;QACPN,MAAMC,WAAWpP;QACjBA,OAAO+G,uBAAuB,KAAK;UACjC0J,QAAQ1B,QAAQ0B;UAChBzJ,UAAU1D,SAAS0D;UACnBwI,SAASe,YAAYpM,MAAMgD;SAC5B;;IAEJ,OAAM;AACL,UAAIuJ,UAAU,MAAMC,iBAClB,UACAtN,OACA0L,SACA,CAACwB,WAAW,GACZtJ,SACA,IAAI;AAENwI,eAASiB,QAAQH,YAAYpM,MAAMgD,EAAE;AAErC,UAAI4H,QAAQpQ,OAAOyB,SAAS;AAC1B,eAAO;UAAEmP,gBAAgB;;MAC1B;IACF;AAED,QAAIqB,iBAAiBnB,MAAM,GAAG;AAC5B,UAAIjN;AACJ,UAAIsJ,QAAQA,KAAKtJ,WAAW,MAAM;AAChCA,QAAAA,WAAUsJ,KAAKtJ;MAChB,OAAM;AAIL,YAAIc,YAAWuN,0BACbpB,OAAOlN,SAASN,QAAQ9B,IAAI,UAAU,GACtC,IAAI2Q,IAAI/B,QAAQ/M,GAAG,GACnBsD,QAAQ;AAEV9C,QAAAA,WAAUc,cAAaD,MAAMC,SAAS0D,WAAW3D,MAAMC,SAAS2K;MACjE;AACD,YAAM8C,wBAAwBhC,SAASU,QAAQ,MAAM;QACnD7B;QACApL,SAAAA;MACD,CAAA;AACD,aAAO;QAAE+M,gBAAgB;;IAC1B;AAED,QAAIyB,iBAAiBvB,MAAM,GAAG;AAC5B,YAAM1I,uBAAuB,KAAK;QAAEoI,MAAM;MAAgB,CAAA;IAC3D;AAED,QAAIO,cAAcD,MAAM,GAAG;AAGzB,UAAIwB,gBAAgB/B,oBAAoBjI,SAASsJ,YAAYpM,MAAMgD,EAAE;AAOrE,WAAK2E,QAAQA,KAAKtJ,aAAa,MAAM;AACnCuG,wBAAgBC,OAAcgE;MAC/B;AAED,aAAO;QACL/F;QACAgI,qBAAqB,CAACgC,cAAc9M,MAAMgD,IAAIsI,MAAM;;IAEvD;AAED,WAAO;MACLxI;MACAgI,qBAAqB,CAACsB,YAAYpM,MAAMgD,IAAIsI,MAAM;;EAEtD;AAIA,iBAAeI,cACbd,SACAzL,UACA2D,SACA+I,YACAxB,oBACAZ,YACAkC,mBACAtN,UACAgJ,kBACAc,WACA2C,qBAAyC;AAGzC,QAAIN,oBACFH,sBAAsBmB,qBAAqBrM,UAAUsK,UAAU;AAIjE,QAAIsD,mBACFtD,cACAkC,qBACAqB,4BAA4BxC,iBAAiB;AAQ/C,QAAIyC,8BACF,CAAC7H,gCACA,CAAC3D,OAAOG,uBAAuB,CAACyF;AAOnC,QAAIwE,YAAY;AACd,UAAIoB,6BAA6B;AAC/B,YAAIzI,aAAa0I,qBAAqBpC,mBAAmB;AACzD9D,oBAAW9I,SAAA;UAEPkG,YAAYoG;WACRhG,eAAe5I,SAAY;UAAE4I;YAAe,CAAA,CAAE,GAEpD;UACE2D;QACD,CAAA;MAEJ;AAED,UAAI4D,iBAAiB,MAAMC,eACzBlJ,SACA3D,SAAS0D,UACT+H,QAAQpQ,MAAM;AAGhB,UAAIuR,eAAef,SAAS,WAAW;AACrC,eAAO;UAAEI,gBAAgB;;MAC1B,WAAUW,eAAef,SAAS,SAAS;AAC1C,YAAI;UAAEiB;UAAYpQ;YAAUqQ,yBAC1B/M,SAAS0D,UACTkJ,cAAc;AAEhB,eAAO;UACLjJ,SAASiJ,eAAeI;UACxB1I,YAAY,CAAA;UACZC,QAAQ;YACN,CAACuI,UAAU,GAAGpQ;UACf;;MAEJ,WAAU,CAACkQ,eAAejJ,SAAS;AAClC,YAAI;UAAEjH;UAAO4O;UAAiBzK;QAAK,IAAK0K,sBACtCvL,SAAS0D,QAAQ;AAEnB,eAAO;UACLC,SAAS2H;UACThH,YAAY,CAAA;UACZC,QAAQ;YACN,CAAC1D,MAAMgD,EAAE,GAAGnH;UACb;;MAEJ,OAAM;AACLiH,kBAAUiJ,eAAejJ;MAC1B;IACF;AAED,QAAIyH,cAAcrJ,sBAAsBF;AACxC,QAAI,CAACmM,eAAeC,oBAAoB,IAAIC,iBAC1ChS,KAAKqH,SACLxD,OACA4D,SACAiK,kBACA5N,UACAsC,OAAOG,uBAAuByF,qBAAqB,MACnD5F,OAAOM,gCACPsD,wBACAC,yBACAC,uBACAQ,iBACAF,kBACAD,kBACA2E,aACApJ,UACA2J,mBAAmB;AAMrBwC,0BACGjC,aACC,EAAEvI,WAAWA,QAAQO,KAAMC,OAAMA,EAAEtD,MAAMgD,OAAOqI,OAAO,MACtD8B,iBAAiBA,cAAc9J,KAAMC,OAAMA,EAAEtD,MAAMgD,OAAOqI,OAAO,CAAE;AAGxE3F,8BAA0B,EAAED;AAG5B,QAAI0H,cAAcvM,WAAW,KAAKwM,qBAAqBxM,WAAW,GAAG;AACnE,UAAI2M,mBAAkBC,uBAAsB;AAC5CpF,yBACEjJ,UAAQjB,SAAA;QAEN4E;QACAW,YAAY,CAAA;;QAEZC,QACEoH,uBAAuBS,cAAcT,oBAAoB,CAAC,CAAC,IACvD;UAAE,CAACA,oBAAoB,CAAC,CAAC,GAAGA,oBAAoB,CAAC,EAAEjP;QAAO,IAC1D;MAAI,GACP+P,uBAAuBd,mBAAmB,GACzCyC,mBAAkB;QAAE9I,UAAU,IAAIC,IAAIxF,MAAMuF,QAAQ;UAAM,CAAA,CAAE,GAElE;QAAE0D;MAAW,CAAA;AAEf,aAAO;QAAEiD,gBAAgB;;IAC1B;AAED,QAAI6B,6BAA6B;AAC/B,UAAIQ,UAAgC,CAAA;AACpC,UAAI,CAAC5B,YAAY;AAEf4B,gBAAQrJ,aAAaoG;AACrB,YAAIhG,aAAa0I,qBAAqBpC,mBAAmB;AACzD,YAAItG,eAAe5I,QAAW;AAC5B6R,kBAAQjJ,aAAaA;QACtB;MACF;AACD,UAAI4I,qBAAqBxM,SAAS,GAAG;AACnC6M,gBAAQhJ,WAAWiJ,+BAA+BN,oBAAoB;MACvE;AACDpG,kBAAYyG,SAAS;QAAEtF;MAAS,CAAE;IACnC;AAEDiF,yBAAqB9Q,QAASqR,QAAM;AAClC,UAAInI,iBAAiBuC,IAAI4F,GAAG3S,GAAG,GAAG;AAChC4S,qBAAaD,GAAG3S,GAAG;MACpB;AACD,UAAI2S,GAAGvT,YAAY;AAIjBoL,yBAAiBxH,IAAI2P,GAAG3S,KAAK2S,GAAGvT,UAAU;MAC3C;IACH,CAAC;AAGD,QAAIyT,iCAAiCA,MACnCT,qBAAqB9Q,QAASwR,OAAMF,aAAaE,EAAE9S,GAAG,CAAC;AACzD,QAAIgK,6BAA6B;AAC/BA,kCAA4BxK,OAAOE,iBACjC,SACAmT,8BAA8B;IAEjC;AAED,QAAI;MAAEE;MAAeC;IAAgB,IACnC,MAAMC,+BACJ/O,OACA4D,SACAqK,eACAC,sBACAxC,OAAO;AAGX,QAAIA,QAAQpQ,OAAOyB,SAAS;AAC1B,aAAO;QAAEmP,gBAAgB;;IAC1B;AAKD,QAAIpG,6BAA6B;AAC/BA,kCAA4BxK,OAAOC,oBACjC,SACAoT,8BAA8B;IAEjC;AACDT,yBAAqB9Q,QAASqR,QAAOnI,iBAAiBtJ,OAAOyR,GAAG3S,GAAG,CAAC;AAGpE,QAAI4C,YAAWsQ,aAAaH,aAAa;AACzC,QAAInQ,WAAU;AACZ,YAAMgP,wBAAwBhC,SAAShN,UAAS0N,QAAQ,MAAM;QAC5DjN,SAAAA;MACD,CAAA;AACD,aAAO;QAAE+M,gBAAgB;;IAC1B;AAEDxN,IAAAA,YAAWsQ,aAAaF,cAAc;AACtC,QAAIpQ,WAAU;AAIZgI,uBAAiBrK,IAAIqC,UAAS5C,GAAG;AACjC,YAAM4R,wBAAwBhC,SAAShN,UAAS0N,QAAQ,MAAM;QAC5DjN,SAAAA;MACD,CAAA;AACD,aAAO;QAAE+M,gBAAgB;;IAC1B;AAGD,QAAI;MAAE3H;MAAYC;IAAM,IAAKyK,kBAC3BjP,OACA4D,SACAqK,eACAY,eACAjD,qBACAsC,sBACAY,gBACAhI,eAAe;AAIjBA,oBAAgB1J,QAAQ,CAAC8R,cAAc/C,YAAW;AAChD+C,mBAAa5R,UAAWP,aAAW;AAIjC,YAAIA,WAAWmS,aAAahT,MAAM;AAChC4K,0BAAgB9J,OAAOmP,OAAO;QAC/B;MACH,CAAC;IACH,CAAC;AAGD,QAAI5J,OAAOG,uBAAuByF,oBAAoBnI,MAAMwE,QAAQ;AAClE/I,aAAOC,QAAQsE,MAAMwE,MAAM,EACxB2K,OAAOtT,WAAA;AAAA,YAAC,CAACiI,EAAE,IAACjI;AAAA,eAAK,CAACoS,cAAc9J,KAAMC,OAAMA,EAAEtD,MAAMgD,OAAOA,EAAE;MAAC,CAAA,EAC9D1G,QAAQY,WAAqB;AAAA,YAApB,CAACmO,SAASxP,KAAK,IAACqB;AACxBwG,iBAAS/I,OAAOO,OAAOwI,UAAU,CAAA,GAAI;UAAE,CAAC2H,OAAO,GAAGxP;QAAK,CAAE;MAC3D,CAAC;IACJ;AAED,QAAI0R,kBAAkBC,uBAAsB;AAC5C,QAAIc,qBAAqBC,qBAAqB7I,uBAAuB;AACrE,QAAI8I,uBACFjB,mBAAmBe,sBAAsBlB,qBAAqBxM,SAAS;AAEzE,WAAA1C,SAAA;MACE4E;MACAW;MACAC;IAAM,GACF8K,uBAAuB;MAAE/J,UAAU,IAAIC,IAAIxF,MAAMuF,QAAQ;QAAM,CAAA,CAAE;EAEzE;AAEA,WAASyI,qBACPpC,qBAAoD;AAEpD,QAAIA,uBAAuB,CAACS,cAAcT,oBAAoB,CAAC,CAAC,GAAG;AAIjE,aAAO;QACL,CAACA,oBAAoB,CAAC,CAAC,GAAGA,oBAAoB,CAAC,EAAExR;;IAEpD,WAAU4F,MAAMsF,YAAY;AAC3B,UAAI7J,OAAOgO,KAAKzJ,MAAMsF,UAAU,EAAE5D,WAAW,GAAG;AAC9C,eAAO;MACR,OAAM;AACL,eAAO1B,MAAMsF;MACd;IACF;EACH;AAEA,WAASkJ,+BACPN,sBAA2C;AAE3CA,yBAAqB9Q,QAASqR,QAAM;AAClC,UAAI7F,UAAU5I,MAAMuF,SAASzI,IAAI2R,GAAG3S,GAAG;AACvC,UAAIyT,sBAAsBC,kBACxB9S,QACAkM,UAAUA,QAAQxO,OAAOsC,MAAS;AAEpCsD,YAAMuF,SAASzG,IAAI2P,GAAG3S,KAAKyT,mBAAmB;IAChD,CAAC;AACD,WAAO,IAAI/J,IAAIxF,MAAMuF,QAAQ;EAC/B;AAGA,WAASkK,MACP3T,KACAqQ,SACAuD,MACAjH,MAAyB;AAEzB,QAAIjH,UAAU;AACZ,YAAM,IAAIvH,MACR,kMAE+C;IAElD;AAED,QAAIqM,iBAAiBuC,IAAI/M,GAAG,EAAG4S,cAAa5S,GAAG;AAC/C,QAAImN,aAAaR,QAAQA,KAAKO,wBAAwB;AAEtD,QAAIqC,cAAcrJ,sBAAsBF;AACxC,QAAIoI,iBAAiBC,YACnBnK,MAAMC,UACND,MAAM4D,SACN3B,UACAM,OAAOI,oBACP+M,MACAnN,OAAOK,sBACPuJ,SACA1D,QAAI,OAAA,SAAJA,KAAM4B,QAAQ;AAEhB,QAAIzG,UAAUL,YAAY8H,aAAanB,gBAAgBjI,QAAQ;AAE/D,QAAI8B,WAAWC,cAAcJ,SAASyH,aAAanB,cAAc;AACjE,QAAInG,SAASE,UAAUF,SAASH,SAAS;AACvCA,gBAAUG,SAASH;IACpB;AAED,QAAI,CAACA,SAAS;AACZ+L,sBACE7T,KACAqQ,SACAzI,uBAAuB,KAAK;QAAEC,UAAUuG;OAAgB,GACxD;QAAEjB;MAAS,CAAE;AAEf;IACD;AAED,QAAI;MAAEqB;MAAMC;MAAY5N;IAAK,IAAK6N,yBAChCjI,OAAOE,wBACP,MACAyH,gBACAzB,IAAI;AAGN,QAAI9L,OAAO;AACTgT,sBAAgB7T,KAAKqQ,SAASxP,OAAO;QAAEsM;MAAW,CAAA;AAClD;IACD;AAED,QAAI2G,QAAQzC,eAAevJ,SAAS0G,IAAI;AAExCzE,iCAA6B4C,QAAQA,KAAKrD,wBAAwB;AAElE,QAAImF,cAAchB,iBAAiBgB,WAAWrK,UAAU,GAAG;AACzD2P,0BACE/T,KACAqQ,SACA7B,MACAsF,OACAhM,SACAG,SAASE,QACTgF,WACAsB,UAAU;AAEZ;IACD;AAID5D,qBAAiB7H,IAAIhD,KAAK;MAAEqQ;MAAS7B;IAAM,CAAA;AAC3CwF,wBACEhU,KACAqQ,SACA7B,MACAsF,OACAhM,SACAG,SAASE,QACTgF,WACAsB,UAAU;EAEd;AAIA,iBAAesF,oBACb/T,KACAqQ,SACA7B,MACAsF,OACAG,gBACApD,YACA1D,WACAsB,YAAsB;AAEtBU,yBAAoB;AACpBtE,qBAAiB3J,OAAOlB,GAAG;AAE3B,aAASkU,wBAAwB5L,GAAyB;AACxD,UAAI,CAACA,EAAEtD,MAAMmE,UAAU,CAACb,EAAEtD,MAAMuD,MAAM;AACpC,YAAI1H,QAAQ+G,uBAAuB,KAAK;UACtC0J,QAAQ7C,WAAWrK;UACnByD,UAAU2G;UACV6B;QACD,CAAA;AACDwD,wBAAgB7T,KAAKqQ,SAASxP,OAAO;UAAEsM;QAAW,CAAA;AAClD,eAAO;MACR;AACD,aAAO;IACT;AAEA,QAAI,CAAC0D,cAAcqD,wBAAwBJ,KAAK,GAAG;AACjD;IACD;AAGD,QAAIK,kBAAkBjQ,MAAMuF,SAASzI,IAAIhB,GAAG;AAC5CoU,uBAAmBpU,KAAKqU,qBAAqB5F,YAAY0F,eAAe,GAAG;MACzEhH;IACD,CAAA;AAED,QAAImH,kBAAkB,IAAIjV,gBAAe;AACzC,QAAIkV,eAAe1E,wBACjBxP,KAAKqH,SACL8G,MACA8F,gBAAgB9U,QAChBiP,UAAU;AAGZ,QAAIoC,YAAY;AACd,UAAIE,iBAAiB,MAAMC,eACzBiD,gBACAzF,MACA+F,aAAa/U,MAAM;AAGrB,UAAIuR,eAAef,SAAS,WAAW;AACrC;MACD,WAAUe,eAAef,SAAS,SAAS;AAC1C,YAAI;UAAEnP;QAAK,IAAKqQ,yBAAyB1C,MAAMuC,cAAc;AAC7D8C,wBAAgB7T,KAAKqQ,SAASxP,OAAO;UAAEsM;QAAW,CAAA;AAClD;MACD,WAAU,CAAC4D,eAAejJ,SAAS;AAClC+L,wBACE7T,KACAqQ,SACAzI,uBAAuB,KAAK;UAAEC,UAAU2G;SAAM,GAC9C;UAAErB;QAAS,CAAE;AAEf;MACD,OAAM;AACL8G,yBAAiBlD,eAAejJ;AAChCgM,gBAAQzC,eAAe4C,gBAAgBzF,IAAI;AAE3C,YAAI0F,wBAAwBJ,KAAK,GAAG;AAClC;QACD;MACF;IACF;AAGDtJ,qBAAiBxH,IAAIhD,KAAKsU,eAAe;AAEzC,QAAIE,oBAAoB/J;AACxB,QAAIgK,gBAAgB,MAAMjD,iBACxB,UACAtN,OACAqQ,cACA,CAACT,KAAK,GACNG,gBACAjU,GAAG;AAEL,QAAIkQ,eAAeuE,cAAcX,MAAM9O,MAAMgD,EAAE;AAE/C,QAAIuM,aAAa/U,OAAOyB,SAAS;AAG/B,UAAIuJ,iBAAiBxJ,IAAIhB,GAAG,MAAMsU,iBAAiB;AACjD9J,yBAAiBtJ,OAAOlB,GAAG;MAC5B;AACD;IACD;AAKD,QAAIyG,OAAOC,qBAAqBqE,gBAAgBgC,IAAI/M,GAAG,GAAG;AACxD,UAAIyR,iBAAiBvB,YAAY,KAAKK,cAAcL,YAAY,GAAG;AACjEkE,2BAAmBpU,KAAK0U,eAAe9T,MAAS,CAAC;AACjD;MACD;IAEF,OAAM;AACL,UAAI6Q,iBAAiBvB,YAAY,GAAG;AAClC1F,yBAAiBtJ,OAAOlB,GAAG;AAC3B,YAAI0K,0BAA0B8J,mBAAmB;AAK/CJ,6BAAmBpU,KAAK0U,eAAe9T,MAAS,CAAC;AACjD;QACD,OAAM;AACLgK,2BAAiBrK,IAAIP,GAAG;AACxBoU,6BAAmBpU,KAAK0T,kBAAkBjF,UAAU,CAAC;AACrD,iBAAOmD,wBAAwB2C,cAAcrE,cAAc,OAAO;YAChES,mBAAmBlC;UACpB,CAAA;QACF;MACF;AAGD,UAAI8B,cAAcL,YAAY,GAAG;AAC/B2D,wBAAgB7T,KAAKqQ,SAASH,aAAarP,KAAK;AAChD;MACD;IACF;AAED,QAAIgR,iBAAiB3B,YAAY,GAAG;AAClC,YAAMtI,uBAAuB,KAAK;QAAEoI,MAAM;MAAgB,CAAA;IAC3D;AAID,QAAIpE,eAAe1H,MAAMkF,WAAWjF,YAAYD,MAAMC;AACtD,QAAIwQ,sBAAsB9E,wBACxBxP,KAAKqH,SACLkE,cACA0I,gBAAgB9U,MAAM;AAExB,QAAI+P,cAAcrJ,sBAAsBF;AACxC,QAAI8B,UACF5D,MAAMkF,WAAWlF,UAAU,SACvBuD,YAAY8H,aAAarL,MAAMkF,WAAWjF,UAAUgC,QAAQ,IAC5DjC,MAAM4D;AAEZlJ,cAAUkJ,SAAS,8CAA8C;AAEjE,QAAI8M,SAAS,EAAEnK;AACfE,mBAAe3H,IAAIhD,KAAK4U,MAAM;AAE9B,QAAIC,cAAcnB,kBAAkBjF,YAAYyB,aAAa5R,IAAI;AACjE4F,UAAMuF,SAASzG,IAAIhD,KAAK6U,WAAW;AAEnC,QAAI,CAAC1C,eAAeC,oBAAoB,IAAIC,iBAC1ChS,KAAKqH,SACLxD,OACA4D,SACA2G,YACA7C,cACA,OACAnF,OAAOM,gCACPsD,wBACAC,yBACAC,uBACAQ,iBACAF,kBACAD,kBACA2E,aACApJ,UACA,CAAC2N,MAAM9O,MAAMgD,IAAIkI,YAAY,CAAC;AAMhCkC,yBACGiB,OAAQV,QAAOA,GAAG3S,QAAQA,GAAG,EAC7BsB,QAASqR,QAAM;AACd,UAAImC,WAAWnC,GAAG3S;AAClB,UAAImU,mBAAkBjQ,MAAMuF,SAASzI,IAAI8T,QAAQ;AACjD,UAAIrB,sBAAsBC,kBACxB9S,QACAuT,mBAAkBA,iBAAgB7V,OAAOsC,MAAS;AAEpDsD,YAAMuF,SAASzG,IAAI8R,UAAUrB,mBAAmB;AAChD,UAAIjJ,iBAAiBuC,IAAI+H,QAAQ,GAAG;AAClClC,qBAAakC,QAAQ;MACtB;AACD,UAAInC,GAAGvT,YAAY;AACjBoL,yBAAiBxH,IAAI8R,UAAUnC,GAAGvT,UAAU;MAC7C;IACH,CAAC;AAEH4M,gBAAY;MAAEvC,UAAU,IAAIC,IAAIxF,MAAMuF,QAAQ;IAAC,CAAE;AAEjD,QAAIoJ,iCAAiCA,MACnCT,qBAAqB9Q,QAASqR,QAAOC,aAAaD,GAAG3S,GAAG,CAAC;AAE3DsU,oBAAgB9U,OAAOE,iBACrB,SACAmT,8BAA8B;AAGhC,QAAI;MAAEE;MAAeC;IAAgB,IACnC,MAAMC,+BACJ/O,OACA4D,SACAqK,eACAC,sBACAuC,mBAAmB;AAGvB,QAAIL,gBAAgB9U,OAAOyB,SAAS;AAClC;IACD;AAEDqT,oBAAgB9U,OAAOC,oBACrB,SACAoT,8BAA8B;AAGhClI,mBAAezJ,OAAOlB,GAAG;AACzBwK,qBAAiBtJ,OAAOlB,GAAG;AAC3BoS,yBAAqB9Q,QAASnC,OAAMqL,iBAAiBtJ,OAAO/B,EAAEa,GAAG,CAAC;AAElE,QAAI4C,YAAWsQ,aAAaH,aAAa;AACzC,QAAInQ,WAAU;AACZ,aAAOgP,wBACL+C,qBACA/R,UAAS0N,QACT,KAAK;IAER;AAED1N,IAAAA,YAAWsQ,aAAaF,cAAc;AACtC,QAAIpQ,WAAU;AAIZgI,uBAAiBrK,IAAIqC,UAAS5C,GAAG;AACjC,aAAO4R,wBACL+C,qBACA/R,UAAS0N,QACT,KAAK;IAER;AAGD,QAAI;MAAE7H;MAAYC;IAAM,IAAKyK,kBAC3BjP,OACA4D,SACAqK,eACAY,eACAnS,QACAwR,sBACAY,gBACAhI,eAAe;AAKjB,QAAI9G,MAAMuF,SAASsD,IAAI/M,GAAG,GAAG;AAC3B,UAAI+U,cAAcL,eAAexE,aAAa5R,IAAI;AAClD4F,YAAMuF,SAASzG,IAAIhD,KAAK+U,WAAW;IACpC;AAEDxB,yBAAqBqB,MAAM;AAK3B,QACE1Q,MAAMkF,WAAWlF,UAAU,aAC3B0Q,SAASlK,yBACT;AACA9L,gBAAUgL,eAAe,yBAAyB;AAClDI,qCAA+BA,4BAA4BrI,MAAK;AAEhEyL,yBAAmBlJ,MAAMkF,WAAWjF,UAAU;QAC5C2D;QACAW;QACAC;QACAe,UAAU,IAAIC,IAAIxF,MAAMuF,QAAQ;MACjC,CAAA;IACF,OAAM;AAILuC,kBAAY;QACVtD;QACAD,YAAYmF,gBACV1J,MAAMuE,YACNA,YACAX,SACAY,MAAM;QAERe,UAAU,IAAIC,IAAIxF,MAAMuF,QAAQ;MACjC,CAAA;AACDY,+BAAyB;IAC1B;EACH;AAGA,iBAAe2J,oBACbhU,KACAqQ,SACA7B,MACAsF,OACAhM,SACA+I,YACA1D,WACAsB,YAAuB;AAEvB,QAAI0F,kBAAkBjQ,MAAMuF,SAASzI,IAAIhB,GAAG;AAC5CoU,uBACEpU,KACA0T,kBACEjF,YACA0F,kBAAkBA,gBAAgB7V,OAAOsC,MAAS,GAEpD;MAAEuM;IAAW,CAAA;AAGf,QAAImH,kBAAkB,IAAIjV,gBAAe;AACzC,QAAIkV,eAAe1E,wBACjBxP,KAAKqH,SACL8G,MACA8F,gBAAgB9U,MAAM;AAGxB,QAAIqR,YAAY;AACd,UAAIE,iBAAiB,MAAMC,eACzBlJ,SACA0G,MACA+F,aAAa/U,MAAM;AAGrB,UAAIuR,eAAef,SAAS,WAAW;AACrC;MACD,WAAUe,eAAef,SAAS,SAAS;AAC1C,YAAI;UAAEnP;QAAK,IAAKqQ,yBAAyB1C,MAAMuC,cAAc;AAC7D8C,wBAAgB7T,KAAKqQ,SAASxP,OAAO;UAAEsM;QAAW,CAAA;AAClD;MACD,WAAU,CAAC4D,eAAejJ,SAAS;AAClC+L,wBACE7T,KACAqQ,SACAzI,uBAAuB,KAAK;UAAEC,UAAU2G;SAAM,GAC9C;UAAErB;QAAS,CAAE;AAEf;MACD,OAAM;AACLrF,kBAAUiJ,eAAejJ;AACzBgM,gBAAQzC,eAAevJ,SAAS0G,IAAI;MACrC;IACF;AAGDhE,qBAAiBxH,IAAIhD,KAAKsU,eAAe;AAEzC,QAAIE,oBAAoB/J;AACxB,QAAI8G,UAAU,MAAMC,iBAClB,UACAtN,OACAqQ,cACA,CAACT,KAAK,GACNhM,SACA9H,GAAG;AAEL,QAAIsQ,SAASiB,QAAQuC,MAAM9O,MAAMgD,EAAE;AAMnC,QAAI6J,iBAAiBvB,MAAM,GAAG;AAC5BA,eACG,MAAM0E,oBAAoB1E,QAAQiE,aAAa/U,QAAQ,IAAI,KAC5D8Q;IACH;AAID,QAAI9F,iBAAiBxJ,IAAIhB,GAAG,MAAMsU,iBAAiB;AACjD9J,uBAAiBtJ,OAAOlB,GAAG;IAC5B;AAED,QAAIuU,aAAa/U,OAAOyB,SAAS;AAC/B;IACD;AAID,QAAI8J,gBAAgBgC,IAAI/M,GAAG,GAAG;AAC5BoU,yBAAmBpU,KAAK0U,eAAe9T,MAAS,CAAC;AACjD;IACD;AAGD,QAAI6Q,iBAAiBnB,MAAM,GAAG;AAC5B,UAAI5F,0BAA0B8J,mBAAmB;AAG/CJ,2BAAmBpU,KAAK0U,eAAe9T,MAAS,CAAC;AACjD;MACD,OAAM;AACLgK,yBAAiBrK,IAAIP,GAAG;AACxB,cAAM4R,wBAAwB2C,cAAcjE,QAAQ,KAAK;AACzD;MACD;IACF;AAGD,QAAIC,cAAcD,MAAM,GAAG;AACzBuD,sBAAgB7T,KAAKqQ,SAASC,OAAOzP,KAAK;AAC1C;IACD;AAEDjC,cAAU,CAACiT,iBAAiBvB,MAAM,GAAG,iCAAiC;AAGtE8D,uBAAmBpU,KAAK0U,eAAepE,OAAOhS,IAAI,CAAC;EACrD;AAqBA,iBAAesT,wBACbhC,SACAhN,WACAqS,cAAqBC,QASf;AAAA,QARN;MACEzG;MACAkC;MACAtN,SAAAA;4BAKE,CAAA,IAAE6R;AAEN,QAAItS,UAASQ,SAASN,QAAQiK,IAAI,oBAAoB,GAAG;AACvD1C,+BAAyB;IAC1B;AAED,QAAIlG,WAAWvB,UAASQ,SAASN,QAAQ9B,IAAI,UAAU;AACvDpC,cAAUuF,UAAU,qDAAqD;AACzEA,eAAWuN,0BACTvN,UACA,IAAIwN,IAAI/B,QAAQ/M,GAAG,GACnBsD,QAAQ;AAEV,QAAIgP,mBAAmBxG,eAAezK,MAAMC,UAAUA,UAAU;MAC9DuJ,aAAa;IACd,CAAA;AAED,QAAInI,WAAW;AACb,UAAI6P,mBAAmB;AAEvB,UAAIxS,UAASQ,SAASN,QAAQiK,IAAI,yBAAyB,GAAG;AAE5DqI,2BAAmB;iBACVtQ,mBAAmBuQ,KAAKlR,QAAQ,GAAG;AAC5C,cAAMtB,MAAMxC,KAAKqH,QAAQ4N,UAAUnR,QAAQ;AAC3CiR;QAEEvS,IAAI0S,WAAWlQ,aAAalB,SAASoR;QAErCC,cAAc3S,IAAIgF,UAAU1B,QAAQ,KAAK;MAC5C;AAED,UAAIiP,kBAAkB;AACpB,YAAI/R,UAAS;AACXgC,uBAAalB,SAASd,QAAQc,QAAQ;QACvC,OAAM;AACLkB,uBAAalB,SAASjE,OAAOiE,QAAQ;QACtC;AACD;MACD;IACF;AAID6F,kCAA8B;AAE9B,QAAIyL,wBACFpS,aAAY,QAAQT,UAASQ,SAASN,QAAQiK,IAAI,iBAAiB,IAC/DlD,OAAciE,UACdjE,OAAcgE;AAIpB,QAAI;MAAEzJ;MAAYC;MAAYC;QAAgBJ,MAAMkF;AACpD,QACE,CAACqF,cACD,CAACkC,qBACDvM,cACAC,cACAC,aACA;AACAmK,mBAAauD,4BAA4B9N,MAAMkF,UAAU;IAC1D;AAKD,QAAI2I,mBAAmBtD,cAAckC;AACrC,QACE3M,kCAAkC+I,IAAInK,UAASQ,SAAST,MAAM,KAC9DoP,oBACAtE,iBAAiBsE,iBAAiB3N,UAAU,GAC5C;AACA,YAAM6H,gBAAgBwJ,uBAAuBN,kBAAkB;QAC7D1G,YAAUvL,SAAA,CAAA,GACL6O,kBAAgB;UACnB1N,YAAYF;SACb;;QAEDmF,oBAAoBS;QACpBiF,sBAAsBiG,eAClBhL,+BACArJ;MACL,CAAA;IACF,OAAM;AAGL,UAAIyO,qBAAqBmB,qBACvB2E,kBACA1G,UAAU;AAEZ,YAAMxC,gBAAgBwJ,uBAAuBN,kBAAkB;QAC7D9F;;QAEAsB;;QAEArH,oBAAoBS;QACpBiF,sBAAsBiG,eAClBhL,+BACArJ;MACL,CAAA;IACF;EACH;AAIA,iBAAe4Q,iBACbxB,MACA9L,QACA0L,SACAuC,eACArK,SACA4N,YAAyB;AAEzB,QAAInE;AACJ,QAAIoE,cAA0C,CAAA;AAC9C,QAAI;AACFpE,gBAAU,MAAMqE,qBACdxP,kBACA4J,MACA9L,QACA0L,SACAuC,eACArK,SACA4N,YACA3P,UACAF,mBAAkB;aAEbgQ,GAAG;AAGV1D,oBAAc7Q,QAASgH,OAAK;AAC1BqN,oBAAYrN,EAAEtD,MAAMgD,EAAE,IAAI;UACxBgI,MAAMC,WAAWpP;UACjBA,OAAOgV;;MAEX,CAAC;AACD,aAAOF;IACR;AAED,aAAS,CAACtF,SAASC,MAAM,KAAK3Q,OAAOC,QAAQ2R,OAAO,GAAG;AACrD,UAAIuE,mCAAmCxF,MAAM,GAAG;AAC9C,YAAIlN,WAAWkN,OAAOA;AACtBqF,oBAAYtF,OAAO,IAAI;UACrBL,MAAMC,WAAWrN;UACjBQ,UAAU2S,yCACR3S,UACAwM,SACAS,SACAvI,SACA3B,UACAM,OAAOK,oBAAoB;;MAGhC,OAAM;AACL6O,oBAAYtF,OAAO,IAAI,MAAM2F,sCAC3B1F,MAAM;MAET;IACF;AAED,WAAOqF;EACT;AAEA,iBAAe1C,+BACb/O,QACA4D,SACAqK,eACA8D,gBACArG,SAAgB;AAEhB,QAAIsG,iBAAiBhS,OAAM4D;AAG3B,QAAIqO,uBAAuB3E,iBACzB,UACAtN,QACA0L,SACAuC,eACArK,SACA,IAAI;AAGN,QAAIsO,wBAAwBnX,QAAQoX,IAClCJ,eAAeK,IAAI,OAAOxD,MAAK;AAC7B,UAAIA,EAAEhL,WAAWgL,EAAEgB,SAAShB,EAAE1T,YAAY;AACxC,YAAImS,UAAU,MAAMC,iBAClB,UACAtN,QACA2L,wBAAwBxP,KAAKqH,SAASoL,EAAEtE,MAAMsE,EAAE1T,WAAWI,MAAM,GACjE,CAACsT,EAAEgB,KAAK,GACRhB,EAAEhL,SACFgL,EAAE9S,GAAG;AAEP,YAAIsQ,SAASiB,QAAQuB,EAAEgB,MAAM9O,MAAMgD,EAAE;AAErC,eAAO;UAAE,CAAC8K,EAAE9S,GAAG,GAAGsQ;;MACnB,OAAM;AACL,eAAOrR,QAAQ8C,QAAQ;UACrB,CAAC+Q,EAAE9S,GAAG,GAAG;YACPgQ,MAAMC,WAAWpP;YACjBA,OAAO+G,uBAAuB,KAAK;cACjCC,UAAUiL,EAAEtE;aACb;UACa;QACjB,CAAA;MACF;IACH,CAAC,CAAC;AAGJ,QAAIuE,gBAAgB,MAAMoD;AAC1B,QAAInD,kBAAkB,MAAMoD,uBAAuBvW,OACjD,CAACC,KAAKX,MAAMQ,OAAOO,OAAOJ,KAAKX,CAAC,GAChC,CAAA,CAAE;AAGJ,UAAMF,QAAQoX,IAAI,CAChBE,iCACEzO,SACAiL,eACAnD,QAAQpQ,QACR0W,gBACAhS,OAAMuE,UAAU,GAElB+N,8BAA8B1O,SAASkL,gBAAgBiD,cAAc,CAAC,CACvE;AAED,WAAO;MACLlD;MACAC;;EAEJ;AAEA,WAAS7D,uBAAoB;AAE3B9E,6BAAyB;AAIzBC,4BAAwBhK,KAAK,GAAGgS,sBAAqB,CAAE;AAGvDzH,qBAAiBvJ,QAAQ,CAACpC,GAAGc,QAAO;AAClC,UAAIwK,iBAAiBuC,IAAI/M,GAAG,GAAG;AAC7BuK,8BAAsBhK,IAAIP,GAAG;AAC7B4S,qBAAa5S,GAAG;MACjB;IACH,CAAC;EACH;AAEA,WAASoU,mBACPpU,KACA8M,SACAH,MAAkC;AAAA,QAAlCA,SAAAA,QAAAA;AAAAA,aAAgC,CAAA;IAAE;AAElCzI,UAAMuF,SAASzG,IAAIhD,KAAK8M,OAAO;AAC/Bd,gBACE;MAAEvC,UAAU,IAAIC,IAAIxF,MAAMuF,QAAQ;IAAG,GACrC;MAAE0D,YAAYR,QAAQA,KAAKQ,eAAe;IAAM,CAAA;EAEpD;AAEA,WAAS0G,gBACP7T,KACAqQ,SACAxP,OACA8L,MAAkC;AAAA,QAAlCA,SAAA,QAAA;AAAAA,aAAgC,CAAA;IAAE;AAElC,QAAImF,gBAAgB/B,oBAAoB7L,MAAM4D,SAASuI,OAAO;AAC9D7D,kBAAcxM,GAAG;AACjBgM,gBACE;MACEtD,QAAQ;QACN,CAACoJ,cAAc9M,MAAMgD,EAAE,GAAGnH;;MAE5B4I,UAAU,IAAIC,IAAIxF,MAAMuF,QAAQ;IACjC,GACD;MAAE0D,YAAYR,QAAQA,KAAKQ,eAAe;IAAI,CAAE;EAEpD;AAEA,WAASsJ,WAAwBzW,KAAW;AAC1C,QAAIyG,OAAOC,mBAAmB;AAC5BoE,qBAAe9H,IAAIhD,MAAM8K,eAAe9J,IAAIhB,GAAG,KAAK,KAAK,CAAC;AAG1D,UAAI+K,gBAAgBgC,IAAI/M,GAAG,GAAG;AAC5B+K,wBAAgB7J,OAAOlB,GAAG;MAC3B;IACF;AACD,WAAOkE,MAAMuF,SAASzI,IAAIhB,GAAG,KAAK0E;EACpC;AAEA,WAAS8H,cAAcxM,KAAW;AAChC,QAAI8M,UAAU5I,MAAMuF,SAASzI,IAAIhB,GAAG;AAIpC,QACEwK,iBAAiBuC,IAAI/M,GAAG,KACxB,EAAE8M,WAAWA,QAAQ5I,UAAU,aAAayG,eAAeoC,IAAI/M,GAAG,IAClE;AACA4S,mBAAa5S,GAAG;IACjB;AACD6K,qBAAiB3J,OAAOlB,GAAG;AAC3B2K,mBAAezJ,OAAOlB,GAAG;AACzB4K,qBAAiB1J,OAAOlB,GAAG;AAC3B+K,oBAAgB7J,OAAOlB,GAAG;AAC1BuK,0BAAsBrJ,OAAOlB,GAAG;AAChCkE,UAAMuF,SAASvI,OAAOlB,GAAG;EAC3B;AAEA,WAAS0W,4BAA4B1W,KAAW;AAC9C,QAAIyG,OAAOC,mBAAmB;AAC5B,UAAIiQ,SAAS7L,eAAe9J,IAAIhB,GAAG,KAAK,KAAK;AAC7C,UAAI2W,SAAS,GAAG;AACd7L,uBAAe5J,OAAOlB,GAAG;AACzB+K,wBAAgBxK,IAAIP,GAAG;MACxB,OAAM;AACL8K,uBAAe9H,IAAIhD,KAAK2W,KAAK;MAC9B;IACF,OAAM;AACLnK,oBAAcxM,GAAG;IAClB;AACDgM,gBAAY;MAAEvC,UAAU,IAAIC,IAAIxF,MAAMuF,QAAQ;IAAC,CAAE;EACnD;AAEA,WAASmJ,aAAa5S,KAAW;AAC/B,QAAIZ,aAAaoL,iBAAiBxJ,IAAIhB,GAAG;AACzCpB,cAAUQ,YAA0CY,gCAAAA,GAAK;AACzDZ,eAAWuC,MAAK;AAChB6I,qBAAiBtJ,OAAOlB,GAAG;EAC7B;AAEA,WAAS4W,iBAAiBjJ,MAAc;AACtC,aAAS3N,OAAO2N,MAAM;AACpB,UAAIb,UAAU2J,WAAWzW,GAAG;AAC5B,UAAI+U,cAAcL,eAAe5H,QAAQxO,IAAI;AAC7C4F,YAAMuF,SAASzG,IAAIhD,KAAK+U,WAAW;IACpC;EACH;AAEA,WAASvC,yBAAsB;AAC7B,QAAIqE,WAAW,CAAA;AACf,QAAItE,kBAAkB;AACtB,aAASvS,OAAO4K,kBAAkB;AAChC,UAAIkC,UAAU5I,MAAMuF,SAASzI,IAAIhB,GAAG;AACpCpB,gBAAUkO,SAA8B9M,uBAAAA,GAAK;AAC7C,UAAI8M,QAAQ5I,UAAU,WAAW;AAC/B0G,yBAAiB1J,OAAOlB,GAAG;AAC3B6W,iBAASvW,KAAKN,GAAG;AACjBuS,0BAAkB;MACnB;IACF;AACDqE,qBAAiBC,QAAQ;AACzB,WAAOtE;EACT;AAEA,WAASgB,qBAAqBuD,UAAgB;AAC5C,QAAIC,aAAa,CAAA;AACjB,aAAS,CAAC/W,KAAKgI,EAAE,KAAK2C,gBAAgB;AACpC,UAAI3C,KAAK8O,UAAU;AACjB,YAAIhK,UAAU5I,MAAMuF,SAASzI,IAAIhB,GAAG;AACpCpB,kBAAUkO,SAA8B9M,uBAAAA,GAAK;AAC7C,YAAI8M,QAAQ5I,UAAU,WAAW;AAC/B0O,uBAAa5S,GAAG;AAChB2K,yBAAezJ,OAAOlB,GAAG;AACzB+W,qBAAWzW,KAAKN,GAAG;QACpB;MACF;IACF;AACD4W,qBAAiBG,UAAU;AAC3B,WAAOA,WAAWnR,SAAS;EAC7B;AAEA,WAASoR,WAAWhX,KAAayB,IAAmB;AAClD,QAAIwV,UAAmB/S,MAAMyF,SAAS3I,IAAIhB,GAAG,KAAK2E;AAElD,QAAIsG,iBAAiBjK,IAAIhB,GAAG,MAAMyB,IAAI;AACpCwJ,uBAAiBjI,IAAIhD,KAAKyB,EAAE;IAC7B;AAED,WAAOwV;EACT;AAEA,WAASxK,cAAczM,KAAW;AAChCkE,UAAMyF,SAASzI,OAAOlB,GAAG;AACzBiL,qBAAiB/J,OAAOlB,GAAG;EAC7B;AAGA,WAAS+L,cAAc/L,KAAakX,YAAmB;AACrD,QAAID,UAAU/S,MAAMyF,SAAS3I,IAAIhB,GAAG,KAAK2E;AAIzC/F,cACGqY,QAAQ/S,UAAU,eAAegT,WAAWhT,UAAU,aACpD+S,QAAQ/S,UAAU,aAAagT,WAAWhT,UAAU,aACpD+S,QAAQ/S,UAAU,aAAagT,WAAWhT,UAAU,gBACpD+S,QAAQ/S,UAAU,aAAagT,WAAWhT,UAAU,eACpD+S,QAAQ/S,UAAU,gBAAgBgT,WAAWhT,UAAU,aAAY,uCACjC+S,QAAQ/S,QAAK,SAAOgT,WAAWhT,KAAO;AAG7E,QAAIyF,WAAW,IAAID,IAAIxF,MAAMyF,QAAQ;AACrCA,aAAS3G,IAAIhD,KAAKkX,UAAU;AAC5BlL,gBAAY;MAAErC;IAAQ,CAAE;EAC1B;AAEA,WAAS+B,sBAAqByL,OAQ7B;AAAA,QAR8B;MAC7BxL;MACAC;MACA1C;IAKD,IAAAiO;AACC,QAAIlM,iBAAiBjJ,SAAS,GAAG;AAC/B;IACD;AAID,QAAIiJ,iBAAiBjJ,OAAO,GAAG;AAC7BwJ,cAAQ,OAAO,8CAA8C;IAC9D;AAED,QAAI5L,UAAUf,MAAMwD,KAAK4I,iBAAiBrL,QAAO,CAAE;AACnD,QAAI,CAAC6L,YAAY2L,eAAe,IAAIxX,QAAQA,QAAQgG,SAAS,CAAC;AAC9D,QAAIqR,UAAU/S,MAAMyF,SAAS3I,IAAIyK,UAAU;AAE3C,QAAIwL,WAAWA,QAAQ/S,UAAU,cAAc;AAG7C;IACD;AAID,QAAIkT,gBAAgB;MAAEzL;MAAiBC;MAAc1C;IAAe,CAAA,GAAG;AACrE,aAAOuC;IACR;EACH;AAEA,WAASiE,sBAAsB7H,UAAgB;AAC7C,QAAIhH,QAAQ+G,uBAAuB,KAAK;MAAEC;IAAU,CAAA;AACpD,QAAI0H,cAAcrJ,sBAAsBF;AACxC,QAAI;MAAE8B;MAAS9C;IAAK,IAAK+C,uBAAuBwH,WAAW;AAG3D+C,0BAAqB;AAErB,WAAO;MAAE7C,iBAAiB3H;MAAS9C;MAAOnE;;EAC5C;AAEA,WAASqQ,yBACPrJ,UACAkJ,gBAAyC;AAEzC,WAAO;MACLE,YAAYlB,oBAAoBgB,eAAeI,cAAc,EAAEnM,MAAMgD;MACrEnH,OAAO+G,uBAAuB,KAAK;QACjCoI,MAAM;QACNnI;QACAwP,SACEtG,eAAelQ,SAAS,QAAQ,aAAakQ,eAAelQ,QACxDkQ,eAAelQ,QACfyW,OAAOvG,eAAelQ,KAAK;OAClC;;EAEL;AAEA,WAASyR,sBACPiF,WAAwC;AAExC,QAAIC,oBAA8B,CAAA;AAClCxM,oBAAgB1J,QAAQ,CAACmW,KAAKpH,YAAW;AACvC,UAAI,CAACkH,aAAaA,UAAUlH,OAAO,GAAG;AAIpCoH,YAAI/V,OAAM;AACV8V,0BAAkBlX,KAAK+P,OAAO;AAC9BrF,wBAAgB9J,OAAOmP,OAAO;MAC/B;IACH,CAAC;AACD,WAAOmH;EACT;AAIA,WAASE,wBACPC,WACAC,aACAC,QAAwC;AAExC1Q,2BAAuBwQ;AACvBtQ,wBAAoBuQ;AACpBxQ,8BAA0ByQ,UAAU;AAKpC,QAAI,CAACvQ,yBAAyBpD,MAAMkF,eAAenF,iBAAiB;AAClEqD,8BAAwB;AACxB,UAAIwQ,IAAI7J,uBAAuB/J,MAAMC,UAAUD,MAAM4D,OAAO;AAC5D,UAAIgQ,KAAK,MAAM;AACb9L,oBAAY;UAAE3C,uBAAuByO;QAAC,CAAE;MACzC;IACF;AAED,WAAO,MAAK;AACV3Q,6BAAuB;AACvBE,0BAAoB;AACpBD,gCAA0B;;EAE9B;AAEA,WAAS2Q,aAAa5T,UAAoB2D,SAAiC;AACzE,QAAIV,yBAAyB;AAC3B,UAAIpH,MAAMoH,wBACRjD,UACA2D,QAAQwO,IAAKhO,OAAM0P,2BAA2B1P,GAAGpE,MAAMuE,UAAU,CAAC,CAAC;AAErE,aAAOzI,OAAOmE,SAASnE;IACxB;AACD,WAAOmE,SAASnE;EAClB;AAEA,WAASsP,mBACPnL,UACA2D,SAAiC;AAEjC,QAAIX,wBAAwBE,mBAAmB;AAC7C,UAAIrH,MAAM+X,aAAa5T,UAAU2D,OAAO;AACxCX,2BAAqBnH,GAAG,IAAIqH,kBAAiB;IAC9C;EACH;AAEA,WAAS4G,uBACP9J,UACA2D,SAAiC;AAEjC,QAAIX,sBAAsB;AACxB,UAAInH,MAAM+X,aAAa5T,UAAU2D,OAAO;AACxC,UAAIgQ,IAAI3Q,qBAAqBnH,GAAG;AAChC,UAAI,OAAO8X,MAAM,UAAU;AACzB,eAAOA;MACR;IACF;AACD,WAAO;EACT;AAEA,WAAS5P,cACPJ,SACAyH,aACA1H,UAAgB;AAEhB,QAAItB,6BAA6B;AAI/B,UAAIW,iBAAiB6F,IAAIlF,QAAQ,GAAG;AAClC,eAAO;UAAEM,QAAQ;UAAOL;;MACzB;AAED,UAAI,CAACA,SAAS;AACZ,YAAImQ,aAAaC,gBACf3I,aACA1H,UACA1B,UACA,IAAI;AAGN,eAAO;UAAEgC,QAAQ;UAAML,SAASmQ,cAAc,CAAA;;MAC/C,OAAM;AACL,YAAItY,OAAOgO,KAAK7F,QAAQ,CAAC,EAAEqQ,MAAM,EAAEvS,SAAS,GAAG;AAI7C,cAAIuL,iBAAiB+G,gBACnB3I,aACA1H,UACA1B,UACA,IAAI;AAEN,iBAAO;YAAEgC,QAAQ;YAAML,SAASqJ;;QACjC;MACF;IACF;AAED,WAAO;MAAEhJ,QAAQ;MAAOL,SAAS;;EACnC;AAiBA,iBAAekJ,eACblJ,SACAD,UACArI,QAAmB;AAEnB,QAAI2R,iBAAkDrJ;AACtD,WAAO,MAAM;AACX,UAAIsQ,WAAWlS,sBAAsB;AACrC,UAAIqJ,cAAcrJ,sBAAsBF;AACxC,UAAI;AACF,cAAMqS,sBACJ9R,6BACAsB,UACAsJ,gBACA5B,aACAxJ,UACAF,qBACAqF,oBACA1L,MAAM;eAEDqW,GAAG;AACV,eAAO;UAAE7F,MAAM;UAASnP,OAAOgV;UAAG1E;;MACnC,UAAA;AAOC,YAAIiH,UAAU;AACZpS,uBAAa,CAAC,GAAGA,UAAU;QAC5B;MACF;AAED,UAAIxG,OAAOyB,SAAS;AAClB,eAAO;UAAE+O,MAAM;;MAChB;AAED,UAAIsI,aAAa7Q,YAAY8H,aAAa1H,UAAU1B,QAAQ;AAC5D,UAAImS,YAAY;AACdC,uBAAe1Q,UAAUX,gBAAgB;AACzC,eAAO;UAAE8I,MAAM;UAAWlI,SAASwQ;;MACpC;AAED,UAAIE,oBAAoBN,gBACtB3I,aACA1H,UACA1B,UACA,IAAI;AAIN,UACE,CAACqS,qBACArH,eAAevL,WAAW4S,kBAAkB5S,UAC3CuL,eAAenI,MACb,CAACV,GAAGmQ,MAAMnQ,EAAEtD,MAAMgD,OAAOwQ,kBAAmBC,CAAC,EAAEzT,MAAMgD,EAAE,GAE3D;AACAuQ,uBAAe1Q,UAAUX,gBAAgB;AACzC,eAAO;UAAE8I,MAAM;UAAWlI,SAAS;;MACpC;AAEDqJ,uBAAiBqH;IAClB;EACH;AAEA,WAASD,eAAe/J,MAAckK,OAAkB;AACtD,QAAIA,MAAM1W,QAAQiF,yBAAyB;AACzC,UAAI0R,QAAQD,MAAME,OAAM,EAAGC,KAAI,EAAG5Y;AAClCyY,YAAMxX,OAAOyX,KAAK;IACnB;AACDD,UAAMnY,IAAIiO,IAAI;EAChB;AAEA,WAASsK,mBAAmBC,WAAoC;AAC9DhT,eAAW,CAAA;AACXG,yBAAqBD,0BACnB8S,WACAlT,qBACAjF,QACAmF,QAAQ;EAEZ;AAEA,WAASiT,YACP3I,SACA4I,UAA+B;AAE/B,QAAIb,WAAWlS,sBAAsB;AACrC,QAAIqJ,cAAcrJ,sBAAsBF;AACxCkT,oBACE7I,SACA4I,UACA1J,aACAxJ,UACAF,mBAAkB;AAQpB,QAAIuS,UAAU;AACZpS,mBAAa,CAAC,GAAGA,UAAU;AAC3BgG,kBAAY,CAAA,CAAE;IACf;EACH;AAEA/C,WAAS;IACP,IAAI9C,WAAQ;AACV,aAAOA;;IAET,IAAIM,SAAM;AACR,aAAOA;;IAET,IAAIvC,QAAK;AACP,aAAOA;;IAET,IAAIyB,SAAM;AACR,aAAOK;;IAET,IAAIV,SAAM;AACR,aAAOD;;IAET+F;IACA5J;IACAkW;IACAxJ;IACAyF;IACAzE;;;IAGAiK,YAAahL,QAAW9N,KAAKqH,QAAQyR,WAAWhL,EAAE;IAClDS,gBAAiBT,QAAW9N,KAAKqH,QAAQkH,eAAeT,EAAE;IAC1DsI;IACAjK,eAAekK;IACfpK;IACA0K;IACAvK;IACAuM;IACAI,2BAA2B5O;IAC3B6O,0BAA0BrO;;;IAG1B8N;;AAGF,SAAO7P;AACT;IAOaqQ,yBAAyBC,OAAO,UAAU;AAmqBvD,SAASC,uBACPC,MAAgC;AAEhC,SACEA,QAAQ,SACN,cAAcA,QAAQA,KAAKC,YAAY,QACtC,UAAUD,QAAQA,KAAKE,SAASC;AAEvC;AAEA,SAASC,YACPC,UACAC,SACAC,UACAC,iBACAC,IACAC,sBACAC,aACAC,UAA8B;AAE9B,MAAIC;AACJ,MAAIC;AACJ,MAAIH,aAAa;AAGfE,wBAAoB,CAAA;AACpB,aAASE,SAAST,SAAS;AACzBO,wBAAkBG,KAAKD,KAAK;AAC5B,UAAIA,MAAME,MAAMC,OAAOP,aAAa;AAClCG,2BAAmBC;AACnB;MACD;IACF;EACF,OAAM;AACLF,wBAAoBP;AACpBQ,uBAAmBR,QAAQA,QAAQa,SAAS,CAAC;EAC9C;AAGD,MAAIC,OAAOC,UACTZ,KAAKA,KAAK,KACVa,oBAAoBT,mBAAmBH,oBAAoB,GAC3Da,cAAclB,SAASmB,UAAUjB,QAAQ,KAAKF,SAASmB,UACvDZ,aAAa,MAAM;AAMrB,MAAIH,MAAM,MAAM;AACdW,SAAKK,SAASpB,SAASoB;AACvBL,SAAKM,OAAOrB,SAASqB;EACtB;AAGD,OACGjB,MAAM,QAAQA,OAAO,MAAMA,OAAO,QACnCK,oBACAA,iBAAiBG,MAAMU,SACvB,CAACC,mBAAmBR,KAAKK,MAAM,GAC/B;AACAL,SAAKK,SAASL,KAAKK,SACfL,KAAKK,OAAOI,QAAQ,OAAO,SAAS,IACpC;EACL;AAMD,MAAIrB,mBAAmBD,aAAa,KAAK;AACvCa,SAAKI,WACHJ,KAAKI,aAAa,MAAMjB,WAAWuB,UAAU,CAACvB,UAAUa,KAAKI,QAAQ,CAAC;EACzE;AAED,SAAOO,WAAWX,IAAI;AACxB;AAIA,SAASY,yBACPC,qBACAC,WACAd,MACApB,MAAiC;AAOjC,MAAI,CAACA,QAAQ,CAACD,uBAAuBC,IAAI,GAAG;AAC1C,WAAO;MAAEoB;;EACV;AAED,MAAIpB,KAAKmC,cAAc,CAACC,cAAcpC,KAAKmC,UAAU,GAAG;AACtD,WAAO;MACLf;MACAiB,OAAOC,uBAAuB,KAAK;QAAEC,QAAQvC,KAAKmC;OAAY;;EAEjE;AAED,MAAIK,sBAAsBA,OAAO;IAC/BpB;IACAiB,OAAOC,uBAAuB,KAAK;MAAEG,MAAM;KAAgB;EAC5D;AAGD,MAAIC,gBAAgB1C,KAAKmC,cAAc;AACvC,MAAIA,aAAaF,sBACZS,cAAcC,YAAW,IACzBD,cAAcE,YAAW;AAC9B,MAAIC,aAAaC,kBAAkB1B,IAAI;AAEvC,MAAIpB,KAAKE,SAASC,QAAW;AAC3B,QAAIH,KAAK+C,gBAAgB,cAAc;AAErC,UAAI,CAACC,iBAAiBb,UAAU,GAAG;AACjC,eAAOK,oBAAmB;MAC3B;AAED,UAAIS,OACF,OAAOjD,KAAKE,SAAS,WACjBF,KAAKE,OACLF,KAAKE,gBAAgBgD,YACrBlD,KAAKE,gBAAgBiD;;QAErBC,MAAMC,KAAKrD,KAAKE,KAAKoD,QAAO,CAAE,EAAEC,OAC9B,CAACC,KAAGC,UAAA;AAAA,cAAE,CAACC,MAAMC,KAAK,IAACF;AAAA,iBAAA,KAAQD,MAAME,OAAI,MAAIC,QAAK;WAC9C,EAAE;UAEJC,OAAO5D,KAAKE,IAAI;AAEtB,aAAO;QACLkB;QACAyC,YAAY;UACV1B;UACAU;UACAE,aAAa/C,KAAK+C;UAClB9C,UAAUE;UACV2D,MAAM3D;UACN8C;QACD;;IAEJ,WAAUjD,KAAK+C,gBAAgB,oBAAoB;AAElD,UAAI,CAACC,iBAAiBb,UAAU,GAAG;AACjC,eAAOK,oBAAmB;MAC3B;AAED,UAAI;AACF,YAAIsB,QACF,OAAO9D,KAAKE,SAAS,WAAW6D,KAAKC,MAAMhE,KAAKE,IAAI,IAAIF,KAAKE;AAE/D,eAAO;UACLkB;UACAyC,YAAY;YACV1B;YACAU;YACAE,aAAa/C,KAAK+C;YAClB9C,UAAUE;YACV2D,MAAAA;YACAb,MAAM9C;UACP;;eAEI8D,GAAG;AACV,eAAOzB,oBAAmB;MAC3B;IACF;EACF;AAED0B,YACE,OAAOhB,aAAa,YACpB,+CAA+C;AAGjD,MAAIiB;AACJ,MAAIlE;AAEJ,MAAID,KAAKC,UAAU;AACjBkE,mBAAeC,8BAA8BpE,KAAKC,QAAQ;AAC1DA,eAAWD,KAAKC;EACjB,WAAUD,KAAKE,gBAAgBgD,UAAU;AACxCiB,mBAAeC,8BAA8BpE,KAAKE,IAAI;AACtDD,eAAWD,KAAKE;EACjB,WAAUF,KAAKE,gBAAgBiD,iBAAiB;AAC/CgB,mBAAenE,KAAKE;AACpBD,eAAWoE,8BAA8BF,YAAY;EACtD,WAAUnE,KAAKE,QAAQ,MAAM;AAC5BiE,mBAAe,IAAIhB,gBAAe;AAClClD,eAAW,IAAIiD,SAAQ;EACxB,OAAM;AACL,QAAI;AACFiB,qBAAe,IAAIhB,gBAAgBnD,KAAKE,IAAI;AAC5CD,iBAAWoE,8BAA8BF,YAAY;aAC9CF,GAAG;AACV,aAAOzB,oBAAmB;IAC3B;EACF;AAED,MAAIqB,aAAyB;IAC3B1B;IACAU;IACAE,aACG/C,QAAQA,KAAK+C,eAAgB;IAChC9C;IACA6D,MAAM3D;IACN8C,MAAM9C;;AAGR,MAAI6C,iBAAiBa,WAAW1B,UAAU,GAAG;AAC3C,WAAO;MAAEf;MAAMyC;;EAChB;AAGD,MAAIS,aAAaC,UAAUnD,IAAI;AAI/B,MAAIc,aAAaoC,WAAW7C,UAAUG,mBAAmB0C,WAAW7C,MAAM,GAAG;AAC3E0C,iBAAaK,OAAO,SAAS,EAAE;EAChC;AACDF,aAAW7C,SAAM,MAAO0C;AAExB,SAAO;IAAE/C,MAAMW,WAAWuC,UAAU;IAAGT;;AACzC;AAIA,SAASY,8BACPnE,SACAoE,YAAkB;AAElB,MAAIC,kBAAkBrE;AACtB,MAAIoE,YAAY;AACd,QAAI/C,QAAQrB,QAAQsE,UAAWC,OAAMA,EAAE5D,MAAMC,OAAOwD,UAAU;AAC9D,QAAI/C,SAAS,GAAG;AACdgD,wBAAkBrE,QAAQwE,MAAM,GAAGnD,KAAK;IACzC;EACF;AACD,SAAOgD;AACT;AAEA,SAASI,iBACPC,SACAC,OACA3E,SACAuD,YACAxD,UACA6E,eACAC,6BACAC,wBACAC,yBACAC,uBACAC,iBACAC,kBACAC,kBACAC,aACAnF,UACAoF,qBAAyC;AAEzC,MAAIC,eAAeD,sBACfE,cAAcF,oBAAoB,CAAC,CAAC,IAClCA,oBAAoB,CAAC,EAAEtD,QACvBsD,oBAAoB,CAAC,EAAEG,OACzB3F;AACJ,MAAI4F,aAAaf,QAAQgB,UAAUf,MAAM5E,QAAQ;AACjD,MAAI4F,UAAUjB,QAAQgB,UAAU3F,QAAQ;AAGxC,MAAIqE,aACFiB,uBAAuBE,cAAcF,oBAAoB,CAAC,CAAC,IACvDA,oBAAoB,CAAC,IACrBxF;AACN,MAAIwE,kBAAkBD,aAClBD,8BAA8BnE,SAASoE,UAAU,IACjDpE;AAKJ,MAAI4F,eAAeP,sBACfA,oBAAoB,CAAC,EAAEQ,aACvBhG;AACJ,MAAIiG,yBACFjB,+BAA+Be,gBAAgBA,gBAAgB;AAEjE,MAAIG,oBAAoB1B,gBAAgB2B,OAAO,CAACvF,OAAOY,UAAS;AAC9D,QAAI;MAAEV;IAAO,IAAGF;AAChB,QAAIE,MAAMsF,MAAM;AAEd,aAAO;IACR;AAED,QAAItF,MAAMuF,UAAU,MAAM;AACxB,aAAO;IACR;AAED,QAAItB,eAAe;AACjB,UAAI,OAAOjE,MAAMuF,WAAW,cAAcvF,MAAMuF,OAAOC,SAAS;AAC9D,eAAO;MACR;AACD,aACExB,MAAMyB,WAAWzF,MAAMC,EAAE,MAAMf;OAE9B,CAAC8E,MAAM0B,UAAU1B,MAAM0B,OAAO1F,MAAMC,EAAE,MAAMf;IAEhD;AAGD,QACEyG,YAAY3B,MAAMyB,YAAYzB,MAAM3E,QAAQqB,KAAK,GAAGZ,KAAK,KACzDsE,wBAAwBwB,KAAM3F,QAAOA,OAAOH,MAAME,MAAMC,EAAE,GAC1D;AACA,aAAO;IACR;AAMD,QAAI4F,oBAAoB7B,MAAM3E,QAAQqB,KAAK;AAC3C,QAAIoF,iBAAiBhG;AAErB,WAAOiG,uBAAuBjG,OAAKkG,SAAA;MACjClB;MACAmB,eAAeJ,kBAAkBK;MACjClB;MACAmB,YAAYL,eAAeI;IAAM,GAC9BtD,YAAU;MACb+B;MACAM;MACAmB,yBAAyBjB,yBACrB;;QAEAhB,0BACAW,WAAWvE,WAAWuE,WAAWtE,WAC/BwE,QAAQzE,WAAWyE,QAAQxE;QAE7BsE,WAAWtE,WAAWwE,QAAQxE,UAC9B6F,mBAAmBR,mBAAmBC,cAAc;;IAAC,CAAA,CAC1D;EACH,CAAC;AAGD,MAAIQ,uBAA8C,CAAA;AAClD/B,mBAAiBgC,QAAQ,CAACC,GAAGC,QAAO;AAMlC,QACExC,iBACA,CAAC5E,QAAQuG,KAAMhC,OAAMA,EAAE5D,MAAMC,OAAOuG,EAAEE,OAAO,KAC7CpC,gBAAgBqC,IAAIF,GAAG,GACvB;AACA;IACD;AAED,QAAIG,iBAAiBC,YAAYpC,aAAa+B,EAAErG,MAAMb,QAAQ;AAM9D,QAAI,CAACsH,gBAAgB;AACnBN,2BAAqBvG,KAAK;QACxB0G;QACAC,SAASF,EAAEE;QACXvG,MAAMqG,EAAErG;QACRd,SAAS;QACTS,OAAO;QACPgH,YAAY;MACb,CAAA;AACD;IACD;AAKD,QAAIC,UAAU/C,MAAMgD,SAASC,IAAIR,GAAG;AACpC,QAAIS,eAAeC,eAAeP,gBAAgBJ,EAAErG,IAAI;AAExD,QAAIiH,mBAAmB;AACvB,QAAI5C,iBAAiBmC,IAAIF,GAAG,GAAG;AAE7BW,yBAAmB;eACV/C,sBAAsBsC,IAAIF,GAAG,GAAG;AAEzCpC,4BAAsBgD,OAAOZ,GAAG;AAChCW,yBAAmB;IACpB,WACCL,WACAA,QAAQ/C,UAAU,UAClB+C,QAAQlC,SAAS3F,QACjB;AAIAkI,yBAAmBjD;IACpB,OAAM;AAGLiD,yBAAmBrB,uBAAuBmB,cAAYlB,SAAA;QACpDlB;QACAmB,eAAejC,MAAM3E,QAAQ2E,MAAM3E,QAAQa,SAAS,CAAC,EAAEgG;QACvDlB;QACAmB,YAAY9G,QAAQA,QAAQa,SAAS,CAAC,EAAEgG;MAAM,GAC3CtD,YAAU;QACb+B;QACAM;QACAmB,yBAAyBjB,yBACrB,QACAhB;MAAsB,CAAA,CAC3B;IACF;AAED,QAAIiD,kBAAkB;AACpBd,2BAAqBvG,KAAK;QACxB0G;QACAC,SAASF,EAAEE;QACXvG,MAAMqG,EAAErG;QACRd,SAASuH;QACT9G,OAAOoH;QACPJ,YAAY,IAAIQ,gBAAe;MAChC,CAAA;IACF;EACH,CAAC;AAED,SAAO,CAAClC,mBAAmBkB,oBAAoB;AACjD;AAEA,SAASX,YACP4B,mBACAC,cACA1H,OAA6B;AAE7B,MAAI2H;;IAEF,CAACD;IAED1H,MAAME,MAAMC,OAAOuH,aAAaxH,MAAMC;;AAIxC,MAAIyH,gBAAgBH,kBAAkBzH,MAAME,MAAMC,EAAE,MAAMf;AAG1D,SAAOuI,SAASC;AAClB;AAEA,SAASrB,mBACPmB,cACA1H,OAA6B;AAE7B,MAAI6H,cAAcH,aAAaxH,MAAMG;AACrC;;IAEEqH,aAAajH,aAAaT,MAAMS;;IAG/BoH,eAAe,QACdA,YAAYC,SAAS,GAAG,KACxBJ,aAAatB,OAAO,GAAG,MAAMpG,MAAMoG,OAAO,GAAG;;AAEnD;AAEA,SAASH,uBACP8B,aACAC,KAAiC;AAEjC,MAAID,YAAY7H,MAAMoH,kBAAkB;AACtC,QAAIW,cAAcF,YAAY7H,MAAMoH,iBAAiBU,GAAG;AACxD,QAAI,OAAOC,gBAAgB,WAAW;AACpC,aAAOA;IACR;EACF;AAED,SAAOD,IAAI1B;AACb;AAMA,eAAe4B,sBACbC,6BACA9H,MACAd,SACA6I,QACAC,UACAC,qBACAC,sBAIAC,QAAmB;AAEnB,MAAI7B,MAAM,CAACtG,MAAM,GAAGd,QAAQkJ,IAAK3E,OAAMA,EAAE5D,MAAMC,EAAE,CAAC,EAAEuI,KAAK,GAAG;AAC5D,MAAI;AACF,QAAIC,UAAUJ,qBAAqBpB,IAAIR,GAAG;AAC1C,QAAI,CAACgC,SAAS;AACZA,gBAAUR,4BAA4B;QACpC9H;QACAd;QACAqJ,OAAOA,CAAChC,SAASiC,aAAY;AAC3B,cAAI,CAACL,OAAOM,SAAS;AACnBC,4BACEnC,SACAiC,UACAT,QACAC,UACAC,mBAAkB;UAErB;QACH;MACD,CAAA;AACDC,2BAAqBS,IAAIrC,KAAKgC,OAAO;IACtC;AAED,QAAIA,WAAWM,UAAiCN,OAAO,GAAG;AACxD,YAAMA;IACP;EACF,UAAA;AACCJ,yBAAqBhB,OAAOZ,GAAG;EAChC;AACH;AAEA,SAASoC,gBACPnC,SACAiC,UACAlE,aACA0D,UACAC,qBAA8C;AAE9C,MAAI1B,SAAS;AAAA,QAAAsC;AACX,QAAIhJ,QAAQmI,SAASzB,OAAO;AAC5BzD,cACEjD,OACoD0G,sDAAAA,OAAS;AAE/D,QAAIuC,eAAeC,0BACjBP,UACAP,qBACA,CAAC1B,SAAS,SAAS/D,SAAOqG,kBAAAhJ,MAAM2I,aAAQ,OAAA,SAAdK,gBAAgB9I,WAAU,GAAG,CAAC,GACxDiI,QAAQ;AAEV,QAAInI,MAAM2I,UAAU;AAClB3I,YAAM2I,SAAS5I,KAAK,GAAGkJ,YAAY;IACpC,OAAM;AACLjJ,YAAM2I,WAAWM;IAClB;EACF,OAAM;AACL,QAAIA,eAAeC,0BACjBP,UACAP,qBACA,CAAC,SAASzF,OAAO8B,YAAYvE,UAAU,GAAG,CAAC,GAC3CiI,QAAQ;AAEV1D,gBAAY1E,KAAK,GAAGkJ,YAAY;EACjC;AACH;AAOA,eAAeE,oBACbnJ,OACAoI,qBACAD,UAAuB;AAEvB,MAAI,CAACnI,MAAMsF,MAAM;AACf;EACD;AAED,MAAI8D,YAAY,MAAMpJ,MAAMsF,KAAI;AAKhC,MAAI,CAACtF,MAAMsF,MAAM;AACf;EACD;AAED,MAAI+D,gBAAgBlB,SAASnI,MAAMC,EAAE;AACrCgD,YAAUoG,eAAe,4BAA4B;AAUrD,MAAIC,eAAoC,CAAA;AACxC,WAASC,qBAAqBH,WAAW;AACvC,QAAII,mBACFH,cAAcE,iBAA+C;AAE/D,QAAIE,8BACFD,qBAAqBtK;;IAGrBqK,sBAAsB;AAExBG,YACE,CAACD,6BACD,YAAUJ,cAAcpJ,KAAE,8BAA4BsJ,oBAAiB,mFAEzCA,8BAAAA,oBAAiB,qBAAoB;AAGrE,QACE,CAACE,+BACD,CAACE,mBAAmBhD,IAAI4C,iBAAsC,GAC9D;AACAD,mBAAaC,iBAAiB,IAC5BH,UAAUG,iBAA2C;IACxD;EACF;AAIDK,SAAOC,OAAOR,eAAeC,YAAY;AAKzCM,SAAOC,OAAOR,eAAarD,SAKtBoC,CAAAA,GAAAA,oBAAmBiB,aAAa,GAAC;IACpC/D,MAAMpG;EAAS,CAAA,CAChB;AACH;AAGA,eAAe4K,oBAAmBC,OAEP;AAAA,MAFQ;IACjC1K;EACyB,IAAA0K;AACzB,MAAIC,gBAAgB3K,QAAQgG,OAAQzB,OAAMA,EAAEqG,UAAU;AACtD,MAAIC,UAAU,MAAMC,QAAQC,IAAIJ,cAAczB,IAAK3E,OAAMA,EAAEyG,QAAO,CAAE,CAAC;AACrE,SAAOH,QAAQ5H,OACb,CAACC,KAAK+H,QAAQC,MACZX,OAAOC,OAAOtH,KAAK;IAAE,CAACyH,cAAcO,CAAC,EAAEvK,MAAMC,EAAE,GAAGqK;EAAM,CAAE,GAC5D,CAAA,CAAE;AAEN;AAEA,eAAeE,qBACbC,kBACAjJ,MACAwC,OACA0G,SACAV,eACA3K,SACAsL,YACAxC,UACAC,qBACAwC,gBAAwB;AAExB,MAAIC,+BAA+BxL,QAAQkJ,IAAK3E,OAC9CA,EAAE5D,MAAMsF,OACJ6D,oBAAoBvF,EAAE5D,OAAOoI,qBAAoBD,QAAQ,IACzDjJ,MAAS;AAGf,MAAI4L,YAAYzL,QAAQkJ,IAAI,CAACzI,OAAOyK,MAAK;AACvC,QAAIQ,mBAAmBF,6BAA6BN,CAAC;AACrD,QAAIN,aAAaD,cAAcpE,KAAMhC,OAAMA,EAAE5D,MAAMC,OAAOH,MAAME,MAAMC,EAAE;AAKxE,QAAIoK,UAAwC,OAAOW,oBAAmB;AACpE,UACEA,mBACAN,QAAQpJ,WAAW,UAClBxB,MAAME,MAAMsF,QAAQxF,MAAME,MAAMuF,SACjC;AACA0E,qBAAa;MACd;AACD,aAAOA,aACHgB,mBACEzJ,MACAkJ,SACA5K,OACAiL,kBACAC,iBACAJ,cAAc,IAEhBT,QAAQE,QAAQ;QAAE7I,MAAM0J,WAAWrG;QAAMyF,QAAQpL;MAAS,CAAE;;AAGlE,WAAA8G,SAAA,CAAA,GACKlG,OAAK;MACRmK;MACAI;IAAO,CAAA;EAEX,CAAC;AAKD,MAAIH,UAAU,MAAMO,iBAAiB;IACnCpL,SAASyL;IACTJ;IACAxE,QAAQ7G,QAAQ,CAAC,EAAE6G;IACnByE;IACAQ,SAASP;EACV,CAAA;AAKD,MAAI;AACF,UAAMT,QAAQC,IAAIS,4BAA4B;WACvC7H,GAAG;EACV;AAGF,SAAOkH;AACT;AAGA,eAAee,mBACbzJ,MACAkJ,SACA5K,OACAiL,kBACAC,iBACAI,eAAuB;AAEvB,MAAId;AACJ,MAAIe;AAEJ,MAAIC,aACFC,aAC+B;AAE/B,QAAIC;AAGJ,QAAIC,eAAe,IAAItB,QAA4B,CAACuB,GAAGC,MAAOH,SAASG,CAAE;AACzEN,eAAWA,MAAMG,OAAM;AACvBd,YAAQpC,OAAOsD,iBAAiB,SAASP,QAAQ;AAEjD,QAAIQ,gBAAiBC,SAAiB;AACpC,UAAI,OAAOP,YAAY,YAAY;AACjC,eAAOpB,QAAQqB,OACb,IAAIO,MACF,sEAAA,MACMvK,OAAI,iBAAe1B,MAAME,MAAMC,KAAE,IAAG,CAC3C;MAEJ;AACD,aAAOsL,QACL;QACEb;QACAxE,QAAQpG,MAAMoG;QACdiF,SAASC;MACV,GACD,GAAIU,QAAQ5M,SAAY,CAAC4M,GAAG,IAAI,CAAA,CAAG;;AAIvC,QAAIE,kBAA+C,YAAW;AAC5D,UAAI;AACF,YAAIC,MAAM,OAAOjB,kBACbA,gBAAiBc,SAAiBD,cAAcC,GAAG,CAAC,IACpDD,cAAa;AACjB,eAAO;UAAErK,MAAM;UAAQ8I,QAAQ2B;;eACxBjJ,GAAG;AACV,eAAO;UAAExB,MAAM;UAAS8I,QAAQtH;;MACjC;IACH,GAAC;AAED,WAAOmH,QAAQ+B,KAAK,CAACF,gBAAgBP,YAAY,CAAC;;AAGpD,MAAI;AACF,QAAIF,UAAUzL,MAAME,MAAMwB,IAAI;AAG9B,QAAIuJ,kBAAkB;AACpB,UAAIQ,SAAS;AAEX,YAAIY;AACJ,YAAI,CAACzJ,KAAK,IAAI,MAAMyH,QAAQC,IAAI;;;;UAI9BkB,WAAWC,OAAO,EAAEa,MAAOpJ,OAAK;AAC9BmJ,2BAAenJ;UACjB,CAAC;UACD+H;QAAgB,CACjB;AACD,YAAIoB,iBAAiBjN,QAAW;AAC9B,gBAAMiN;QACP;AACD7B,iBAAS5H;MACV,OAAM;AAEL,cAAMqI;AAENQ,kBAAUzL,MAAME,MAAMwB,IAAI;AAC1B,YAAI+J,SAAS;AAIXjB,mBAAS,MAAMgB,WAAWC,OAAO;QAClC,WAAU/J,SAAS,UAAU;AAC5B,cAAI6K,MAAM,IAAIC,IAAI5B,QAAQ2B,GAAG;AAC7B,cAAI9L,WAAW8L,IAAI9L,WAAW8L,IAAI7L;AAClC,gBAAMa,uBAAuB,KAAK;YAChCC,QAAQoJ,QAAQpJ;YAChBf;YACAmG,SAAS5G,MAAME,MAAMC;UACtB,CAAA;QACF,OAAM;AAGL,iBAAO;YAAEuB,MAAM0J,WAAWrG;YAAMyF,QAAQpL;;QACzC;MACF;IACF,WAAU,CAACqM,SAAS;AACnB,UAAIc,MAAM,IAAIC,IAAI5B,QAAQ2B,GAAG;AAC7B,UAAI9L,WAAW8L,IAAI9L,WAAW8L,IAAI7L;AAClC,YAAMa,uBAAuB,KAAK;QAChCd;MACD,CAAA;IACF,OAAM;AACL+J,eAAS,MAAMgB,WAAWC,OAAO;IAClC;AAEDtI,cACEqH,OAAOA,WAAWpL,QAClB,kBAAesC,SAAS,WAAW,cAAc,cAC3C1B,iBAAAA,MAAAA,MAAME,MAAMC,KAA8CuB,8CAAAA,OAAS,QAAA,4CACzB;WAE3CwB,GAAG;AAIV,WAAO;MAAExB,MAAM0J,WAAW9J;MAAOkJ,QAAQtH;;EAC1C,UAAA;AACC,QAAIqI,UAAU;AACZX,cAAQpC,OAAOiE,oBAAoB,SAASlB,QAAQ;IACrD;EACF;AAED,SAAOf;AACT;AAEA,eAAekC,sCACbC,oBAAsC;AAEtC,MAAI;IAAEnC;IAAQ9I;EAAM,IAAGiL;AAEvB,MAAIC,WAAWpC,MAAM,GAAG;AACtB,QAAIzF;AAEJ,QAAI;AACF,UAAI8H,cAAcrC,OAAOsC,QAAQ3F,IAAI,cAAc;AAGnD,UAAI0F,eAAe,wBAAwBE,KAAKF,WAAW,GAAG;AAC5D,YAAIrC,OAAOrL,QAAQ,MAAM;AACvB4F,iBAAO;QACR,OAAM;AACLA,iBAAO,MAAMyF,OAAOzH,KAAI;QACzB;MACF,OAAM;AACLgC,eAAO,MAAMyF,OAAOtI,KAAI;MACzB;aACMgB,GAAG;AACV,aAAO;QAAExB,MAAM0J,WAAW9J;QAAOA,OAAO4B;;IACzC;AAED,QAAIxB,SAAS0J,WAAW9J,OAAO;AAC7B,aAAO;QACLI,MAAM0J,WAAW9J;QACjBA,OAAO,IAAI0L,kBAAkBxC,OAAOyC,QAAQzC,OAAO0C,YAAYnI,IAAI;QACnEK,YAAYoF,OAAOyC;QACnBH,SAAStC,OAAOsC;;IAEnB;AAED,WAAO;MACLpL,MAAM0J,WAAWrG;MACjBA;MACAK,YAAYoF,OAAOyC;MACnBH,SAAStC,OAAOsC;;EAEnB;AAED,MAAIpL,SAAS0J,WAAW9J,OAAO;AAC7B,QAAI6L,uBAAuB3C,MAAM,GAAG;AAAA,UAAA4C;AAClC,UAAI5C,OAAOzF,gBAAgBkH,OAAO;AAAA,YAAAoB;AAChC,eAAO;UACL3L,MAAM0J,WAAW9J;UACjBA,OAAOkJ,OAAOzF;UACdK,aAAUiI,eAAE7C,OAAO8C,SAAI,OAAA,SAAXD,aAAaJ;;MAE5B;AAGDzC,eAAS,IAAIwC,oBACXI,gBAAA5C,OAAO8C,SAAI,OAAA,SAAXF,cAAaH,WAAU,KACvB7N,QACAoL,OAAOzF,IAAI;IAEd;AACD,WAAO;MACLrD,MAAM0J,WAAW9J;MACjBA,OAAOkJ;MACPpF,YAAYmI,qBAAqB/C,MAAM,IAAIA,OAAOyC,SAAS7N;;EAE9D;AAED,MAAIoO,eAAehD,MAAM,GAAG;AAAA,QAAAiD,eAAAC;AAC1B,WAAO;MACLhM,MAAM0J,WAAWuC;MACjBC,cAAcpD;MACdpF,aAAUqI,gBAAEjD,OAAO8C,SAAI,OAAA,SAAXG,cAAaR;MACzBH,WAASY,gBAAAlD,OAAO8C,SAAPI,OAAAA,SAAAA,cAAaZ,YAAW,IAAIe,QAAQrD,OAAO8C,KAAKR,OAAO;;EAEnE;AAED,MAAIK,uBAAuB3C,MAAM,GAAG;AAAA,QAAAsD,eAAAC;AAClC,WAAO;MACLrM,MAAM0J,WAAWrG;MACjBA,MAAMyF,OAAOzF;MACbK,aAAU0I,gBAAEtD,OAAO8C,SAAI,OAAA,SAAXQ,cAAab;MACzBH,UAASiB,gBAAAvD,OAAO8C,SAAI,QAAXS,cAAajB,UAClB,IAAIe,QAAQrD,OAAO8C,KAAKR,OAAO,IAC/B1N;;EAEP;AAED,SAAO;IAAEsC,MAAM0J,WAAWrG;IAAMA,MAAMyF;;AACxC;AAGA,SAASwD,yCACPC,UACArD,SACAhE,SACArH,SACAC,UACAG,sBAA6B;AAE7B,MAAIL,WAAW2O,SAASnB,QAAQ3F,IAAI,UAAU;AAC9ChE,YACE7D,UACA,4EAA4E;AAG9E,MAAI,CAAC4O,mBAAmBnB,KAAKzN,QAAQ,GAAG;AACtC,QAAI6O,iBAAiB5O,QAAQwE,MAC3B,GACAxE,QAAQsE,UAAWC,OAAMA,EAAE5D,MAAMC,OAAOyG,OAAO,IAAI,CAAC;AAEtDtH,eAAWD,YACT,IAAImN,IAAI5B,QAAQ2B,GAAG,GACnB4B,gBACA3O,UACA,MACAF,UACAK,oBAAoB;AAEtBsO,aAASnB,QAAQ9D,IAAI,YAAY1J,QAAQ;EAC1C;AAED,SAAO2O;AACT;AAEA,SAASG,0BACP9O,UACA0F,YACAxF,UAAgB;AAEhB,MAAI0O,mBAAmBnB,KAAKzN,QAAQ,GAAG;AAErC,QAAI+O,qBAAqB/O;AACzB,QAAIiN,MAAM8B,mBAAmBC,WAAW,IAAI,IACxC,IAAI9B,IAAIxH,WAAWuJ,WAAWF,kBAAkB,IAChD,IAAI7B,IAAI6B,kBAAkB;AAC9B,QAAIG,iBAAiBhO,cAAc+L,IAAI9L,UAAUjB,QAAQ,KAAK;AAC9D,QAAI+M,IAAIkC,WAAWzJ,WAAWyJ,UAAUD,gBAAgB;AACtD,aAAOjC,IAAI9L,WAAW8L,IAAI7L,SAAS6L,IAAI5L;IACxC;EACF;AACD,SAAOrB;AACT;AAKA,SAASoP,wBACPzK,SACA3E,UACAkJ,QACA1F,YAAuB;AAEvB,MAAIyJ,MAAMtI,QAAQgB,UAAUlD,kBAAkBzC,QAAQ,CAAC,EAAEqP,SAAQ;AACjE,MAAIrB,OAAoB;IAAE9E;;AAE1B,MAAI1F,cAAcb,iBAAiBa,WAAW1B,UAAU,GAAG;AACzD,QAAI;MAAEA;MAAYY;IAAa,IAAGc;AAIlCwK,SAAK9L,SAASJ,WAAWQ,YAAW;AAEpC,QAAII,gBAAgB,oBAAoB;AACtCsL,WAAKR,UAAU,IAAIe,QAAQ;QAAE,gBAAgB7L;MAAa,CAAA;AAC1DsL,WAAKnO,OAAO6D,KAAK4L,UAAU9L,WAAWC,IAAI;IAC3C,WAAUf,gBAAgB,cAAc;AAEvCsL,WAAKnO,OAAO2D,WAAWZ;eAEvBF,gBAAgB,uCAChBc,WAAW5D,UACX;AAEAoO,WAAKnO,OAAOkE,8BAA8BP,WAAW5D,QAAQ;IAC9D,OAAM;AAELoO,WAAKnO,OAAO2D,WAAW5D;IACxB;EACF;AAED,SAAO,IAAI2P,QAAQtC,KAAKe,IAAI;AAC9B;AAEA,SAASjK,8BAA8BnE,UAAkB;AACvD,MAAIkE,eAAe,IAAIhB,gBAAe;AAEtC,WAAS,CAACuE,KAAK/D,KAAK,KAAK1D,SAASqD,QAAO,GAAI;AAE3Ca,iBAAaK,OAAOkD,KAAK,OAAO/D,UAAU,WAAWA,QAAQA,MAAMD,IAAI;EACxE;AAED,SAAOS;AACT;AAEA,SAASE,8BACPF,cAA6B;AAE7B,MAAIlE,WAAW,IAAIiD,SAAQ;AAC3B,WAAS,CAACwE,KAAK/D,KAAK,KAAKQ,aAAab,QAAO,GAAI;AAC/CrD,aAASuE,OAAOkD,KAAK/D,KAAK;EAC3B;AACD,SAAO1D;AACT;AAEA,SAAS4P,uBACPvP,SACA6K,SACAxF,qBACAmK,iBACAC,yBAAgC;AAQhC,MAAIrJ,aAAwC,CAAA;AAC5C,MAAIC,SAAuC;AAC3C,MAAIR;AACJ,MAAI6J,aAAa;AACjB,MAAIC,gBAAyC,CAAA;AAC7C,MAAIC,eACFvK,uBAAuBE,cAAcF,oBAAoB,CAAC,CAAC,IACvDA,oBAAoB,CAAC,EAAEtD,QACvBlC;AAGNG,UAAQkH,QAASzG,WAAS;AACxB,QAAI,EAAEA,MAAME,MAAMC,MAAMiK,UAAU;AAChC;IACD;AACD,QAAIjK,KAAKH,MAAME,MAAMC;AACrB,QAAIqK,SAASJ,QAAQjK,EAAE;AACvBgD,cACE,CAACiM,iBAAiB5E,MAAM,GACxB,qDAAqD;AAEvD,QAAI1F,cAAc0F,MAAM,GAAG;AACzB,UAAIlJ,QAAQkJ,OAAOlJ;AAInB,UAAI6N,iBAAiB/P,QAAW;AAC9BkC,gBAAQ6N;AACRA,uBAAe/P;MAChB;AAEDwG,eAASA,UAAU,CAAA;AAEnB,UAAIoJ,yBAAyB;AAC3BpJ,eAAOzF,EAAE,IAAImB;MACd,OAAM;AAIL,YAAI+N,gBAAgBC,oBAAoB/P,SAASY,EAAE;AACnD,YAAIyF,OAAOyJ,cAAcnP,MAAMC,EAAE,KAAK,MAAM;AAC1CyF,iBAAOyJ,cAAcnP,MAAMC,EAAE,IAAImB;QAClC;MACF;AAGDqE,iBAAWxF,EAAE,IAAIf;AAIjB,UAAI,CAAC6P,YAAY;AACfA,qBAAa;AACb7J,qBAAamI,qBAAqB/C,OAAOlJ,KAAK,IAC1CkJ,OAAOlJ,MAAM2L,SACb;MACL;AACD,UAAIzC,OAAOsC,SAAS;AAClBoC,sBAAc/O,EAAE,IAAIqK,OAAOsC;MAC5B;IACF,OAAM;AACL,UAAIyC,iBAAiB/E,MAAM,GAAG;AAC5BuE,wBAAgB/F,IAAI7I,IAAIqK,OAAOoD,YAAY;AAC3CjI,mBAAWxF,EAAE,IAAIqK,OAAOoD,aAAa7I;AAGrC,YACEyF,OAAOpF,cAAc,QACrBoF,OAAOpF,eAAe,OACtB,CAAC6J,YACD;AACA7J,uBAAaoF,OAAOpF;QACrB;AACD,YAAIoF,OAAOsC,SAAS;AAClBoC,wBAAc/O,EAAE,IAAIqK,OAAOsC;QAC5B;MACF,OAAM;AACLnH,mBAAWxF,EAAE,IAAIqK,OAAOzF;AAGxB,YAAIyF,OAAOpF,cAAcoF,OAAOpF,eAAe,OAAO,CAAC6J,YAAY;AACjE7J,uBAAaoF,OAAOpF;QACrB;AACD,YAAIoF,OAAOsC,SAAS;AAClBoC,wBAAc/O,EAAE,IAAIqK,OAAOsC;QAC5B;MACF;IACF;EACH,CAAC;AAKD,MAAIqC,iBAAiB/P,UAAawF,qBAAqB;AACrDgB,aAAS;MAAE,CAAChB,oBAAoB,CAAC,CAAC,GAAGuK;;AACrCxJ,eAAWf,oBAAoB,CAAC,CAAC,IAAIxF;EACtC;AAED,SAAO;IACLuG;IACAC;IACAR,YAAYA,cAAc;IAC1B8J;;AAEJ;AAEA,SAASM,kBACPtL,OACA3E,SACA2K,eACAE,SACAxF,qBACA4B,sBACAiJ,gBACAV,iBAA0C;AAK1C,MAAI;IAAEpJ;IAAYC;EAAQ,IAAGkJ;IAC3BvP;IACA6K;IACAxF;IACAmK;IACA;;;AAIFvI,uBAAqBC,QAASiJ,QAAM;AAClC,QAAI;MAAE/I;MAAK3G;MAAOgH;IAAU,IAAK0I;AACjC,QAAIlF,SAASiF,eAAe9I,GAAG;AAC/BxD,cAAUqH,QAAQ,2CAA2C;AAG7D,QAAIxD,cAAcA,WAAWwB,OAAOM,SAAS;AAE3C;IACD,WAAUhE,cAAc0F,MAAM,GAAG;AAChC,UAAI6E,gBAAgBC,oBAAoBpL,MAAM3E,SAASS,SAAK,OAAA,SAALA,MAAOE,MAAMC,EAAE;AACtE,UAAI,EAAEyF,UAAUA,OAAOyJ,cAAcnP,MAAMC,EAAE,IAAI;AAC/CyF,iBAAMM,SAAA,CAAA,GACDN,QAAM;UACT,CAACyJ,cAAcnP,MAAMC,EAAE,GAAGqK,OAAOlJ;SAClC;MACF;AACD4C,YAAMgD,SAASK,OAAOZ,GAAG;IAC1B,WAAUyI,iBAAiB5E,MAAM,GAAG;AAGnCrH,gBAAU,OAAO,yCAAyC;IAC3D,WAAUoM,iBAAiB/E,MAAM,GAAG;AAGnCrH,gBAAU,OAAO,iCAAiC;IACnD,OAAM;AACL,UAAIwM,cAAcC,eAAepF,OAAOzF,IAAI;AAC5Cb,YAAMgD,SAAS8B,IAAIrC,KAAKgJ,WAAW;IACpC;EACH,CAAC;AAED,SAAO;IAAEhK;IAAYC;;AACvB;AAEA,SAASiK,gBACPlK,YACAmK,eACAvQ,SACAqG,QAAoC;AAEpC,MAAImK,mBAAgB7J,SAAA,CAAA,GAAQ4J,aAAa;AACzC,WAAS9P,SAAST,SAAS;AACzB,QAAIY,KAAKH,MAAME,MAAMC;AACrB,QAAI2P,cAAcE,eAAe7P,EAAE,GAAG;AACpC,UAAI2P,cAAc3P,EAAE,MAAMf,QAAW;AACnC2Q,yBAAiB5P,EAAE,IAAI2P,cAAc3P,EAAE;MACxC;IAKF,WAAUwF,WAAWxF,EAAE,MAAMf,UAAaY,MAAME,MAAMuF,QAAQ;AAG7DsK,uBAAiB5P,EAAE,IAAIwF,WAAWxF,EAAE;IACrC;AAED,QAAIyF,UAAUA,OAAOoK,eAAe7P,EAAE,GAAG;AAEvC;IACD;EACF;AACD,SAAO4P;AACT;AAEA,SAASE,uBACPrL,qBAAoD;AAEpD,MAAI,CAACA,qBAAqB;AACxB,WAAO,CAAA;EACR;AACD,SAAOE,cAAcF,oBAAoB,CAAC,CAAC,IACvC;;IAEEsL,YAAY,CAAA;EACb,IACD;IACEA,YAAY;MACV,CAACtL,oBAAoB,CAAC,CAAC,GAAGA,oBAAoB,CAAC,EAAEG;IAClD;;AAET;AAKA,SAASuK,oBACP/P,SACAqH,SAAgB;AAEhB,MAAIuJ,kBAAkBvJ,UAClBrH,QAAQwE,MAAM,GAAGxE,QAAQsE,UAAWC,OAAMA,EAAE5D,MAAMC,OAAOyG,OAAO,IAAI,CAAC,IACrE,CAAC,GAAGrH,OAAO;AACf,SACE4Q,gBAAgBC,QAAO,EAAGC,KAAMvM,OAAMA,EAAE5D,MAAMoQ,qBAAqB,IAAI,KACvE/Q,QAAQ,CAAC;AAEb;AAEA,SAASgR,uBAAuBnI,QAAiC;AAK/D,MAAIlI,QACFkI,OAAOhI,WAAW,IACdgI,OAAO,CAAC,IACRA,OAAOiI,KAAMxE,OAAMA,EAAEjL,SAAS,CAACiL,EAAExL,QAAQwL,EAAExL,SAAS,GAAG,KAAK;IAC1DF,IAAE;;AAGV,SAAO;IACLZ,SAAS,CACP;MACE6G,QAAQ,CAAA;MACR3F,UAAU;MACV+P,cAAc;MACdtQ;IACD,CAAA;IAEHA;;AAEJ;AAEA,SAASqB,uBACP0L,QAAcwD,QAaR;AAAA,MAZN;IACEhQ;IACAmG;IACApF;IACAE;IACAgP;0BAOE,CAAA,IAAED;AAEN,MAAIvD,aAAa;AACjB,MAAIyD,eAAe;AAEnB,MAAI1D,WAAW,KAAK;AAClBC,iBAAa;AACb,QAAIxL,SAAS,mBAAmB;AAC9BiP,qBACE,0BAAwBlQ,WAAQ,mDAAA,0CACQiQ;IAC3C,WAAUlP,UAAUf,YAAYmG,SAAS;AACxC+J,qBACE,gBAAcnP,SAAM,kBAAgBf,WACOmG,YAAAA,2CAAAA,UAAO,SACP;IAC9C,WAAUlF,SAAS,gBAAgB;AAClCiP,qBAAe;IAChB,WAAUjP,SAAS,gBAAgB;AAClCiP,qBAAe;IAChB;EACF,WAAU1D,WAAW,KAAK;AACzBC,iBAAa;AACbyD,mBAAyB/J,YAAAA,UAAgCnG,2BAAAA,WAAW;EACrE,WAAUwM,WAAW,KAAK;AACzBC,iBAAa;AACbyD,mBAAY,2BAA4BlQ,WAAW;EACpD,WAAUwM,WAAW,KAAK;AACzBC,iBAAa;AACb,QAAI1L,UAAUf,YAAYmG,SAAS;AACjC+J,qBACE,gBAAcnP,OAAOI,YAAW,IAAE,kBAAgBnB,WAAQ,YAAA,4CACdmG,UAAO,SACR;eACpCpF,QAAQ;AACjBmP,qBAAY,6BAA8BnP,OAAOI,YAAW,IAAK;IAClE;EACF;AAED,SAAO,IAAIoL,kBACTC,UAAU,KACVC,YACA,IAAIjB,MAAM0E,YAAY,GACtB,IAAI;AAER;AAGA,SAASC,aACPxG,SAAmC;AAEnC,MAAI7H,UAAUuH,OAAOvH,QAAQ6H,OAAO;AACpC,WAASK,IAAIlI,QAAQnC,SAAS,GAAGqK,KAAK,GAAGA,KAAK;AAC5C,QAAI,CAAC9D,KAAK6D,MAAM,IAAIjI,QAAQkI,CAAC;AAC7B,QAAI2E,iBAAiB5E,MAAM,GAAG;AAC5B,aAAO;QAAE7D;QAAK6D;;IACf;EACF;AACH;AAEA,SAASzI,kBAAkB1B,MAAQ;AACjC,MAAIkD,aAAa,OAAOlD,SAAS,WAAWmD,UAAUnD,IAAI,IAAIA;AAC9D,SAAOW,WAAUkF,SAAA,CAAA,GAAM3C,YAAU;IAAE5C,MAAM;EAAE,CAAA,CAAE;AAC/C;AAEA,SAASkQ,iBAAiBC,GAAaC,GAAW;AAChD,MAAID,EAAErQ,aAAasQ,EAAEtQ,YAAYqQ,EAAEpQ,WAAWqQ,EAAErQ,QAAQ;AACtD,WAAO;EACR;AAED,MAAIoQ,EAAEnQ,SAAS,IAAI;AAEjB,WAAOoQ,EAAEpQ,SAAS;aACTmQ,EAAEnQ,SAASoQ,EAAEpQ,MAAM;AAE5B,WAAO;EACR,WAAUoQ,EAAEpQ,SAAS,IAAI;AAExB,WAAO;EACR;AAID,SAAO;AACT;AAEA,SAASsI,UAAuBkD,KAAY;AAC1C,SAAO,OAAOA,QAAQ,YAAYA,OAAO,QAAQ,UAAUA;AAC7D;AAYA,SAAS6E,mCAAmCC,QAA0B;AACpE,SACEC,WAAWD,OAAOA,MAAM,KAAKE,oBAAoBC,IAAIH,OAAOA,OAAOI,MAAM;AAE7E;AAEA,SAASC,iBAAiBL,QAAkB;AAC1C,SAAOA,OAAOM,SAASC,WAAWC;AACpC;AAEA,SAASC,cAAcT,QAAkB;AACvC,SAAOA,OAAOM,SAASC,WAAWG;AACpC;AAEA,SAASC,iBAAiBX,QAAmB;AAC3C,UAAQA,UAAUA,OAAOM,UAAUC,WAAWK;AAChD;AAEM,SAAUC,uBACdC,OAAU;AAEV,SACE,OAAOA,UAAU,YACjBA,SAAS,QACT,UAAUA,SACV,UAAUA,SACV,UAAUA,SACVA,MAAMR,SAAS;AAEnB;AAEM,SAAUS,eAAeD,OAAU;AACvC,MAAIN,WAAyBM;AAC7B,SACEN,YACA,OAAOA,aAAa,YACpB,OAAOA,SAASQ,SAAS,YACzB,OAAOR,SAASS,cAAc,cAC9B,OAAOT,SAASU,WAAW,cAC3B,OAAOV,SAASW,gBAAgB;AAEpC;AAEA,SAASlB,WAAWa,OAAU;AAC5B,SACEA,SAAS,QACT,OAAOA,MAAMV,WAAW,YACxB,OAAOU,MAAMM,eAAe,YAC5B,OAAON,MAAMO,YAAY,YACzB,OAAOP,MAAMQ,SAAS;AAE1B;AAYA,SAASC,cAAcC,QAAc;AACnC,SAAOC,oBAAoBC,IAAIF,OAAOG,YAAW,CAAgB;AACnE;AAEA,SAASC,iBACPJ,QAAc;AAEd,SAAOK,qBAAqBH,IAAIF,OAAOG,YAAW,CAAwB;AAC5E;AAEA,eAAeG,iCACbC,SACAC,SACAC,QACAC,gBACAC,mBAA4B;AAE5B,MAAIC,UAAUC,OAAOD,QAAQJ,OAAO;AACpC,WAASM,QAAQ,GAAGA,QAAQF,QAAQG,QAAQD,SAAS;AACnD,QAAI,CAACE,SAASC,MAAM,IAAIL,QAAQE,KAAK;AACrC,QAAII,QAAQX,QAAQY,KAAMC,QAAMA,KAAC,OAAA,SAADA,EAAGC,MAAMC,QAAON,OAAO;AAIvD,QAAI,CAACE,OAAO;AACV;IACD;AAED,QAAIK,eAAeb,eAAeS,KAC/BC,OAAMA,EAAEC,MAAMC,OAAOJ,MAAOG,MAAMC,EAAE;AAEvC,QAAIE,uBACFD,gBAAgB,QAChB,CAACE,mBAAmBF,cAAcL,KAAK,MACtCP,qBAAqBA,kBAAkBO,MAAMG,MAAMC,EAAE,OAAOI;AAE/D,QAAIC,iBAAiBV,MAAM,KAAKO,sBAAsB;AAIpD,YAAMI,oBAAoBX,QAAQR,QAAQ,KAAK,EAAEoB,KAAMZ,CAAAA,YAAU;AAC/D,YAAIA,SAAQ;AACVT,kBAAQQ,OAAO,IAAIC;QACpB;MACH,CAAC;IACF;EACF;AACH;AAEA,eAAea,8BACbvB,SACAC,SACAuB,sBAA2C;AAE3C,WAASjB,QAAQ,GAAGA,QAAQiB,qBAAqBhB,QAAQD,SAAS;AAChE,QAAI;MAAEkB;MAAKhB;MAASiB;IAAY,IAAGF,qBAAqBjB,KAAK;AAC7D,QAAIG,SAAST,QAAQwB,GAAG;AACxB,QAAId,QAAQX,QAAQY,KAAMC,QAAMA,KAAC,OAAA,SAADA,EAAGC,MAAMC,QAAON,OAAO;AAIvD,QAAI,CAACE,OAAO;AACV;IACD;AAED,QAAIS,iBAAiBV,MAAM,GAAG;AAI5BiB,gBACED,YACA,sEAAsE;AAExE,YAAML,oBAAoBX,QAAQgB,WAAWxB,QAAQ,IAAI,EAAEoB,KACxDZ,CAAAA,YAAU;AACT,YAAIA,SAAQ;AACVT,kBAAQwB,GAAG,IAAIf;QAChB;MACH,CAAC;IAEJ;EACF;AACH;AAEA,eAAeW,oBACbX,QACAR,QACA0B,QAAc;AAAA,MAAdA,WAAM,QAAA;AAANA,aAAS;EAAK;AAEd,MAAIC,UAAU,MAAMnB,OAAOoB,aAAaC,YAAY7B,MAAM;AAC1D,MAAI2B,SAAS;AACX;EACD;AAED,MAAID,QAAQ;AACV,QAAI;AACF,aAAO;QACLI,MAAMC,WAAWC;QACjBA,MAAMxB,OAAOoB,aAAaK;;aAErBC,GAAG;AAEV,aAAO;QACLJ,MAAMC,WAAWI;QACjBA,OAAOD;;IAEV;EACF;AAED,SAAO;IACLJ,MAAMC,WAAWC;IACjBA,MAAMxB,OAAOoB,aAAaI;;AAE9B;AAEA,SAASI,mBAAmBC,QAAc;AACxC,SAAO,IAAIC,gBAAgBD,MAAM,EAAEE,OAAO,OAAO,EAAEC,KAAMC,OAAMA,MAAM,EAAE;AACzE;AAEA,SAASC,eACP5C,SACA6C,UAA2B;AAE3B,MAAIN,SACF,OAAOM,aAAa,WAAWC,UAAUD,QAAQ,EAAEN,SAASM,SAASN;AACvE,MACEvC,QAAQA,QAAQQ,SAAS,CAAC,EAAEM,MAAMP,SAClC+B,mBAAmBC,UAAU,EAAE,GAC/B;AAEA,WAAOvC,QAAQA,QAAQQ,SAAS,CAAC;EAClC;AAGD,MAAIuC,cAAcC,2BAA2BhD,OAAO;AACpD,SAAO+C,YAAYA,YAAYvC,SAAS,CAAC;AAC3C;AAEA,SAASyC,4BACPC,YAAsB;AAEtB,MAAI;IAAEC;IAAYC;IAAYC;IAAaC;IAAMC;IAAUC,MAAAA;EAAM,IAC/DN;AACF,MAAI,CAACC,cAAc,CAACC,cAAc,CAACC,aAAa;AAC9C;EACD;AAED,MAAIC,QAAQ,MAAM;AAChB,WAAO;MACLH;MACAC;MACAC;MACAE,UAAUpC;MACVqC,MAAMrC;MACNmC;;EAEH,WAAUC,YAAY,MAAM;AAC3B,WAAO;MACLJ;MACAC;MACAC;MACAE;MACAC,MAAMrC;MACNmC,MAAMnC;;EAET,WAAUqC,UAASrC,QAAW;AAC7B,WAAO;MACLgC;MACAC;MACAC;MACAE,UAAUpC;MACVqC,MAAAA;MACAF,MAAMnC;;EAET;AACH;AAEA,SAASsC,qBACPZ,UACAa,YAAuB;AAEvB,MAAIA,YAAY;AACd,QAAIR,aAA0C;MAC5CS,OAAO;MACPd;MACAM,YAAYO,WAAWP;MACvBC,YAAYM,WAAWN;MACvBC,aAAaK,WAAWL;MACxBE,UAAUG,WAAWH;MACrBC,MAAME,WAAWF;MACjBF,MAAMI,WAAWJ;;AAEnB,WAAOJ;EACR,OAAM;AACL,QAAIA,aAA0C;MAC5CS,OAAO;MACPd;MACAM,YAAYhC;MACZiC,YAAYjC;MACZkC,aAAalC;MACboC,UAAUpC;MACVqC,MAAMrC;MACNmC,MAAMnC;;AAER,WAAO+B;EACR;AACH;AAEA,SAASU,wBACPf,UACAa,YAAsB;AAEtB,MAAIR,aAA6C;IAC/CS,OAAO;IACPd;IACAM,YAAYO,WAAWP;IACvBC,YAAYM,WAAWN;IACvBC,aAAaK,WAAWL;IACxBE,UAAUG,WAAWH;IACrBC,MAAME,WAAWF;IACjBF,MAAMI,WAAWJ;;AAEnB,SAAOJ;AACT;AAEA,SAASW,kBACPH,YACAxB,MAAsB;AAEtB,MAAIwB,YAAY;AACd,QAAII,UAAoC;MACtCH,OAAO;MACPR,YAAYO,WAAWP;MACvBC,YAAYM,WAAWN;MACvBC,aAAaK,WAAWL;MACxBE,UAAUG,WAAWH;MACrBC,MAAME,WAAWF;MACjBF,MAAMI,WAAWJ;MACjBpB;;AAEF,WAAO4B;EACR,OAAM;AACL,QAAIA,UAAoC;MACtCH,OAAO;MACPR,YAAYhC;MACZiC,YAAYjC;MACZkC,aAAalC;MACboC,UAAUpC;MACVqC,MAAMrC;MACNmC,MAAMnC;MACNe;;AAEF,WAAO4B;EACR;AACH;AAEA,SAASC,qBACPL,YACAM,iBAAyB;AAEzB,MAAIF,UAAuC;IACzCH,OAAO;IACPR,YAAYO,WAAWP;IACvBC,YAAYM,WAAWN;IACvBC,aAAaK,WAAWL;IACxBE,UAAUG,WAAWH;IACrBC,MAAME,WAAWF;IACjBF,MAAMI,WAAWJ;IACjBpB,MAAM8B,kBAAkBA,gBAAgB9B,OAAOf;;AAEjD,SAAO2C;AACT;AAEA,SAASG,eAAe/B,MAAqB;AAC3C,MAAI4B,UAAiC;IACnCH,OAAO;IACPR,YAAYhC;IACZiC,YAAYjC;IACZkC,aAAalC;IACboC,UAAUpC;IACVqC,MAAMrC;IACNmC,MAAMnC;IACNe;;AAEF,SAAO4B;AACT;AAEA,SAASI,0BACPC,SACAC,aAAqC;AAErC,MAAI;AACF,QAAIC,mBAAmBF,QAAQG,eAAeC,QAC5CC,uBAAuB;AAEzB,QAAIH,kBAAkB;AACpB,UAAIb,QAAOiB,KAAKC,MAAML,gBAAgB;AACtC,eAAS,CAACM,GAAGhC,CAAC,KAAKrC,OAAOD,QAAQmD,SAAQ,CAAA,CAAE,GAAG;AAC7C,YAAIb,KAAKiC,MAAMC,QAAQlC,CAAC,GAAG;AACzByB,sBAAYU,IAAIH,GAAG,IAAII,IAAIpC,KAAK,CAAA,CAAE,CAAC;QACpC;MACF;IACF;WACMP,GAAG;EACV;AAEJ;AAEA,SAAS4C,0BACPb,SACAC,aAAqC;AAErC,MAAIA,YAAYa,OAAO,GAAG;AACxB,QAAIzB,QAAiC,CAAA;AACrC,aAAS,CAACmB,GAAGhC,CAAC,KAAKyB,aAAa;AAC9BZ,MAAAA,MAAKmB,CAAC,IAAI,CAAC,GAAGhC,CAAC;IAChB;AACD,QAAI;AACFwB,cAAQG,eAAeY,QACrBV,yBACAC,KAAKU,UAAU3B,KAAI,CAAC;aAEfnB,OAAO;AACd+C,cACE,OAC8D/C,gEAAAA,QAAK,IAAI;IAE1E;EACF;AACH;;;;;;;;;;;;;;;;;AC1vLO,IAAMgD,oBACLC,oBAA8C,IAAI;AAC1D,IAAAC,MAAa;AACXF,oBAAkBG,cAAc;AAClC;AAEO,IAAMC,yBAA+BH,oBAE1C,IAAI;AACN,IAAAC,MAAa;AACXE,yBAAuBD,cAAc;AACvC;AAEO,IAAME,eAAqBJ,oBAAqC,IAAI;AAC3E,IAAAC,MAAa;AACXG,eAAaF,cAAc;AAC7B;AAsCO,IAAMG,oBAA0BL,oBACrC,IACF;AAEA,IAAAC,MAAa;AACXI,oBAAkBH,cAAc;AAClC;AAOO,IAAMI,kBAAwBN,oBACnC,IACF;AAEA,IAAAC,MAAa;AACXK,kBAAgBJ,cAAc;AAChC;IAQaK,eAAqBP,oBAAkC;EAClEQ,QAAQ;EACRC,SAAS,CAAA;EACTC,aAAa;AACf,CAAC;AAED,IAAAT,MAAa;AACXM,eAAaL,cAAc;AAC7B;AAEO,IAAMS,oBAA0BX,oBAAmB,IAAI;AAE9D,IAAAC,MAAa;AACXU,oBAAkBT,cAAc;AAClC;ACvHO,SAASU,QACdC,IAAMC,OAEE;AAAA,MADR;IAAEC;EAA6C,IAACD,UAAA,SAAG,CAAA,IAAEA;AAErD,GACEE,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEC;IAAUC;EAAU,IAAUC,iBAAWf,iBAAiB;AAChE,MAAI;IAAEgB;IAAMC;IAAUC;EAAO,IAAIC,gBAAgBX,IAAI;IAAEE;EAAS,CAAC;AAEjE,MAAIU,iBAAiBH;AAMrB,MAAIJ,aAAa,KAAK;AACpBO,qBACEH,aAAa,MAAMJ,WAAWQ,UAAU,CAACR,UAAUI,QAAQ,CAAC;EAChE;AAEA,SAAOH,UAAUQ,WAAW;IAAEL,UAAUG;IAAgBF;IAAQF;EAAK,CAAC;AACxE;AAOO,SAASL,qBAA8B;AAC5C,SAAaI,iBAAWd,eAAe,KAAK;AAC9C;AAYO,SAASsB,cAAwB;AACtC,GACEZ,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,SAAaG,iBAAWd,eAAe,EAAEuB;AAC3C;AAQO,SAASC,oBAAoC;AAClD,SAAaV,iBAAWd,eAAe,EAAEyB;AAC3C;AASO,SAASC,SAGdC,SAA+D;AAC/D,GACEjB,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEK;MAAaM,YAAW;AAC9B,SAAaM,cACX,MAAMC,UAA0BF,SAASG,WAAWd,QAAQ,CAAC,GAC7D,CAACA,UAAUW,OAAO,CACpB;AACF;AAUA,IAAMI,wBACJ;AAIF,SAASC,0BACPC,IACA;AACA,MAAIC,WAAiBpB,iBAAWf,iBAAiB,EAAEoC;AACnD,MAAI,CAACD,UAAU;AAIbE,IAAMC,sBAAgBJ,EAAE;EAC1B;AACF;AAQO,SAASK,cAAgC;AAC9C,MAAI;IAAElC;EAAY,IAAUU,iBAAWb,YAAY;AAGnD,SAAOG,cAAcmC,kBAAiB,IAAKC,oBAAmB;AAChE;AAEA,SAASA,sBAAwC;AAC/C,GACE9B,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI8B,oBAA0B3B,iBAAWrB,iBAAiB;AAC1D,MAAI;IAAEmB;IAAU8B;IAAQ7B;EAAU,IAAUC,iBAAWf,iBAAiB;AACxE,MAAI;IAAEI;EAAQ,IAAUW,iBAAWb,YAAY;AAC/C,MAAI;IAAEe,UAAU2B;MAAqBrB,YAAW;AAEhD,MAAIsB,qBAAqBC,KAAKC,UAC5BC,oBAAoB5C,SAASuC,OAAOM,oBAAoB,CAC1D;AAEA,MAAIC,YAAkBC,aAAO,KAAK;AAClClB,4BAA0B,MAAM;AAC9BiB,cAAUE,UAAU;EACtB,CAAC;AAED,MAAIC,WAAmCC,kBACrC,SAAC9C,IAAiB+C,SAAkC;AAAA,QAAlCA,YAAwB,QAAA;AAAxBA,gBAA2B,CAAA;IAAE;AAC7C3D,WAAA4D,QAAQN,UAAUE,SAASpB,qBAAqB,IAAC;AAIjD,QAAI,CAACkB,UAAUE,QAAS;AAExB,QAAI,OAAO5C,OAAO,UAAU;AAC1BM,gBAAU2C,GAAGjD,EAAE;AACf;IACF;AAEA,QAAIkD,OAAOC,UACTnD,IACAsC,KAAKc,MAAMf,kBAAkB,GAC7BD,kBACAW,QAAQ7C,aAAa,MACvB;AAQA,QAAIgC,qBAAqB,QAAQ7B,aAAa,KAAK;AACjD6C,WAAKzC,WACHyC,KAAKzC,aAAa,MACdJ,WACAQ,UAAU,CAACR,UAAU6C,KAAKzC,QAAQ,CAAC;IAC3C;AAEA,KAAC,CAAC,CAACsC,QAAQM,UAAU/C,UAAU+C,UAAU/C,UAAUgD,MACjDJ,MACAH,QAAQQ,OACRR,OACF;EACF,GACA,CACE1C,UACAC,WACA+B,oBACAD,kBACAF,iBAAiB,CAErB;AAEA,SAAOW;AACT;AAEA,IAAMW,gBAAsBrE,oBAAuB,IAAI;AAOhD,SAASsE,mBAA+C;AAC7D,SAAalD,iBAAWiD,aAAa;AACvC;AAQO,SAASE,UAAUC,SAA8C;AACtE,MAAIhE,SAAeY,iBAAWb,YAAY,EAAEC;AAC5C,MAAIA,QAAQ;AACV,WACEiE,oBAACJ,cAAcK,UAAQ;MAACC,OAAOH;IAAQ,GAAEhE,MAA+B;EAE5E;AACA,SAAOA;AACT;AAQO,SAASoE,YAId;AACA,MAAI;IAAEnE;EAAQ,IAAUW,iBAAWb,YAAY;AAC/C,MAAIsE,aAAapE,QAAQA,QAAQqE,SAAS,CAAC;AAC3C,SAAOD,aAAcA,WAAWE,SAAiB,CAAA;AACnD;AAOO,SAASvD,gBACdX,IAAMmE,QAEA;AAAA,MADN;IAAEjE;EAA6C,IAACiE,WAAA,SAAG,CAAA,IAAEA;AAErD,MAAI;IAAEhC;EAAO,IAAU5B,iBAAWf,iBAAiB;AACnD,MAAI;IAAEI;EAAQ,IAAUW,iBAAWb,YAAY;AAC/C,MAAI;IAAEe,UAAU2B;MAAqBrB,YAAW;AAChD,MAAIsB,qBAAqBC,KAAKC,UAC5BC,oBAAoB5C,SAASuC,OAAOM,oBAAoB,CAC1D;AAEA,SAAapB,cACX,MACE8B,UACEnD,IACAsC,KAAKc,MAAMf,kBAAkB,GAC7BD,kBACAlC,aAAa,MACf,GACF,CAACF,IAAIqC,oBAAoBD,kBAAkBlC,QAAQ,CACrD;AACF;AAUO,SAASkE,UACdC,QACAC,aAC2B;AAC3B,SAAOC,cAAcF,QAAQC,WAAW;AAC1C;AAGO,SAASC,cACdF,QACAC,aACAE,iBACArC,QAC2B;AAC3B,GACEhC,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEE;EAAU,IAAUC,iBAAWf,iBAAiB;AACtD,MAAI;IAAEI,SAAS6E;EAAc,IAAUlE,iBAAWb,YAAY;AAC9D,MAAIsE,aAAaS,cAAcA,cAAcR,SAAS,CAAC;AACvD,MAAIS,eAAeV,aAAaA,WAAWE,SAAS,CAAA;AACpD,MAAIS,iBAAiBX,aAAaA,WAAWvD,WAAW;AACxD,MAAImE,qBAAqBZ,aAAaA,WAAWa,eAAe;AAChE,MAAIC,cAAcd,cAAcA,WAAWe;AAE3C,MAAA3F,MAAa;AAqBX,QAAI4F,aAAcF,eAAeA,YAAY5B,QAAS;AACtD+B,gBACEN,gBACA,CAACG,eAAeE,WAAWE,SAAS,GAAG,GACvC,oEAAA,MACMP,iBAAuCK,2BAAAA,aAAwB,kBAAA;;KAI1BA,2CAAAA,aAAU,oBAC1CA,YAAAA,eAAe,MAAM,MAASA,aAAU,QAAI,MACzD;EACF;AAEA,MAAIG,sBAAsBpE,YAAW;AAErC,MAAIC;AACJ,MAAIsD,aAAa;AAAA,QAAAc;AACf,QAAIC,oBACF,OAAOf,gBAAgB,WAAWgB,UAAUhB,WAAW,IAAIA;AAE7D,MACEM,uBAAuB,SAAGQ,wBACxBC,kBAAkB5E,aAAQ,OAAA,SAA1B2E,sBAA4BG,WAAWX,kBAAkB,MAACxF,OAF9DgB,UAAS,OAGP,8KACmF,iEAClBwE,qBAAkB,SAAI,mBACpES,kBAAkB5E,WAAQ,sCAAuC,IANtFL,UAAS,KAAA,IAAA;AASTY,eAAWqE;EACb,OAAO;AACLrE,eAAWmE;EACb;AAEA,MAAI1E,WAAWO,SAASP,YAAY;AAEpC,MAAI+E,oBAAoB/E;AACxB,MAAImE,uBAAuB,KAAK;AAe9B,QAAIa,iBAAiBb,mBAAmBvB,QAAQ,OAAO,EAAE,EAAEqC,MAAM,GAAG;AACpE,QAAIC,WAAWlF,SAAS4C,QAAQ,OAAO,EAAE,EAAEqC,MAAM,GAAG;AACpDF,wBAAoB,MAAMG,SAASC,MAAMH,eAAexB,MAAM,EAAE4B,KAAK,GAAG;EAC1E;AAEA,MAAIjG,UAAUkG,YAAYzB,QAAQ;IAAE5D,UAAU+E;EAAkB,CAAC;AAEjE,MAAApG,MAAa;AACXA,WAAA4D,QACE8B,eAAelF,WAAW,MAAI,iCACCoB,SAASP,WAAWO,SAASN,SAASM,SAASR,OAAI,IACpF,IAAC;AAEDpB,WAAA4D,QACEpD,WAAW,QACTA,QAAQA,QAAQqE,SAAS,CAAC,EAAEc,MAAMgB,YAAYC,UAC9CpG,QAAQA,QAAQqE,SAAS,CAAC,EAAEc,MAAMkB,cAAcD,UAChDpG,QAAQA,QAAQqE,SAAS,CAAC,EAAEc,MAAMmB,SAASF,QAC7C,qCAAmChF,SAASP,WAAWO,SAASN,SAASM,SAASR,OAAI,6IAGxF,IAAC;EACH;AAEA,MAAI2F,kBAAkBC,eACpBxG,WACEA,QAAQyG,IAAKC,WACXC,OAAOC,OAAO,CAAA,GAAIF,OAAO;IACvBpC,QAAQqC,OAAOC,OAAO,CAAA,GAAI9B,cAAc4B,MAAMpC,MAAM;IACpDzD,UAAUI,UAAU;MAClB+D;;MAEAtE,UAAUmG,iBACNnG,UAAUmG,eAAeH,MAAM7F,QAAQ,EAAEA,WACzC6F,MAAM7F;IAAQ,CACnB;IACDoE,cACEyB,MAAMzB,iBAAiB,MACnBD,qBACA/D,UAAU;MACR+D;;MAEAtE,UAAUmG,iBACNnG,UAAUmG,eAAeH,MAAMzB,YAAY,EAAEpE,WAC7C6F,MAAMzB;IAAY,CACvB;GACR,CACH,GACFJ,eACAD,iBACArC,MACF;AAKA,MAAImC,eAAe6B,iBAAiB;AAClC,WACEvC,oBAACnE,gBAAgBoE,UAAQ;MACvBC,OAAO;QACL9C,UAAQ0F,UAAA;UACNjG,UAAU;UACVC,QAAQ;UACRF,MAAM;UACN+C,OAAO;UACPoD,KAAK;QAAS,GACX3F,QAAQ;QAEbE,gBAAgB0F,OAAeC;MACjC;IAAE,GAEDV,eACuB;EAE9B;AAEA,SAAOA;AACT;AAEA,SAASW,wBAAwB;AAC/B,MAAIC,QAAQC,cAAa;AACzB,MAAIC,UAAUC,qBAAqBH,KAAK,IACjCA,MAAMI,SAAUJ,MAAAA,MAAMK,aACzBL,iBAAiBM,QACjBN,MAAME,UACN3E,KAAKC,UAAUwE,KAAK;AACxB,MAAIO,QAAQP,iBAAiBM,QAAQN,MAAMO,QAAQ;AACnD,MAAIC,YAAY;AAChB,MAAIC,YAAY;IAAEC,SAAS;IAAUC,iBAAiBH;;AACtD,MAAII,aAAa;IAAEF,SAAS;IAAWC,iBAAiBH;;AAExD,MAAIK,UAAU;AACd,MAAAxI,MAAa;AACXyI,YAAQd,MACN,wDACAA,KACF;AAEAa,cACEhE,oBAAAkE,gBACEjG,MAAA+B,oBAAA,KAAA,MAAG,qBAAsB,GACzBA,oBAAA,KAAA,MAAG,gGAEqBA,oBAAA,QAAA;MAAMmE,OAAOJ;OAAY,eAAmB,GAAI,OAAC,KACvE/D,oBAAA,QAAA;MAAMmE,OAAOJ;IAAW,GAAC,cAAkB,GAC1C,sBAAA,CACH;EAEN;AAEA,SACE/D,oBAAAkE,gBAAA,MACElE,oBAAI,MAAA,MAAA,+BAAiC,GACrCA,oBAAA,MAAA;IAAImE,OAAO;MAAEC,WAAW;IAAS;EAAE,GAAEf,OAAY,GAChDK,QAAQ1D,oBAAA,OAAA;IAAKmE,OAAOP;EAAU,GAAEF,KAAW,IAAI,MAC/CM,OACD;AAEN;AAEA,IAAMK,sBAAsBrE,oBAACkD,uBAAqB,IAAE;AAgB7C,IAAMoB,sBAAN,cAAwCjC,gBAG7C;EACAkC,YAAYC,OAAiC;AAC3C,UAAMA,KAAK;AACX,SAAK7E,QAAQ;MACXvC,UAAUoH,MAAMpH;MAChBqH,cAAcD,MAAMC;MACpBtB,OAAOqB,MAAMrB;;EAEjB;EAEA,OAAOuB,yBAAyBvB,OAAY;AAC1C,WAAO;MAAEA;;EACX;EAEA,OAAOwB,yBACLH,OACA7E,OACA;AASA,QACEA,MAAMvC,aAAaoH,MAAMpH,YACxBuC,MAAM8E,iBAAiB,UAAUD,MAAMC,iBAAiB,QACzD;AACA,aAAO;QACLtB,OAAOqB,MAAMrB;QACb/F,UAAUoH,MAAMpH;QAChBqH,cAAcD,MAAMC;;IAExB;AAMA,WAAO;MACLtB,OAAOqB,MAAMrB,UAAUf,SAAYoC,MAAMrB,QAAQxD,MAAMwD;MACvD/F,UAAUuC,MAAMvC;MAChBqH,cAAcD,MAAMC,gBAAgB9E,MAAM8E;;EAE9C;EAEAG,kBAAkBzB,OAAY0B,WAAgB;AAC5CZ,YAAQd,MACN,yDACAA,OACA0B,SACF;EACF;EAEAC,SAAS;AACP,WAAO,KAAKnF,MAAMwD,UAAUf,SAC1BpC,oBAAClE,aAAamE,UAAQ;MAACC,OAAO,KAAKsE,MAAMO;IAAa,GACpD/E,oBAAC9D,kBAAkB+D,UAAQ;MACzBC,OAAO,KAAKP,MAAMwD;MAClB6B,UAAU,KAAKR,MAAMS;IAAU,CAChC,CACoB,IAEvB,KAAKT,MAAMQ;EAEf;AACF;AAQA,SAASE,cAAaC,MAAwD;AAAA,MAAvD;IAAEJ;IAAcrC;IAAOsC;EAA6B,IAACG;AAC1E,MAAI7G,oBAA0B3B,iBAAWrB,iBAAiB;AAI1D,MACEgD,qBACAA,kBAAkBN,UAClBM,kBAAkB8G,kBACjB1C,MAAMvB,MAAMkE,gBAAgB3C,MAAMvB,MAAMmE,gBACzC;AACAhH,sBAAkB8G,cAAcG,6BAA6B7C,MAAMvB,MAAMqE;EAC3E;AAEA,SACExF,oBAAClE,aAAamE,UAAQ;IAACC,OAAO6E;EAAa,GACxCC,QACoB;AAE3B;AAEO,SAASxC,eACdxG,SACA6E,eACAD,iBACArC,QAC2B;AAAA,MAAAkH;AAAA,MAH3B5E,kBAA2B,QAAA;AAA3BA,oBAA8B,CAAA;EAAE;AAAA,MAChCD,oBAA4C,QAAA;AAA5CA,sBAA+C;EAAI;AAAA,MACnDrC,WAAoC,QAAA;AAApCA,aAAuC;EAAI;AAE3C,MAAIvC,WAAW,MAAM;AAAA,QAAA0J;AACnB,QAAI,CAAC9E,iBAAiB;AACpB,aAAO;IACT;AAEA,QAAIA,gBAAgB+E,QAAQ;AAG1B3J,gBAAU4E,gBAAgB5E;IAC5B,YACE0J,UAAAnH,WAAAmH,QAAAA,QAAQE,uBACR/E,cAAcR,WAAW,KACzB,CAACO,gBAAgBiF,eACjBjF,gBAAgB5E,QAAQqE,SAAS,GACjC;AAOArE,gBAAU4E,gBAAgB5E;IAC5B,OAAO;AACL,aAAO;IACT;EACF;AAEA,MAAIuG,kBAAkBvG;AAGtB,MAAI2J,UAAMF,mBAAG7E,oBAAA6E,OAAAA,SAAAA,iBAAiBE;AAC9B,MAAIA,UAAU,MAAM;AAClB,QAAIG,aAAavD,gBAAgBwD,UAC9BC,OAAMA,EAAE7E,MAAMqE,OAAMG,UAAM,OAAA,SAANA,OAASK,EAAE7E,MAAMqE,EAAE,OAAMpD,MAChD;AACA,MACE0D,cAAc,KAACtK,OADjBgB,UAAS,OAAA,8DAEqDmG,OAAOsD,KACjEN,MACF,EAAE1D,KAAK,GAAG,CAAC,IAJbzF,UAAS,KAAA,IAAA;AAMT+F,sBAAkBA,gBAAgBP,MAChC,GACAkE,KAAKC,IAAI5D,gBAAgBlC,QAAQyF,aAAa,CAAC,CACjD;EACF;AAIA,MAAIM,iBAAiB;AACrB,MAAIC,gBAAgB;AACpB,MAAIzF,mBAAmBrC,UAAUA,OAAOqH,qBAAqB;AAC3D,aAASU,IAAI,GAAGA,IAAI/D,gBAAgBlC,QAAQiG,KAAK;AAC/C,UAAI5D,QAAQH,gBAAgB+D,CAAC;AAE7B,UAAI5D,MAAMvB,MAAMoF,mBAAmB7D,MAAMvB,MAAMqF,wBAAwB;AACrEH,wBAAgBC;MAClB;AAEA,UAAI5D,MAAMvB,MAAMqE,IAAI;AAClB,YAAI;UAAEiB;UAAYd,QAAAA;QAAO,IAAI/E;AAC7B,YAAI8F,mBACFhE,MAAMvB,MAAMwF,UACZF,WAAW/D,MAAMvB,MAAMqE,EAAE,MAAMpD,WAC9B,CAACuD,WAAUA,QAAOjD,MAAMvB,MAAMqE,EAAE,MAAMpD;AACzC,YAAIM,MAAMvB,MAAMmB,QAAQoE,kBAAkB;AAIxCN,2BAAiB;AACjB,cAAIC,iBAAiB,GAAG;AACtB9D,8BAAkBA,gBAAgBP,MAAM,GAAGqE,gBAAgB,CAAC;UAC9D,OAAO;AACL9D,8BAAkB,CAACA,gBAAgB,CAAC,CAAC;UACvC;AACA;QACF;MACF;IACF;EACF;AAEA,SAAOA,gBAAgBqE,YAAY,CAAC7K,QAAQ2G,OAAOmE,UAAU;AAE3D,QAAI1D;AACJ,QAAI2D,8BAA8B;AAClC,QAAIzB,eAAuC;AAC3C,QAAImB,yBAAiD;AACrD,QAAI5F,iBAAiB;AACnBuC,cAAQwC,UAAUjD,MAAMvB,MAAMqE,KAAKG,OAAOjD,MAAMvB,MAAMqE,EAAE,IAAIpD;AAC5DiD,qBAAe3C,MAAMvB,MAAMkE,gBAAgBhB;AAE3C,UAAI+B,gBAAgB;AAClB,YAAIC,gBAAgB,KAAKQ,UAAU,GAAG;AACpCxF,sBACE,kBACA,OACA,0EACF;AACAyF,wCAA8B;AAC9BN,mCAAyB;QAC3B,WAAWH,kBAAkBQ,OAAO;AAClCC,wCAA8B;AAC9BN,mCAAyB9D,MAAMvB,MAAMqF,0BAA0B;QACjE;MACF;IACF;AAEA,QAAIxK,WAAU6E,cAAckG,OAAOxE,gBAAgBP,MAAM,GAAG6E,QAAQ,CAAC,CAAC;AACtE,QAAIG,cAAcA,MAAM;AACtB,UAAIhC;AACJ,UAAI7B,OAAO;AACT6B,mBAAWK;iBACFyB,6BAA6B;AACtC9B,mBAAWwB;MACb,WAAW9D,MAAMvB,MAAMkB,WAAW;AAOhC2C,mBAAWhF,oBAAC0C,MAAMvB,MAAMkB,WAAS,IAAE;MACrC,WAAWK,MAAMvB,MAAMgB,SAAS;AAC9B6C,mBAAWtC,MAAMvB,MAAMgB;MACzB,OAAO;AACL6C,mBAAWjJ;MACb;AACA,aACEiE,oBAACkF,eAAa;QACZxC;QACAqC,cAAc;UACZhJ;UACAC,SAAAA;UACAC,aAAa2E,mBAAmB;;QAElCoE;MAAmB,CACpB;;AAML,WAAOpE,oBACJ8B,MAAMvB,MAAMmE,iBAAiB5C,MAAMvB,MAAMkE,gBAAgBwB,UAAU,KACpE7G,oBAACsE,qBAAmB;MAClBlH,UAAUwD,gBAAgBxD;MAC1BqH,cAAc7D,gBAAgB6D;MAC9BQ,WAAWI;MACXlC;MACA6B,UAAUgC,YAAW;MACrBjC,cAAc;QAAEhJ,QAAQ;QAAMC,SAAAA;QAASC,aAAa;MAAK;IAAE,CAC5D,IAED+K,YAAW;KAEZ,IAAiC;AACtC;AAAC,IAEIC,iBAAc,SAAdA,iBAAc;AAAdA,EAAAA,gBAAc,YAAA,IAAA;AAAdA,EAAAA,gBAAc,gBAAA,IAAA;AAAdA,EAAAA,gBAAc,mBAAA,IAAA;AAAA,SAAdA;AAAc,EAAdA,kBAAc,CAAA,CAAA;AAAA,IAMdC,sBAAmB,SAAnBA,sBAAmB;AAAnBA,EAAAA,qBAAmB,YAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,oBAAA,IAAA;AAAnBA,EAAAA,qBAAmB,YAAA,IAAA;AAAnBA,EAAAA,qBAAmB,gBAAA,IAAA;AAAnBA,EAAAA,qBAAmB,mBAAA,IAAA;AAAnBA,EAAAA,qBAAmB,YAAA,IAAA;AAAA,SAAnBA;AAAmB,EAAnBA,uBAAmB,CAAA,CAAA;AAaxB,SAASC,0BACPC,UACA;AACA,SAAUA,WAAQ;AACpB;AAEA,SAASC,qBAAqBD,UAA0B;AACtD,MAAIE,MAAY3K,iBAAWrB,iBAAiB;AAC5C,GAAUgM,MAAG9L,OAAbgB,UAAS,OAAM2K,0BAA0BC,QAAQ,CAAC,IAAlD5K,UAAS,KAAA,IAAA;AACT,SAAO8K;AACT;AAEA,SAASC,mBAAmBH,UAA+B;AACzD,MAAIzH,QAAchD,iBAAWjB,sBAAsB;AACnD,GAAUiE,QAAKnE,OAAfgB,UAAS,OAAQ2K,0BAA0BC,QAAQ,CAAC,IAApD5K,UAAS,KAAA,IAAA;AACT,SAAOmD;AACT;AAEA,SAAS6H,gBAAgBJ,UAA+B;AACtD,MAAIjG,QAAcxE,iBAAWb,YAAY;AACzC,GAAUqF,QAAK3F,OAAfgB,UAAS,OAAQ2K,0BAA0BC,QAAQ,CAAC,IAApD5K,UAAS,KAAA,IAAA;AACT,SAAO2E;AACT;AAGA,SAASsG,kBAAkBL,UAA+B;AACxD,MAAIjG,QAAQqG,gBAAgBJ,QAAQ;AACpC,MAAIM,YAAYvG,MAAMnF,QAAQmF,MAAMnF,QAAQqE,SAAS,CAAC;AACtD,GACEqH,UAAUvG,MAAMqE,KAAEhK,OADpBgB,UAEK4K,OAAAA,WAAQ,wDAAA,IAFb5K,UAAS,KAAA,IAAA;AAIT,SAAOkL,UAAUvG,MAAMqE;AACzB;AAKO,SAASmC,aAAa;AAC3B,SAAOF,kBAAkBP,oBAAoBU,UAAU;AACzD;AAMO,SAASC,gBAAgB;AAC9B,MAAIlI,QAAQ4H,mBAAmBL,oBAAoBY,aAAa;AAChE,SAAOnI,MAAMoI;AACf;AAMO,SAASC,iBAAiB;AAC/B,MAAI1J,oBAAoB+I,qBAAqBJ,eAAegB,cAAc;AAC1E,MAAItI,QAAQ4H,mBAAmBL,oBAAoBe,cAAc;AACjE,SAAaxK,cACX,OAAO;IACLyK,YAAY5J,kBAAkB6J,OAAOD;IACrCvI,OAAOA,MAAM8E;EACf,IACA,CAACnG,kBAAkB6J,OAAOD,YAAYvI,MAAM8E,YAAY,CAC1D;AACF;AAMO,SAAS2D,aAAwB;AACtC,MAAI;IAAEpM;IAASyK;EAAW,IAAIc,mBAC5BL,oBAAoBmB,UACtB;AACA,SAAa5K,cACX,MAAMzB,QAAQyG,IAAKuD,OAAMsC,2BAA2BtC,GAAGS,UAAU,CAAC,GAClE,CAACzK,SAASyK,UAAU,CACtB;AACF;AAKO,SAAS8B,gBAAyB;AACvC,MAAI5I,QAAQ4H,mBAAmBL,oBAAoBsB,aAAa;AAChE,MAAIC,UAAUhB,kBAAkBP,oBAAoBsB,aAAa;AAEjE,MAAI7I,MAAMgG,UAAUhG,MAAMgG,OAAO8C,OAAO,KAAK,MAAM;AACjDxE,YAAQd,MACuDsF,6DAAAA,UAAO,GACtE;AACA,WAAOrG;EACT;AACA,SAAOzC,MAAM8G,WAAWgC,OAAO;AACjC;AAKO,SAASC,mBAAmBD,SAA0B;AAC3D,MAAI9I,QAAQ4H,mBAAmBL,oBAAoByB,kBAAkB;AACrE,SAAOhJ,MAAM8G,WAAWgC,OAAO;AACjC;AAKO,SAASG,gBAAyB;AACvC,MAAIjJ,QAAQ4H,mBAAmBL,oBAAoB2B,aAAa;AAChE,MAAIJ,UAAUhB,kBAAkBP,oBAAoBsB,aAAa;AACjE,SAAO7I,MAAMmJ,aAAanJ,MAAMmJ,WAAWL,OAAO,IAAIrG;AACxD;AAOO,SAASgB,gBAAyB;AAAA,MAAA2F;AACvC,MAAI5F,QAAcxG,iBAAWT,iBAAiB;AAC9C,MAAIyD,QAAQ4H,mBAAmBL,oBAAoB8B,aAAa;AAChE,MAAIP,UAAUhB,kBAAkBP,oBAAoB8B,aAAa;AAIjE,MAAI7F,UAAUf,QAAW;AACvB,WAAOe;EACT;AAGA,UAAA4F,gBAAOpJ,MAAMgG,WAANoD,OAAAA,SAAAA,cAAeN,OAAO;AAC/B;AAKO,SAASQ,gBAAyB;AACvC,MAAI/I,QAAcvD,iBAAWhB,YAAY;AACzC,SAAOuE,SAAK,OAAA,SAALA,MAAOgJ;AAChB;AAKO,SAASC,gBAAyB;AACvC,MAAIjJ,QAAcvD,iBAAWhB,YAAY;AACzC,SAAOuE,SAAK,OAAA,SAALA,MAAOkJ;AAChB;AAEA,IAAIC,YAAY;AAQT,SAASC,WAAWC,aAAiD;AAC1E,MAAI;IAAEpB;IAAQ1L;EAAS,IAAI4K,qBAAqBJ,eAAeuC,UAAU;AACzE,MAAI7J,QAAQ4H,mBAAmBL,oBAAoBsC,UAAU;AAE7D,MAAI,CAACC,YAAYC,aAAa,IAAUC,eAAS,EAAE;AACnD,MAAIC,kBAAwB1K,kBACzB2K,SAAQ;AACP,QAAI,OAAON,gBAAgB,YAAY;AACrC,aAAO,CAAC,CAACA;IACX;AACA,QAAI9M,aAAa,KAAK;AACpB,aAAO8M,YAAYM,GAAG;IACxB;AAKA,QAAI;MAAEC;MAAiBC;MAAcC;IAAc,IAAIH;AACvD,WAAON,YAAY;MACjBO,iBAAehH,UAAA,CAAA,GACVgH,iBAAe;QAClBjN,UACEoN,cAAcH,gBAAgBjN,UAAUJ,QAAQ,KAChDqN,gBAAgBjN;OACnB;MACDkN,cAAYjH,UAAA,CAAA,GACPiH,cAAY;QACflN,UACEoN,cAAcF,aAAalN,UAAUJ,QAAQ,KAC7CsN,aAAalN;OAChB;MACDmN;IACF,CAAC;EACH,GACA,CAACvN,UAAU8M,WAAW,CACxB;AAIAtL,EAAMiM,gBAAU,MAAM;AACpB,QAAInH,MAAMoH,OAAO,EAAEd,SAAS;AAC5BK,kBAAc3G,GAAG;AACjB,WAAO,MAAMoF,OAAOiC,cAAcrH,GAAG;EACvC,GAAG,CAACoF,MAAM,CAAC;AAMXlK,EAAMiM,gBAAU,MAAM;AACpB,QAAIT,eAAe,IAAI;AACrBtB,aAAOkC,WAAWZ,YAAYG,eAAe;IAC/C;KACC,CAACzB,QAAQsB,YAAYG,eAAe,CAAC;AAIxC,SAAOH,cAAc9J,MAAM2K,SAASC,IAAId,UAAU,IAC9C9J,MAAM2K,SAASE,IAAIf,UAAU,IAC7BgB;AACN;AAMA,SAASrM,oBAAsC;AAC7C,MAAI;IAAE+J;EAAO,IAAId,qBAAqBJ,eAAeyD,iBAAiB;AACtE,MAAIlF,KAAKiC,kBAAkBP,oBAAoBwD,iBAAiB;AAEhE,MAAI5L,YAAkBC,aAAO,KAAK;AAClClB,4BAA0B,MAAM;AAC9BiB,cAAUE,UAAU;EACtB,CAAC;AAED,MAAIC,WAAmCC,kBACrC,SAAC9C,IAAiB+C,SAAkC;AAAA,QAAlCA,YAAwB,QAAA;AAAxBA,gBAA2B,CAAA;IAAE;AAC7C3D,WAAA4D,QAAQN,UAAUE,SAASpB,qBAAqB,IAAC;AAIjD,QAAI,CAACkB,UAAUE,QAAS;AAExB,QAAI,OAAO5C,OAAO,UAAU;AAC1B+L,aAAOlJ,SAAS7C,EAAE;IACpB,OAAO;AACL+L,aAAOlJ,SAAS7C,IAAE0G,UAAA;QAAI6H,aAAanF;SAAOrG,OAAO,CAAE;IACrD;EACF,GACA,CAACgJ,QAAQ3C,EAAE,CACb;AAEA,SAAOvG;AACT;AAEA,IAAM2L,gBAAyC,CAAA;AAE/C,SAASvJ,YAAY0B,KAAa8H,MAAexH,SAAiB;AAChE,MAAI,CAACwH,QAAQ,CAACD,cAAc7H,GAAG,GAAG;AAChC6H,kBAAc7H,GAAG,IAAI;AACrBvH,WAAA4D,QAAQ,OAAOiE,OAAO,IAAC;EACzB;AACF;AC3gCA,IAAMyH,mBAAmB;AACzB,IAAMC,sBAAsB9M,MAAM6M,gBAAgB;AAK3C,SAASE,eAAc7F,MAIc;AAAA,MAJb;IAC7B8F;IACA9C;IACA5J;EACmB,IAAC4G;AACpB,MAAI,CAACxF,OAAOuL,YAAY,IAAUvB,eAASxB,OAAOxI,KAAK;AACvD,MAAI;IAAEwL;EAAmB,IAAI5M,UAAU,CAAA;AAEvC,MAAI6M,WAAiBlM,kBAClBmM,cAA0B;AACzB,QAAIF,sBAAsBJ,qBAAqB;AAC7CA,0BAAoB,MAAMG,aAAaG,QAAQ,CAAC;IAClD,OAAO;AACLH,mBAAaG,QAAQ;IACvB;EACF,GACA,CAACH,cAAcC,kBAAkB,CACnC;AAIAlN,EAAMC,sBAAgB,MAAMiK,OAAOmD,UAAUF,QAAQ,GAAG,CAACjD,QAAQiD,QAAQ,CAAC;AAE1EnN,EAAMiM,gBAAU,MAAM;AACpB1O,WAAA4D,QACE6L,mBAAmB,QAAQ,CAAC9C,OAAO5J,OAAOqH,qBAC1C,8HAEF,IAAC;KAGA,CAAA,CAAE;AAEL,MAAIlJ,YAAkBe,cAAQ,MAAiB;AAC7C,WAAO;MACLP,YAAYiL,OAAOjL;MACnB2F,gBAAgBsF,OAAOtF;MACvBxD,IAAKkM,OAAMpD,OAAOlJ,SAASsM,CAAC;MAC5B7L,MAAMA,CAACtD,IAAIuD,QAAO6L,SAChBrD,OAAOlJ,SAAS7C,IAAI;QAClBuD,OAAAA;QACA8L,oBAAoBD,QAAAA,OAAAA,SAAAA,KAAMC;MAC5B,CAAC;MACHhM,SAASA,CAACrD,IAAIuD,QAAO6L,SACnBrD,OAAOlJ,SAAS7C,IAAI;QAClBqD,SAAS;QACTE,OAAAA;QACA8L,oBAAoBD,QAAAA,OAAAA,SAAAA,KAAMC;OAC3B;;EAEP,GAAG,CAACtD,MAAM,CAAC;AAEX,MAAI1L,WAAW0L,OAAO1L,YAAY;AAElC,MAAI6B,oBAA0Bb,cAC5B,OAAO;IACL0K;IACAzL;IACAsB,QAAQ;IACRvB;MAEF,CAAC0L,QAAQzL,WAAWD,QAAQ,CAC9B;AAQA,SACEuD,oBAAAkE,gBACEjG,MAAA+B,oBAAC1E,kBAAkB2E,UAAQ;IAACC,OAAO5B;EAAkB,GACnD0B,oBAACtE,uBAAuBuE,UAAQ;IAACC,OAAOP;EAAM,GAC5CK,oBAAC0L,QAAM;IACLjP;IACAW,UAAUuC,MAAMvC;IAChBE,gBAAgBqC,MAAMqK;IACtBtN;IACA6B,QAAQ;MACNM,sBAAsBsJ,OAAO5J,OAAOM;IACtC;EAAE,GAEDc,MAAMkG,eAAesC,OAAO5J,OAAOqH,sBAClC5F,oBAAC2L,YAAU;IACTlL,QAAQ0H,OAAO1H;IACflC,QAAQ4J,OAAO5J;IACfoB;GACD,IAEDsL,eAEI,CACuB,CACP,GAC3B,IACD;AAEN;AAEA,SAASU,WAAUC,OAQW;AAAA,MARV;IAClBnL;IACAlC;IACAoB;EAKF,IAACiM;AACC,SAAOjL,cAAcF,QAAQ2B,QAAWzC,OAAOpB,MAAM;AACvD;AAeO,SAASsN,aAAYC,OAMc;AAAA,MANb;IAC3BrP;IACAuI;IACA+G;IACAC;IACAzN;EACiB,IAACuN;AAClB,MAAIG,aAAmBlN,aAAM;AAC7B,MAAIkN,WAAWjN,WAAW,MAAM;AAC9BiN,eAAWjN,UAAUkN,oBAAoB;MACvCH;MACAC;MACAG,UAAU;IACZ,CAAC;EACH;AAEA,MAAIC,UAAUH,WAAWjN;AACzB,MAAI,CAACW,OAAOuL,YAAY,IAAUvB,eAAS;IACzC0C,QAAQD,QAAQC;IAChBjP,UAAUgP,QAAQhP;EACpB,CAAC;AACD,MAAI;IAAE+N;EAAmB,IAAI5M,UAAU,CAAA;AACvC,MAAI6M,WAAiBlM,kBAClBmM,cAA6D;AAC5DF,0BAAsBJ,sBAClBA,oBAAoB,MAAMG,aAAaG,QAAQ,CAAC,IAChDH,aAAaG,QAAQ;EAC3B,GACA,CAACH,cAAcC,kBAAkB,CACnC;AAEAlN,EAAMC,sBAAgB,MAAMkO,QAAQE,OAAOlB,QAAQ,GAAG,CAACgB,SAAShB,QAAQ,CAAC;AAEzE,SACEpL,oBAAC0L,QAAM;IACLjP;IACAuI;IACA5H,UAAUuC,MAAMvC;IAChBE,gBAAgBqC,MAAM0M;IACtB3P,WAAW0P;IACX7N;EAAe,CAChB;AAEL;AAkBO,SAASgO,SAAQC,OAKA;AAAA,MALC;IACvBpQ;IACAqD,SAAAA;IACAE;IACArD;EACa,IAACkQ;AACd,GACEjQ,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAE+B;IAAQP,QAAQD;EAAS,IAAUpB,iBAAWf,iBAAiB;AAErEJ,SAAA4D,QACE,CAACrB,UACD,uNAGF,IAAC;AAED,MAAI;IAAE/B;EAAQ,IAAUW,iBAAWb,YAAY;AAC/C,MAAI;IAAEe,UAAU2B;MAAqBrB,YAAW;AAChD,MAAI8B,WAAWd,YAAW;AAI1B,MAAImB,OAAOC,UACTnD,IACAwC,oBAAoB5C,SAASuC,OAAOM,oBAAoB,GACxDL,kBACAlC,aAAa,MACf;AACA,MAAImQ,WAAW/N,KAAKC,UAAUW,IAAI;AAElCrB,EAAMiM,gBACJ,MAAMjL,SAASP,KAAKc,MAAMiN,QAAQ,GAAG;IAAEhN,SAAAA;IAASE;IAAOrD;EAAS,CAAC,GACjE,CAAC2C,UAAUwN,UAAUnQ,UAAUmD,UAASE,KAAK,CAC/C;AAEA,SAAO;AACT;AAWO,SAAS+M,OAAOlI,OAA+C;AACpE,SAAO1E,UAAU0E,MAAMzE,OAAO;AAChC;AAmDO,SAAS4M,MAAMC,QAA+C;AAE5DpR,SADPgB,UAAS,OAEP,sIACoE,IAHtEA,UAAS,KAAA;AAKX;AAqBO,SAASkP,OAAMmB,OAQqB;AAAA,MARpB;IACrBpQ,UAAUqQ,eAAe;IACzB9H,WAAW;IACX5H,UAAU2P;IACVzP,iBAAiB0F,OAAeC;IAChCvG;IACAsB,QAAQgP,aAAa;IACrBzO;EACW,IAACsO;AACZ,GACE,CAACtQ,mBAAkB,IAAEf,OADvBgB,UAEE,OAAA,wGACqD,IAHvDA,UAAS,KAAA,IAAA;AAQT,MAAIC,WAAWqQ,aAAarN,QAAQ,QAAQ,GAAG;AAC/C,MAAIwN,oBAA0BxP,cAC5B,OAAO;IACLhB;IACAC;IACAsB,QAAQgP;IACRzO,QAAMuE,UAAA;MACJjE,sBAAsB;IAAK,GACxBN,MAAM;MAGb,CAAC9B,UAAU8B,QAAQ7B,WAAWsQ,UAAU,CAC1C;AAEA,MAAI,OAAOD,iBAAiB,UAAU;AACpCA,mBAAerL,UAAUqL,YAAY;EACvC;AAEA,MAAI;IACFlQ,WAAW;IACXC,SAAS;IACTF,OAAO;IACP+C,QAAQ;IACRoD,MAAM;EACR,IAAIgK;AAEJ,MAAIG,kBAAwBzP,cAAQ,MAAM;AACxC,QAAI0P,mBAAmBlD,cAAcpN,UAAUJ,QAAQ;AAEvD,QAAI0Q,oBAAoB,MAAM;AAC5B,aAAO;IACT;AAEA,WAAO;MACL/P,UAAU;QACRP,UAAUsQ;QACVrQ;QACAF;QACA+C;QACAoD;;MAEFzF;;EAEJ,GAAG,CAACb,UAAUI,UAAUC,QAAQF,MAAM+C,OAAOoD,KAAKzF,cAAc,CAAC;AAEjE9B,SAAA4D,QACE8N,mBAAmB,MACnB,uBAAqBzQ,WAAQ,sCAAA,MACvBI,WAAWC,SAASF,OAA2C,2CAAA,kDAEvE,IAAC;AAED,MAAIsQ,mBAAmB,MAAM;AAC3B,WAAO;EACT;AAEA,SACElN,oBAACpE,kBAAkBqE,UAAQ;IAACC,OAAO+M;EAAkB,GACnDjN,oBAACnE,gBAAgBoE,UAAQ;IAAC+E;IAAoB9E,OAAOgN;EAAgB,CAAE,CAC7C;AAEhC;AAaO,SAASE,OAAMC,OAGqB;AAAA,MAHpB;IACrBrI;IACA5H;EACW,IAACiQ;AACZ,SAAO7M,UAAU8M,yBAAyBtI,QAAQ,GAAG5H,QAAQ;AAC/D;AAgBO,SAASmQ,MAAKC,OAAkD;AAAA,MAAjD;IAAExI;IAAUK;IAAcoI;EAAoB,IAACD;AACnE,SACExN,oBAAC0N,oBAAkB;IAACD;IAAkBpI;KACpCrF,oBAAC2N,cAAc3I,MAAAA,QAAuB,CACpB;AAExB;AAAC,IAWI4I,oBAAiB,SAAjBA,oBAAiB;AAAjBA,EAAAA,mBAAAA,mBAAiB,SAAA,IAAA,CAAA,IAAA;AAAjBA,EAAAA,mBAAAA,mBAAiB,SAAA,IAAA,CAAA,IAAA;AAAjBA,EAAAA,mBAAAA,mBAAiB,OAAA,IAAA,CAAA,IAAA;AAAA,SAAjBA;AAAiB,EAAjBA,qBAAiB,CAAA,CAAA;AAMtB,IAAMC,sBAAsB,IAAIC,QAAQ,MAAM;AAAA,CAAE;AAEhD,IAAMJ,qBAAN,cAAuCrL,gBAGrC;EACAkC,YAAYC,OAAgC;AAC1C,UAAMA,KAAK;AACX,SAAK7E,QAAQ;MAAEwD,OAAO;;EACxB;EAEA,OAAOuB,yBAAyBvB,OAAY;AAC1C,WAAO;MAAEA;;EACX;EAEAyB,kBAAkBzB,OAAY0B,WAAgB;AAC5CZ,YAAQd,MACN,oDACAA,OACA0B,SACF;EACF;EAEAC,SAAS;AACP,QAAI;MAAEE;MAAUK;MAAcoI;QAAY,KAAKjJ;AAE/C,QAAIuJ,UAAiC;AACrC,QAAIxK,SAA4BqK,kBAAkBI;AAElD,QAAI,EAAEP,mBAAmBK,UAAU;AAEjCvK,eAASqK,kBAAkBK;AAC3BF,gBAAUD,QAAQL,QAAO;AACzB9K,aAAOuL,eAAeH,SAAS,YAAY;QAAEvD,KAAKA,MAAM;MAAK,CAAC;AAC9D7H,aAAOuL,eAAeH,SAAS,SAAS;QAAEvD,KAAKA,MAAMiD;MAAQ,CAAC;IAChE,WAAW,KAAK9N,MAAMwD,OAAO;AAE3BI,eAASqK,kBAAkBzK;AAC3B,UAAIgL,cAAc,KAAKxO,MAAMwD;AAC7B4K,gBAAUD,QAAQM,OAAM,EAAGC,MAAM,MAAM;MAAA,CAAE;AACzC1L,aAAOuL,eAAeH,SAAS,YAAY;QAAEvD,KAAKA,MAAM;MAAK,CAAC;AAC9D7H,aAAOuL,eAAeH,SAAS,UAAU;QAAEvD,KAAKA,MAAM2D;MAAY,CAAC;IACrE,WAAYV,QAA2Ba,UAAU;AAE/CP,gBAAUN;AACVlK,eACE,YAAYwK,UACRH,kBAAkBzK,QAClB,WAAW4K,UACXH,kBAAkBK,UAClBL,kBAAkBI;IAC1B,OAAO;AAELzK,eAASqK,kBAAkBI;AAC3BrL,aAAOuL,eAAeT,SAAS,YAAY;QAAEjD,KAAKA,MAAM;MAAK,CAAC;AAC9DuD,gBAAUN,QAAQc,KACfC,UACC7L,OAAOuL,eAAeT,SAAS,SAAS;QAAEjD,KAAKA,MAAMgE;OAAM,GAC5DrL,WACCR,OAAOuL,eAAeT,SAAS,UAAU;QAAEjD,KAAKA,MAAMrH;MAAM,CAAC,CACjE;IACF;AAEA,QACEI,WAAWqK,kBAAkBzK,SAC7B4K,QAAQ3E,kBAAkBqF,sBAC1B;AAEA,YAAMZ;IACR;AAEA,QAAItK,WAAWqK,kBAAkBzK,SAAS,CAACkC,cAAc;AAEvD,YAAM0I,QAAQ3E;IAChB;AAEA,QAAI7F,WAAWqK,kBAAkBzK,OAAO;AAEtC,aAAOnD,oBAACrE,aAAasE,UAAQ;QAACC,OAAO6N;QAAS/I,UAAUK;MAAa,CAAE;IACzE;AAEA,QAAI9B,WAAWqK,kBAAkBK,SAAS;AAExC,aAAOjO,oBAACrE,aAAasE,UAAQ;QAACC,OAAO6N;QAAS/I;MAAmB,CAAE;IACrE;AAGA,UAAM+I;EACR;AACF;AAMA,SAASJ,aAAYe,OAIlB;AAAA,MAJmB;IACpB1J;EAGF,IAAC0J;AACC,MAAIF,OAAOvF,cAAa;AACxB,MAAI0F,WAAW,OAAO3J,aAAa,aAAaA,SAASwJ,IAAI,IAAIxJ;AACjE,SAAOhF,oBAAAkE,gBAAGyK,MAAAA,QAAW;AACvB;AAaO,SAASrB,yBACdtI,UACA5D,YACe;AAAA,MADfA,eAAoB,QAAA;AAApBA,iBAAuB,CAAA;EAAE;AAEzB,MAAIX,SAAwB,CAAA;AAE5BxC,EAAM2Q,eAASC,QAAQ7J,UAAU,CAAC7C,SAAS0E,UAAU;AACnD,QAAI,CAAOiI,qBAAe3M,OAAO,GAAG;AAGlC;IACF;AAEA,QAAI4M,WAAW,CAAC,GAAG3N,YAAYyF,KAAK;AAEpC,QAAI1E,QAAQ6M,SAAe9K,gBAAU;AAEnCzD,aAAOf,KAAKuP,MACVxO,QACA6M,yBAAyBnL,QAAQqC,MAAMQ,UAAU+J,QAAQ,CAC3D;AACA;IACF;AAEA,MACE5M,QAAQ6M,SAASrC,SAAKnR,OADxBgB,UAGI,OAAA,OAAA,OAAO2F,QAAQ6M,SAAS,WAAW7M,QAAQ6M,OAAO7M,QAAQ6M,KAAKE,QAAI,wGAAA,IAHvE1S,UAAS,KAAA,IAAA;AAOT,MACE,CAAC2F,QAAQqC,MAAMqC,SAAS,CAAC1E,QAAQqC,MAAMQ,YAAQxJ,OADjDgB,UAAS,OAEP,0CAA0C,IAF5CA,UAAS,KAAA,IAAA;AAKT,QAAI2E,QAAqB;MACvBqE,IAAIrD,QAAQqC,MAAMgB,MAAMuJ,SAAS9M,KAAK,GAAG;MACzCkN,eAAehN,QAAQqC,MAAM2K;MAC7BhN,SAASA,QAAQqC,MAAMrC;MACvBE,WAAWF,QAAQqC,MAAMnC;MACzBwE,OAAO1E,QAAQqC,MAAMqC;MACrBvH,MAAM6C,QAAQqC,MAAMlF;MACpBqH,QAAQxE,QAAQqC,MAAMmC;MACtB0F,QAAQlK,QAAQqC,MAAM6H;MACtBhH,cAAclD,QAAQqC,MAAMa;MAC5BC,eAAenD,QAAQqC,MAAMc;MAC7B8J,kBACEjN,QAAQqC,MAAMc,iBAAiB,QAC/BnD,QAAQqC,MAAMa,gBAAgB;MAChCgK,kBAAkBlN,QAAQqC,MAAM6K;MAChCC,QAAQnN,QAAQqC,MAAM8K;MACtBhN,MAAMH,QAAQqC,MAAMlC;;AAGtB,QAAIH,QAAQqC,MAAMQ,UAAU;AAC1B7D,YAAM6D,WAAWsI,yBACfnL,QAAQqC,MAAMQ,UACd+J,QACF;IACF;AAEAtO,WAAOf,KAAKyB,KAAK;EACnB,CAAC;AAED,SAAOV;AACT;AAKO,SAAS8O,cACdvT,SAC2B;AAC3B,SAAOwG,eAAexG,OAAO;AAC/B;ACtfA,SAASwT,mBAAmBrO,OAAoB;AAC9C,MAAIsO,UAAgE;;;IAGlEL,kBAAkBjO,MAAMmE,iBAAiB,QAAQnE,MAAMkE,gBAAgB;;AAGzE,MAAIlE,MAAMkB,WAAW;AACnB,QAAA7G,MAAa;AACX,UAAI2F,MAAMgB,SAAS;AACjB3G,eAAA4D,QACE,OACA,iGAEF,IAAC;MACH;IACF;AACAuD,WAAOC,OAAO6M,SAAS;MACrBtN,SAAenC,oBAAcmB,MAAMkB,SAAS;MAC5CA,WAAWD;IACb,CAAC;EACH;AAEA,MAAIjB,MAAMoF,iBAAiB;AACzB,QAAA/K,MAAa;AACX,UAAI2F,MAAMqF,wBAAwB;AAChChL,eAAA4D,QACE,OACA,4HAEF,IAAC;MACH;IACF;AACAuD,WAAOC,OAAO6M,SAAS;MACrBjJ,wBAA8BxG,oBAAcmB,MAAMoF,eAAe;MACjEA,iBAAiBnE;IACnB,CAAC;EACH;AAEA,MAAIjB,MAAMmE,eAAe;AACvB,QAAA9J,MAAa;AACX,UAAI2F,MAAMkE,cAAc;AACtB7J,eAAA4D,QACE,OACA,8GAEF,IAAC;MACH;IACF;AACAuD,WAAOC,OAAO6M,SAAS;MACrBpK,cAAoBrF,oBAAcmB,MAAMmE,aAAa;MACrDA,eAAelD;IACjB,CAAC;EACH;AAEA,SAAOqN;AACT;AAKO,SAASC,mBACdjP,QACA+K,MASa;AACb,SAAOmE,aAAa;IAClBlT,UAAU+O,QAAAA,OAAAA,SAAAA,KAAM/O;IAChB8B,QAAMuE,UAAA,CAAA,GACD0I,QAAAA,OAAAA,SAAAA,KAAMjN,QAAM;MACfqR,oBAAoB;KACrB;IACDxD,SAASF,oBAAoB;MAC3BH,gBAAgBP,QAAAA,OAAAA,SAAAA,KAAMO;MACtBC,cAAcR,QAAAA,OAAAA,SAAAA,KAAMQ;IACtB,CAAC;IACD6D,eAAerE,QAAAA,OAAAA,SAAAA,KAAMqE;IACrBpP;IACA+O;IACAM,uBAAuBtE,QAAAA,OAAAA,SAAAA,KAAMsE;IAC7BC,kCAAkCvE,QAAAA,OAAAA,SAAAA,KAAMuE;EAC1C,CAAC,EAAEC,WAAU;AACf;", "names": ["Action", "PopStateEventType", "createMemoryHistory", "options", "initialEntries", "initialIndex", "v5Compat", "entries", "map", "entry", "index", "createMemoryLocation", "state", "undefined", "clampIndex", "length", "action", "Pop", "listener", "n", "Math", "min", "max", "getCurrentLocation", "to", "key", "location", "createLocation", "pathname", "warning", "char<PERSON>t", "JSON", "stringify", "createHref", "createPath", "history", "createURL", "URL", "encodeLocation", "path", "parsePath", "search", "hash", "push", "<PERSON><PERSON>", "nextLocation", "splice", "delta", "replace", "Replace", "go", "nextIndex", "listen", "fn", "createBrowserHistory", "createBrowserLocation", "window", "globalHistory", "usr", "createBrowserHref", "getUrlBasedHistory", "createHashHistory", "createHashLocation", "substr", "startsWith", "createHashHref", "base", "document", "querySelector", "href", "getAttribute", "url", "hashIndex", "indexOf", "slice", "validateHashLocation", "invariant", "value", "message", "Error", "cond", "console", "warn", "e", "create<PERSON><PERSON>", "random", "toString", "getHistoryState", "idx", "current", "_extends", "_ref", "parsed<PERSON><PERSON>", "searchIndex", "getLocation", "validateLocation", "defaultView", "getIndex", "replaceState", "handlePop", "historyState", "pushState", "error", "DOMException", "name", "assign", "origin", "addEventListener", "removeEventListener", "ResultType", "immutableRouteKeys", "Set", "isIndexRoute", "route", "convertRoutesToDataRoutes", "routes", "mapRouteProperties", "parentPath", "manifest", "treePath", "String", "id", "join", "children", "indexRoute", "pathOrLayoutRoute", "matchRoutes", "locationArg", "basename", "matchRoutesImpl", "allowPartial", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "matches", "i", "decoded", "decodePath", "matchRouteBranch", "convertRouteMatchToUiMatch", "match", "loaderData", "params", "data", "handle", "parents<PERSON>eta", "flattenRoute", "relativePath", "meta", "caseSensitive", "childrenIndex", "joinPaths", "routesMeta", "concat", "score", "computeScore", "for<PERSON>ach", "_route$path", "includes", "exploded", "explodeOptionalSegments", "segments", "split", "first", "rest", "isOptional", "endsWith", "required", "restExploded", "result", "subpath", "sort", "a", "b", "compareIndexes", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "initialScore", "some", "filter", "reduce", "segment", "test", "siblings", "every", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "matchPath", "Object", "pathnameBase", "normalizePathname", "generatePath", "originalPath", "prefix", "p", "array", "isLastSegment", "star", "keyMatch", "optional", "param", "pattern", "matcher", "compiledParams", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "regexpSource", "_", "RegExp", "v", "decodeURIComponent", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "getInvalidPathError", "char", "field", "dest", "getPathContributingMatches", "getResolveToMatches", "v7_relativeSplatPath", "pathMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "isEmptyPath", "from", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "joinPaths", "paths", "join", "replace", "normalizePathname", "pathname", "normalizeSearch", "search", "startsWith", "normalizeHash", "hash", "json", "data", "init", "responseInit", "status", "headers", "Headers", "has", "set", "Response", "JSON", "stringify", "_extends", "Aborted<PERSON>eferredError", "Error", "DeferredData", "constructor", "data", "responseInit", "pendingKeysSet", "Set", "subscribers", "deferred<PERSON><PERSON><PERSON>", "invariant", "Array", "isArray", "reject", "abortPromise", "Promise", "_", "r", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "signal", "removeEventListener", "addEventListener", "Object", "entries", "reduce", "acc", "_ref2", "key", "value", "assign", "trackPromise", "done", "init", "push", "add", "promise", "race", "then", "onSettle", "undefined", "error", "catch", "defineProperty", "get", "aborted", "delete", "undefinedError", "emit", "<PERSON><PERSON><PERSON>", "for<PERSON>ach", "subscriber", "subscribe", "fn", "cancel", "abort", "v", "k", "resolveData", "resolve", "size", "unwrappedData", "_ref3", "unwrapTrackedPromise", "<PERSON><PERSON><PERSON><PERSON>", "from", "isTrackedPromise", "_tracked", "_error", "_data", "defer", "status", "redirect", "url", "headers", "Headers", "set", "Response", "_extends", "redirectDocument", "response", "replace", "ErrorResponseImpl", "statusText", "internal", "toString", "isRouteErrorResponse", "validMutationMethodsArr", "validMutationMethods", "validRequestMethodsArr", "validRequestMethods", "redirectStatusCodes", "redirectPreserveMethodStatusCodes", "IDLE_NAVIGATION", "state", "location", "formMethod", "formAction", "formEncType", "formData", "json", "text", "IDLE_FETCHER", "IDLE_BLOCKER", "proceed", "reset", "ABSOLUTE_URL_REGEX", "defaultMapRouteProperties", "route", "hasErrorBou<PERSON>ry", "Boolean", "TRANSITIONS_STORAGE_KEY", "createRouter", "routerWindow", "window", "<PERSON><PERSON><PERSON><PERSON>", "document", "createElement", "isServer", "routes", "length", "mapRouteProperties", "detectErrorBoundary", "manifest", "dataRoutes", "convertRoutesToDataRoutes", "inFlightDataRoutes", "basename", "dataStrategyImpl", "unstable_dataStrategy", "defaultDataStrategy", "patchRoutesOnNavigationImpl", "unstable_patchRoutesOnNavigation", "future", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_partialHydration", "v7_prependBasename", "v7_relativeSplatPath", "v7_skipActionErrorRevalidation", "unlistenHistory", "discoveredRoutesMaxSize", "discoveredRoutes", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "hydrationData", "initialMatches", "matchRoutes", "history", "initialErrors", "getInternalRouterError", "pathname", "matches", "getShortCircuitMatches", "id", "fogOfWar", "checkFogOfWar", "active", "initialized", "some", "m", "lazy", "loader", "loaderData", "errors", "isRouteInitialized", "hydrate", "idx", "findIndex", "slice", "every", "router", "historyAction", "action", "navigation", "restoreScrollPosition", "preventScrollReset", "revalidation", "actionData", "fetchers", "Map", "blockers", "pendingAction", "HistoryAction", "Pop", "pendingPreventScrollReset", "pendingNavigationController", "pendingViewTransitionEnabled", "appliedViewTransitions", "removePageHideEventListener", "isUninterruptedRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "fetchRedirectIds", "fetchLoadMatches", "activeFetchers", "deletedFetchers", "activeDeferreds", "blockerFunctions", "pendingPatchRoutes", "unblockBlockerHistoryUpdate", "initialize", "listen", "_ref", "delta", "warning", "blockerKey", "shouldBlockNavigation", "currentLocation", "nextLocation", "nextHistoryUpdatePromise", "go", "updateBlocker", "updateState", "startNavigation", "restoreAppliedTransitions", "_saveAppliedTransitions", "persistAppliedTransitions", "initialHydration", "dispose", "clear", "deleteFetcher", "deleteBlocker", "newState", "opts", "completedFetchers", "deletedFetchersKeys", "fetcher", "has", "unstable_viewTransitionOpts", "viewTransitionOpts", "unstable_flushSync", "flushSync", "completeNavigation", "_temp", "_location$state", "_location$state2", "isActionReload", "isMutationMethod", "_isRedirect", "keys", "mergeLoaderData", "<PERSON><PERSON>", "Replace", "priorPaths", "toPaths", "getSavedScrollPosition", "navigate", "to", "normalizedPath", "normalizeTo", "fromRouteId", "relative", "path", "submission", "normalizeNavigateOptions", "createLocation", "encodeLocation", "userReplace", "search", "pendingError", "enableViewTransition", "unstable_viewTransition", "revalidate", "interruptActiveLoads", "startUninterruptedRevalidation", "overrideNavigation", "saveScrollPosition", "routesToUse", "loadingNavigation", "notFoundMatches", "handleNavigational404", "isHashChangeOnly", "request", "createClientSideRequest", "pendingActionResult", "findNearestBoundary", "type", "ResultType", "actionResult", "handleAction", "shortCircuited", "routeId", "result", "isErrorResult", "getLoadingNavigation", "updatedMatches", "handleLoaders", "fetcherSubmission", "getActionDataForCommit", "isFogOfWar", "getSubmittingNavigation", "discoverResult", "discoverRoutes", "boundaryId", "handleDiscoverRouteError", "partialMatch<PERSON>", "actionMatch", "getTargetMatch", "method", "results", "callDataStrategy", "isRedirectResult", "normalizeRedirectLocation", "URL", "startRedirectNavigation", "isDeferredResult", "boundaryMatch", "activeSubmission", "getSubmissionFromNavigation", "shouldUpdateNavigationState", "getUpdatedActionData", "matchesToLoad", "revalidatingFetchers", "getMatchesToLoad", "cancelActiveDeferreds", "updatedFetchers", "markFetchRedirectsDone", "updates", "getUpdatedRevalidatingFetchers", "rf", "abort<PERSON><PERSON><PERSON>", "abortPendingFetchRevalidations", "f", "loaderResults", "fetcherResults", "callLoadersAndMaybeResolveData", "findRedirect", "processLoaderData", "deferredData", "filter", "didAbortFetchLoads", "abortStaleFetchLoads", "shouldUpdateFetchers", "revalidatingFetcher", "getLoadingFetcher", "fetch", "href", "setFetcherError", "match", "handleFetcherAction", "handleFetcherLoader", "requestMatches", "detectAndHandle405Error", "existingFetcher", "updateFetcherState", "getSubmittingFetcher", "abortController", "fetchRequest", "originatingLoadId", "actionResults", "getDoneFetcher", "revalidationRequest", "loadId", "loadFetcher", "staleKey", "done<PERSON>etcher", "resolveDeferredData", "isNavigation", "_temp2", "redirectLocation", "isDocumentReload", "test", "createURL", "origin", "stripBasename", "redirectHistoryAction", "fetcher<PERSON>ey", "dataResults", "callDataStrategyImpl", "e", "isRedirectDataStrategyResultResult", "normalizeRelativeRoutingRedirectResponse", "convertDataStrategyResultToDataResult", "fetchersToLoad", "currentMatches", "loaderResultsPromise", "fetcherResultsPromise", "all", "map", "resolveNavigationDeferredResults", "resolveFetcherDeferredResults", "getFetcher", "deleteFetcherAndUpdateState", "count", "markFetchersDone", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "get<PERSON><PERSON>er", "blocker", "newBlocker", "_ref4", "blockerFunction", "message", "String", "predicate", "cancelledRouteIds", "dfd", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "y", "getScrollKey", "convertRouteMatchToUiMatch", "fogMatches", "matchRoutesImpl", "params", "isNonHMR", "loadLazyRouteChildren", "newMatches", "addToFifoQueue", "newPartialMatches", "i", "queue", "first", "values", "next", "_internalSetRoutes", "newRoutes", "patchRoutes", "children", "patchRoutesImpl", "createHref", "_internalFetchControllers", "_internalActiveDeferreds", "UNSAFE_DEFERRED_SYMBOL", "Symbol", "isSubmissionNavigation", "opts", "formData", "body", "undefined", "normalizeTo", "location", "matches", "basename", "prependBasename", "to", "v7_relativeSplatPath", "fromRouteId", "relative", "contextualMatches", "activeRouteMatch", "match", "push", "route", "id", "length", "path", "resolveTo", "getResolveToMatches", "stripBasename", "pathname", "search", "hash", "index", "hasNakedIndexQuery", "replace", "joinPaths", "createPath", "normalizeNavigateOptions", "normalizeFormMethod", "isFetcher", "formMethod", "isValidMethod", "error", "getInternalRouterError", "method", "getInvalidBodyError", "type", "rawFormMethod", "toUpperCase", "toLowerCase", "formAction", "stripHashFromPath", "formEncType", "isMutationMethod", "text", "FormData", "URLSearchParams", "Array", "from", "entries", "reduce", "acc", "_ref5", "name", "value", "String", "submission", "json", "JSON", "parse", "e", "invariant", "searchParams", "convertFormDataToSearchParams", "convertSearchParamsToFormData", "parsed<PERSON><PERSON>", "parsePath", "append", "getLoaderMatchesUntilBoundary", "boundaryId", "boundaryMatches", "findIndex", "m", "slice", "getMatchesToLoad", "history", "state", "isInitialLoad", "skipActionErrorRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "deletedFetchers", "fetchLoadMatches", "fetchRedirectIds", "routesToUse", "pendingActionResult", "actionResult", "isErrorResult", "data", "currentUrl", "createURL", "nextUrl", "actionStatus", "statusCode", "shouldSkipRevalidation", "navigationMatches", "filter", "lazy", "loader", "hydrate", "loaderData", "errors", "is<PERSON>ew<PERSON><PERSON>der", "some", "currentRouteMatch", "nextRouteMatch", "shouldRevalidateLoader", "_extends", "currentParams", "params", "nextParams", "defaultShouldRevalidate", "isNewRouteInstance", "revalidatingFetchers", "for<PERSON>ach", "f", "key", "routeId", "has", "fetcherMatches", "matchRoutes", "controller", "fetcher", "fetchers", "get", "fetcherMatch", "getTargetMatch", "shouldRevalidate", "delete", "AbortController", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "currentPath", "endsWith", "loaderMatch", "arg", "routeChoice", "loadLazyRouteChildren", "patchRoutesOnNavigationImpl", "routes", "manifest", "mapRouteProperties", "pendingRouteChildren", "signal", "map", "join", "pending", "patch", "children", "aborted", "patchRoutesImpl", "set", "isPromise", "_route$children", "dataChildren", "convertRoutesToDataRoutes", "loadLazyRouteModule", "lazyRoute", "routeToUpdate", "routeUpdates", "lazyRouteProperty", "staticRouteValue", "isPropertyStaticallyDefined", "warning", "immutableRouteKeys", "Object", "assign", "defaultDataStrategy", "_ref6", "matchesToLoad", "shouldLoad", "results", "Promise", "all", "resolve", "result", "i", "callDataStrategyImpl", "dataStrategyImpl", "request", "fetcher<PERSON>ey", "requestContext", "loadRouteDefinitionsPromises", "dsMatches", "loadRoutePromise", "handlerOverride", "callLoaderOrAction", "ResultType", "context", "staticContext", "onReject", "<PERSON><PERSON><PERSON><PERSON>", "handler", "reject", "abortPromise", "_", "r", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "ctx", "Error", "handlerPromise", "val", "race", "handlerError", "catch", "url", "URL", "removeEventListener", "convertDataStrategyResultToDataResult", "dataStrategyResult", "isResponse", "contentType", "headers", "test", "ErrorResponseImpl", "status", "statusText", "isDataWithResponseInit", "_result$init2", "_result$init", "init", "isRouteErrorResponse", "isDeferredData", "_result$init3", "_result$init4", "deferred", "deferredData", "Headers", "_result$init5", "_result$init6", "normalizeRelativeRoutingRedirectResponse", "response", "ABSOLUTE_URL_REGEX", "trimmedMatches", "normalizeRedirectLocation", "normalizedLocation", "startsWith", "protocol", "isSameBasename", "origin", "createClientSideRequest", "toString", "stringify", "Request", "processRouteLoaderData", "activeDeferreds", "skipL<PERSON>derError<PERSON><PERSON>bling", "found<PERSON><PERSON>r", "loaderHeaders", "pendingError", "isRedirectResult", "boundaryMatch", "findNearestBoundary", "isDeferredResult", "processLoaderData", "fetcherResults", "rf", "done<PERSON>etcher", "getDoneFetcher", "mergeLoaderData", "newLoaderData", "mergedLoaderData", "hasOwnProperty", "getActionDataForCommit", "actionData", "eligibleMatches", "reverse", "find", "hasErrorBou<PERSON>ry", "getShortCircuitMatches", "pathnameBase", "_temp5", "message", "errorMessage", "findRedirect", "isHashChangeOnly", "a", "b", "isRedirectDataStrategyResultResult", "result", "isResponse", "redirectStatusCodes", "has", "status", "isDeferredResult", "type", "ResultType", "deferred", "isErrorResult", "error", "isRedirectResult", "redirect", "isDataWithResponseInit", "value", "isDeferredData", "data", "subscribe", "cancel", "resolveData", "statusText", "headers", "body", "isValidMethod", "method", "validRequestMethods", "has", "toLowerCase", "isMutationMethod", "validMutationMethods", "resolveNavigationDeferredResults", "matches", "results", "signal", "currentMatches", "currentLoaderData", "entries", "Object", "index", "length", "routeId", "result", "match", "find", "m", "route", "id", "currentMatch", "isRevalidatingLoader", "isNewRouteInstance", "undefined", "isDeferredResult", "resolveDeferredData", "then", "resolveFetcherDeferredResults", "revalidatingFetchers", "key", "controller", "invariant", "unwrap", "aborted", "deferredData", "resolveData", "type", "ResultType", "data", "unwrappedData", "e", "error", "hasNakedIndexQuery", "search", "URLSearchParams", "getAll", "some", "v", "getTargetMatch", "location", "parsePath", "pathMatches", "getPathContributingMatches", "getSubmissionFromNavigation", "navigation", "formMethod", "formAction", "formEncType", "text", "formData", "json", "getLoadingNavigation", "submission", "state", "getSubmittingNavigation", "getLoadingFetcher", "fetcher", "getSubmittingFetcher", "existingFetcher", "getDoneFetcher", "restoreAppliedTransitions", "_window", "transitions", "sessionPositions", "sessionStorage", "getItem", "TRANSITIONS_STORAGE_KEY", "JSON", "parse", "k", "Array", "isArray", "set", "Set", "persistAppliedTransitions", "size", "setItem", "stringify", "warning", "DataRouterContext", "createContext", "process", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "isDataRoute", "RouteErrorContext", "useHref", "to", "_temp", "relative", "useInRouterContext", "invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "decodePath", "navigateEffectWarning", "useIsomorphicLayoutEffect", "cb", "isStatic", "static", "React", "useLayoutEffect", "useNavigate", "useNavigateStable", "useNavigateUnstable", "dataRouterContext", "future", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getResolveToMatches", "v7_relativeSplatPath", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "createElement", "Provider", "value", "useParams", "routeMatch", "length", "params", "_temp2", "useRoutes", "routes", "locationArg", "useRoutesImpl", "dataRouterState", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "pathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "parentSegments", "split", "segments", "slice", "join", "matchRoutes", "element", "undefined", "Component", "lazy", "renderedMatches", "_renderMatches", "map", "match", "Object", "assign", "encodeLocation", "_extends", "key", "NavigationType", "Pop", "DefaultErrorComponent", "error", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "console", "Fragment", "style", "fontStyle", "defaultErrorElement", "RenderErrorBoundary", "constructor", "props", "revalidation", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "children", "component", "RenderedRoute", "_ref", "staticContext", "errorElement", "Error<PERSON>ou<PERSON><PERSON>", "_deepestRenderedBoundaryId", "id", "_dataRouterState", "_future", "errors", "v7_partialHydration", "initialized", "errorIndex", "findIndex", "m", "keys", "Math", "min", "renderFallback", "fallbackIndex", "i", "HydrateFallback", "hydrateFallbackElement", "loaderData", "needsToRunLoader", "loader", "reduceRight", "index", "shouldRenderHydrateFallback", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useRouteId", "UseRouteId", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "useMatches", "UseMatches", "convertRouteMatchToUiMatch", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "actionData", "_state$errors", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "set<PERSON><PERSON>er<PERSON>ey", "useState", "blockerFunction", "arg", "currentLocation", "nextLocation", "historyAction", "stripBasename", "useEffect", "String", "deleteBlocker", "get<PERSON><PERSON>er", "blockers", "has", "get", "IDLE_BLOCKER", "UseNavigateStable", "fromRouteId", "alreadyWarned", "cond", "START_TRANSITION", "startTransitionImpl", "RouterProvider", "fallbackElement", "setStateImpl", "v7_startTransition", "setState", "newState", "subscribe", "n", "opts", "preventScrollReset", "Router", "DataRoutes", "_ref2", "MemoryRouter", "_ref3", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "action", "listen", "Navigate", "_ref4", "jsonPath", "Outlet", "Route", "_props", "_ref5", "basenameProp", "locationProp", "staticProp", "navigationContext", "locationContext", "trailingPathname", "Routes", "_ref6", "createRoutesFromChildren", "Await", "_ref7", "resolve", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ResolveAwait", "AwaitRenderStatus", "neverSettledPromise", "Promise", "promise", "pending", "success", "defineProperty", "renderError", "reject", "catch", "_tracked", "then", "data", "Aborted<PERSON>eferredError", "_ref8", "to<PERSON><PERSON>", "Children", "for<PERSON>ach", "isValidElement", "treePath", "type", "apply", "name", "caseSensitive", "hasErrorBou<PERSON>ry", "shouldRevalidate", "handle", "renderMatches", "mapRouteProperties", "updates", "createMemoryRouter", "createRouter", "v7_prependBasename", "hydrationData", "unstable_dataStrategy", "unstable_patchRoutesOnNavigation", "initialize"]}