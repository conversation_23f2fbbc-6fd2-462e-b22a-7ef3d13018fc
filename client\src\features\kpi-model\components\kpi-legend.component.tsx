import React, { useContext } from 'react';
import { Box, Typography, Chip, Stack } from '@mui/material';
import { fetchMechanismOptions, KPI_TYPE_COLORS, KPI_TYPE_LABELS } from '@/shared/enum/common.enum';
import { KpiModelContext } from '@/core/contexts/kpimodel.context';

export default function KpiLegend() {
  const { modelData } = useContext(KpiModelContext);

  var fetchMechanismLabel = fetchMechanismOptions.find(
    (x) => x.value === modelData?.override_fetch_mechanism_type,
  )?.label;

  const initialValues = {
    fetch_mechanism_label: fetchMechanismLabel || 'default',
    pull_from_date: modelData?.pull_from_date ? new Date(modelData.pull_from_date) : null,
    pull_to_date: modelData?.pull_to_date ? new Date(modelData.pull_to_date) : null,
  };

  return (
    <Box sx={{ mb: 0, p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
      <Stack direction="row" spacing={2} flexWrap="wrap" justifyContent="space-between" alignItems="center">
        <Typography variant="subtitle2">
          Fetch Mechanism:{' '}
          {initialValues.fetch_mechanism_label !== 'Custom'
            ? initialValues.fetch_mechanism_label +
              ' (' +
              initialValues.pull_from_date?.toLocaleDateString() +
              ' to ' +
              initialValues.pull_to_date?.toLocaleDateString() +
              ')'
            : initialValues.fetch_mechanism_label}
        </Typography>
        <Stack direction="row" spacing={2} flexWrap="wrap">
          {Object.entries(KPI_TYPE_COLORS).map(([type, color]) => (
            <Box key={type} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                sx={{
                  width: 18,
                  height: 18,
                  backgroundColor: color,
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 0.5,
                }}
              />
              <Typography variant="body2">{KPI_TYPE_LABELS[type as keyof typeof KPI_TYPE_LABELS]}</Typography>
            </Box>
          ))}
        </Stack>
      </Stack>
    </Box>
  );
}
