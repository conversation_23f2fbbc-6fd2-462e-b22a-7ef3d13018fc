{"version": 3, "sources": ["../../@mui/material/TableRow/TableRow.js", "../../@mui/material/TableRow/tableRowClasses.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\", \"hover\", \"selected\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport Tablelvl2Context from '../Table/Tablelvl2Context';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport tableRowClasses, { getTableRowUtilityClass } from './tableRowClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    selected,\n    hover,\n    head,\n    footer\n  } = ownerState;\n  const slots = {\n    root: ['root', selected && 'selected', hover && 'hover', head && 'head', footer && 'footer']\n  };\n  return composeClasses(slots, getTableRowUtilityClass, classes);\n};\nconst TableRowRoot = styled('tr', {\n  name: 'MuiTableRow',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.head && styles.head, ownerState.footer && styles.footer];\n  }\n})(({\n  theme\n}) => ({\n  color: 'inherit',\n  display: 'table-row',\n  verticalAlign: 'middle',\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0,\n  [`&.${tableRowClasses.hover}:hover`]: {\n    backgroundColor: (theme.vars || theme).palette.action.hover\n  },\n  [`&.${tableRowClasses.selected}`]: {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),\n    '&:hover': {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity)\n    }\n  }\n}));\nconst defaultComponent = 'tr';\n/**\n * Will automatically set dynamic row height\n * based on the material table element parent (head, body, etc).\n */\nconst TableRow = /*#__PURE__*/React.forwardRef(function TableRow(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTableRow'\n  });\n  const {\n      className,\n      component = defaultComponent,\n      hover = false,\n      selected = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const tablelvl2 = React.useContext(Tablelvl2Context);\n  const ownerState = _extends({}, props, {\n    component,\n    hover,\n    selected,\n    head: tablelvl2 && tablelvl2.variant === 'head',\n    footer: tablelvl2 && tablelvl2.variant === 'footer'\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TableRowRoot, _extends({\n    as: component,\n    ref: ref,\n    className: clsx(classes.root, className),\n    role: component === defaultComponent ? null : 'row',\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TableRow.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Should be valid `<tr>` children such as `TableCell`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the table row will shade on hover.\n   * @default false\n   */\n  hover: PropTypes.bool,\n  /**\n   * If `true`, the table row will have the selected shading.\n   * @default false\n   */\n  selected: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TableRow;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableRowUtilityClass(slot) {\n  return generateUtilityClass('MuiTableRow', slot);\n}\nconst tableRowClasses = generateUtilityClasses('MuiTableRow', ['root', 'selected', 'hover', 'head', 'footer']);\nexport default tableRowClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AAGA,YAAuB;AACvB,wBAAsB;AAGtB,8BAAsB;;;ACPf,SAAS,wBAAwB,MAAM;AAC5C,SAAO,qBAAqB,eAAe,IAAI;AACjD;AACA,IAAM,kBAAkB,uBAAuB,eAAe,CAAC,QAAQ,YAAY,SAAS,QAAQ,QAAQ,CAAC;AAC7G,IAAO,0BAAQ;;;ADQf,yBAA4B;AAV5B,IAAM,YAAY,CAAC,aAAa,aAAa,SAAS,UAAU;AAWhE,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,YAAY,YAAY,SAAS,SAAS,QAAQ,QAAQ,UAAU,QAAQ;AAAA,EAC7F;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACA,IAAM,eAAe,eAAO,MAAM;AAAA,EAChC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,WAAW,QAAQ,OAAO,MAAM,WAAW,UAAU,OAAO,MAAM;AAAA,EACzF;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,OAAO;AAAA,EACP,SAAS;AAAA,EACT,eAAe;AAAA;AAAA,EAEf,SAAS;AAAA,EACT,CAAC,KAAK,wBAAgB,KAAK,QAAQ,GAAG;AAAA,IACpC,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EACxD;AAAA,EACA,CAAC,KAAK,wBAAgB,QAAQ,EAAE,GAAG;AAAA,IACjC,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,eAAe,UAAM,+BAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,eAAe;AAAA,IACvM,WAAW;AAAA,MACT,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,WAAW,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,WAAO,+BAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,kBAAkB,MAAM,QAAQ,OAAO,YAAY;AAAA,IAC/R;AAAA,EACF;AACF,EAAE;AACF,IAAM,mBAAmB;AAKzB,IAAM,WAA8B,iBAAW,SAASA,UAAS,SAAS,KAAK;AAC7E,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,EACb,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,YAAkB,iBAAW,wBAAgB;AACnD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,aAAa,UAAU,YAAY;AAAA,IACzC,QAAQ,aAAa,UAAU,YAAY;AAAA,EAC7C,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,cAAc,SAAS;AAAA,IAC9C,IAAI;AAAA,IACJ;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC,MAAM,cAAc,mBAAmB,OAAO;AAAA,IAC9C;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,OAAwC,SAAS,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,mBAAQ;", "names": ["TableRow", "_jsx", "PropTypes"]}