{"version": 3, "sources": ["../../@azure/msal-browser/src/telemetry/BrowserPerformanceMeasurement.ts"], "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n\nimport { IPerformanceMeasurement, SubMeasurement } from \"@azure/msal-common\";\n\nexport class BrowserPerformanceMeasurement implements IPerformanceMeasurement {\n    private readonly measureName: string;\n    private readonly correlationId: string;\n    private readonly startMark: string;\n    private readonly endMark: string;\n\n    constructor(name: string, correlationId: string) {\n        this.correlationId = correlationId;\n        this.measureName = BrowserPerformanceMeasurement.makeMeasureName(\n            name,\n            correlationId\n        );\n        this.startMark = BrowserPerformanceMeasurement.makeStartMark(\n            name,\n            correlationId\n        );\n        this.endMark = BrowserPerformanceMeasurement.makeEndMark(\n            name,\n            correlationId\n        );\n    }\n\n    private static makeMeasureName(name: string, correlationId: string) {\n        return `msal.measure.${name}.${correlationId}`;\n    }\n\n    private static makeStartMark(name: string, correlationId: string) {\n        return `msal.start.${name}.${correlationId}`;\n    }\n\n    private static makeEndMark(name: string, correlationId: string) {\n        return `msal.end.${name}.${correlationId}`;\n    }\n\n    static supportsBrowserPerformance(): boolean {\n        return (\n            typeof window !== \"undefined\" &&\n            typeof window.performance !== \"undefined\" &&\n            typeof window.performance.mark === \"function\" &&\n            typeof window.performance.measure === \"function\" &&\n            typeof window.performance.clearMarks === \"function\" &&\n            typeof window.performance.clearMeasures === \"function\" &&\n            typeof window.performance.getEntriesByName === \"function\"\n        );\n    }\n\n    /**\n     * Flush browser marks and measurements.\n     * @param {string} correlationId\n     * @param {SubMeasurement} measurements\n     */\n    public static flushMeasurements(\n        correlationId: string,\n        measurements: SubMeasurement[]\n    ): void {\n        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\n            try {\n                measurements.forEach((measurement) => {\n                    const measureName =\n                        BrowserPerformanceMeasurement.makeMeasureName(\n                            measurement.name,\n                            correlationId\n                        );\n                    const entriesForMeasurement =\n                        window.performance.getEntriesByName(\n                            measureName,\n                            \"measure\"\n                        );\n                    if (entriesForMeasurement.length > 0) {\n                        window.performance.clearMeasures(measureName);\n                        window.performance.clearMarks(\n                            BrowserPerformanceMeasurement.makeStartMark(\n                                measureName,\n                                correlationId\n                            )\n                        );\n                        window.performance.clearMarks(\n                            BrowserPerformanceMeasurement.makeEndMark(\n                                measureName,\n                                correlationId\n                            )\n                        );\n                    }\n                });\n            } catch (e) {\n                // Silently catch and return null\n            }\n        }\n    }\n\n    startMeasurement(): void {\n        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\n            try {\n                window.performance.mark(this.startMark);\n            } catch (e) {\n                // Silently catch\n            }\n        }\n    }\n\n    endMeasurement(): void {\n        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\n            try {\n                window.performance.mark(this.endMark);\n                window.performance.measure(\n                    this.measureName,\n                    this.startMark,\n                    this.endMark\n                );\n            } catch (e) {\n                // Silently catch\n            }\n        }\n    }\n\n    flushMeasurement(): number | null {\n        if (BrowserPerformanceMeasurement.supportsBrowserPerformance()) {\n            try {\n                const entriesForMeasurement =\n                    window.performance.getEntriesByName(\n                        this.measureName,\n                        \"measure\"\n                    );\n                if (entriesForMeasurement.length > 0) {\n                    const durationMs = entriesForMeasurement[0].duration;\n                    window.performance.clearMeasures(this.measureName);\n                    window.performance.clearMarks(this.startMark);\n                    window.performance.clearMarks(this.endMark);\n                    return durationMs;\n                }\n            } catch (e) {\n                // Silently catch and return null\n            }\n        }\n        return null;\n    }\n}\n"], "mappings": ";IAOa,sCAAA,+BAA6B;EAMtC,YAAY,MAAc,eAAqB;AAC3C,SAAK,gBAAgB;AACrB,SAAK,cAAc,+BAA8B,gBAC7C,MACA,aAAa;AAEjB,SAAK,YAAY,+BAA8B,cAC3C,MACA,aAAa;AAEjB,SAAK,UAAU,+BAA8B,YACzC,MACA,aAAa;;EAIb,OAAO,gBAAgB,MAAc,eAAqB;AAC9D,WAAO,gBAAgB,IAAI,IAAI,aAAa;;EAGxC,OAAO,cAAc,MAAc,eAAqB;AAC5D,WAAO,cAAc,IAAI,IAAI,aAAa;;EAGtC,OAAO,YAAY,MAAc,eAAqB;AAC1D,WAAO,YAAY,IAAI,IAAI,aAAa;;EAG5C,OAAO,6BAA0B;AAC7B,WACI,OAAO,WAAW,eAClB,OAAO,OAAO,gBAAgB,eAC9B,OAAO,OAAO,YAAY,SAAS,cACnC,OAAO,OAAO,YAAY,YAAY,cACtC,OAAO,OAAO,YAAY,eAAe,cACzC,OAAO,OAAO,YAAY,kBAAkB,cAC5C,OAAO,OAAO,YAAY,qBAAqB;;;;;;;EAShD,OAAO,kBACV,eACA,cAA8B;AAE9B,QAAI,+BAA8B,2BAA0B,GAAI;AAC5D,UAAI;AACA,qBAAa,QAAQ,CAAC,gBAAe;AACjC,gBAAM,cACF,+BAA8B,gBAC1B,YAAY,MACZ,aAAa;AAErB,gBAAM,wBACF,OAAO,YAAY,iBACf,aACA,SAAS;AAEjB,cAAI,sBAAsB,SAAS,GAAG;AAClC,mBAAO,YAAY,cAAc,WAAW;AAC5C,mBAAO,YAAY,WACf,+BAA8B,cAC1B,aACA,aAAa,CAChB;AAEL,mBAAO,YAAY,WACf,+BAA8B,YAC1B,aACA,aAAa,CAChB;UAER;QACL,CAAC;MACJ,SAAQ,GAAG;MAEX;IACJ;;EAGL,mBAAgB;AACZ,QAAI,+BAA8B,2BAA0B,GAAI;AAC5D,UAAI;AACA,eAAO,YAAY,KAAK,KAAK,SAAS;MACzC,SAAQ,GAAG;MAEX;IACJ;;EAGL,iBAAc;AACV,QAAI,+BAA8B,2BAA0B,GAAI;AAC5D,UAAI;AACA,eAAO,YAAY,KAAK,KAAK,OAAO;AACpC,eAAO,YAAY,QACf,KAAK,aACL,KAAK,WACL,KAAK,OAAO;MAEnB,SAAQ,GAAG;MAEX;IACJ;;EAGL,mBAAgB;AACZ,QAAI,+BAA8B,2BAA0B,GAAI;AAC5D,UAAI;AACA,cAAM,wBACF,OAAO,YAAY,iBACf,KAAK,aACL,SAAS;AAEjB,YAAI,sBAAsB,SAAS,GAAG;AAClC,gBAAM,aAAa,sBAAsB,CAAC,EAAE;AAC5C,iBAAO,YAAY,cAAc,KAAK,WAAW;AACjD,iBAAO,YAAY,WAAW,KAAK,SAAS;AAC5C,iBAAO,YAAY,WAAW,KAAK,OAAO;AAC1C,iBAAO;QACV;MACJ,SAAQ,GAAG;MAEX;IACJ;AACD,WAAO;;AAEd;", "names": []}