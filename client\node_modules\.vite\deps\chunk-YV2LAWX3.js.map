{"version": 3, "sources": ["../../@mui/system/esm/RtlProvider/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RtlContext = /*#__PURE__*/React.createContext();\nfunction RtlProvider(_ref) {\n  let {\n      value\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  return /*#__PURE__*/_jsx(RtlContext.Provider, _extends({\n    value: value != null ? value : true\n  }, props));\n}\nprocess.env.NODE_ENV !== \"production\" ? RtlProvider.propTypes = {\n  children: PropTypes.node,\n  value: PropTypes.bool\n} : void 0;\nexport const useRtl = () => {\n  const value = React.useContext(RtlContext);\n  return value != null ? value : false;\n};\nexport default RtlProvider;"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAGA,YAAuB;AACvB,wBAAsB;AACtB,yBAA4B;AAH5B,IAAM,YAAY,CAAC,OAAO;AAI1B,IAAM,aAAgC,oBAAc;AACpD,SAAS,YAAY,MAAM;AACzB,MAAI;AAAA,IACA;AAAA,EACF,IAAI,MACJ,QAAQ,8BAA8B,MAAM,SAAS;AACvD,aAAoB,mBAAAA,KAAK,WAAW,UAAU,SAAS;AAAA,IACrD,OAAO,SAAS,OAAO,QAAQ;AAAA,EACjC,GAAG,KAAK,CAAC;AACX;AACA,OAAwC,YAAY,YAAY;AAAA,EAC9D,UAAU,kBAAAC,QAAU;AAAA,EACpB,OAAO,kBAAAA,QAAU;AACnB,IAAI;AACG,IAAM,SAAS,MAAM;AAC1B,QAAM,QAAc,iBAAW,UAAU;AACzC,SAAO,SAAS,OAAO,QAAQ;AACjC;AACA,IAAO,sBAAQ;", "names": ["_jsx", "PropTypes"]}