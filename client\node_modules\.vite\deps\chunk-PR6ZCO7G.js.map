{"version": 3, "sources": ["../../@mui/material/FormControl/formControlState.js"], "sourcesContent": ["export default function formControlState({\n  props,\n  states,\n  muiFormControl\n}) {\n  return states.reduce((acc, state) => {\n    acc[state] = props[state];\n    if (muiFormControl) {\n      if (typeof props[state] === 'undefined') {\n        acc[state] = muiFormControl[state];\n      }\n    }\n    return acc;\n  }, {});\n}"], "mappings": ";AAAe,SAAR,iBAAkC;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,OAAO,OAAO,CAAC,KAAK,UAAU;AACnC,QAAI,KAAK,IAAI,MAAM,KAAK;AACxB,QAAI,gBAAgB;AAClB,UAAI,OAAO,MAAM,KAAK,MAAM,aAAa;AACvC,YAAI,KAAK,IAAI,eAAe,KAAK;AAAA,MACnC;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;", "names": []}