import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/material/ButtonGroup/ButtonGroupContext.js
var React = __toESM(require_react());
var ButtonGroupContext = React.createContext({});
if (true) {
  ButtonGroupContext.displayName = "ButtonGroupContext";
}
var ButtonGroupContext_default = ButtonGroupContext;

// node_modules/@mui/material/ButtonGroup/ButtonGroupButtonContext.js
var React2 = __toESM(require_react());
var ButtonGroupButtonContext = React2.createContext(void 0);
if (true) {
  ButtonGroupButtonContext.displayName = "ButtonGroupButtonContext";
}
var ButtonGroupButtonContext_default = ButtonGroupButtonContext;

export {
  ButtonGroupContext_default,
  ButtonGroupButtonContext_default
};
//# sourceMappingURL=chunk-6B45ZDZL.js.map
