{"version": 3, "sources": ["../../@mui/utils/esm/getValidReactChildren/getValidReactChildren.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Gets only the valid children of a component,\n * and ignores any nullish or falsy child.\n *\n * @param children the children\n */\nexport default function getValidReactChildren(children) {\n  return React.Children.toArray(children).filter(child => /*#__PURE__*/React.isValidElement(child));\n}"], "mappings": ";;;;;;;;AAAA,YAAuB;AAQR,SAAR,sBAAuC,UAAU;AACtD,SAAa,eAAS,QAAQ,QAAQ,EAAE,OAAO,WAA4B,qBAAe,KAAK,CAAC;AAClG;", "names": []}